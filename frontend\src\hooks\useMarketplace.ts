import { useState, useEffect, useCallback } from 'react'
import { MarketplaceService, MarketplaceTemplate, MarketplaceFilters, MarketplaceSearchResult } from '../services/marketplaceService'
import toast from 'react-hot-toast'

interface UseMarketplaceReturn {
  // Estado
  templates: MarketplaceTemplate[]
  featuredTemplates: MarketplaceTemplate[]
  categories: string[]
  currentTemplate: MarketplaceTemplate | null
  searchResult: MarketplaceSearchResult | null
  isLoading: boolean
  isSearching: boolean
  
  // Filtros
  filters: MarketplaceFilters
  setFilters: (filters: MarketplaceFilters) => void
  
  // Ações
  searchTemplates: (filters?: MarketplaceFilters) => Promise<void>
  getTemplate: (templateId: string) => Promise<void>
  downloadTemplate: (templateId: string) => Promise<void>
  toggleLike: (templateId: string) => Promise<void>
  toggleFavorite: (templateId: string) => Promise<void>
  addReview: (templateId: string, review: { rating: number; comment?: string }) => Promise<void>
  publishTemplate: (templateId: string, data: { category: string; tags: string[]; deviceTypes: string[] }) => Promise<void>
  unpublishTemplate: (templateId: string) => Promise<void>
  
  // Utilitários
  loadFeaturedTemplates: () => Promise<void>
  loadCategories: () => Promise<void>
  clearCurrentTemplate: () => void
  resetFilters: () => void
}

const defaultFilters: MarketplaceFilters = {
  page: 1,
  limit: 20,
  sortBy: 'popular'
}

export const useMarketplace = (): UseMarketplaceReturn => {
  // Estado principal
  const [templates, setTemplates] = useState<MarketplaceTemplate[]>([])
  const [featuredTemplates, setFeaturedTemplates] = useState<MarketplaceTemplate[]>([])
  const [categories, setCategories] = useState<string[]>([])
  const [currentTemplate, setCurrentTemplate] = useState<MarketplaceTemplate | null>(null)
  const [searchResult, setSearchResult] = useState<MarketplaceSearchResult | null>(null)
  
  // Estado de carregamento
  const [isLoading, setIsLoading] = useState(false)
  const [isSearching, setIsSearching] = useState(false)
  
  // Filtros
  const [filters, setFilters] = useState<MarketplaceFilters>(defaultFilters)

  // Função para buscar templates
  const searchTemplates = useCallback(async (customFilters?: MarketplaceFilters) => {
    try {
      setIsSearching(true)
      const searchFilters = customFilters || filters
      const result = await MarketplaceService.searchTemplates(searchFilters)
      
      setSearchResult(result)
      setTemplates(result.templates)
    } catch (error) {
      console.error('Erro ao buscar templates:', error)
      toast.error('Erro ao buscar templates')
    } finally {
      setIsSearching(false)
    }
  }, [filters])

  // Função para obter template específico
  const getTemplate = useCallback(async (templateId: string) => {
    try {
      setIsLoading(true)
      const template = await MarketplaceService.getTemplate(templateId)
      setCurrentTemplate(template)
    } catch (error) {
      console.error('Erro ao obter template:', error)
      toast.error('Erro ao carregar template')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Função para fazer download de template
  const downloadTemplate = useCallback(async (templateId: string) => {
    try {
      setIsLoading(true)
      await MarketplaceService.downloadTemplate(templateId)
      toast.success('Template baixado com sucesso!')
      
      // Atualizar estatísticas do template atual se for o mesmo
      if (currentTemplate?.id === templateId) {
        await getTemplate(templateId)
      }
    } catch (error) {
      console.error('Erro ao baixar template:', error)
      toast.error('Erro ao baixar template')
    } finally {
      setIsLoading(false)
    }
  }, [currentTemplate, getTemplate])

  // Função para curtir/descurtir template
  const toggleLike = useCallback(async (templateId: string) => {
    try {
      const result = await MarketplaceService.toggleLike(templateId)
      
      if (result.liked) {
        toast.success('Template curtido!')
      } else {
        toast.success('Like removido')
      }
      
      // Atualizar template atual se for o mesmo
      if (currentTemplate?.id === templateId) {
        await getTemplate(templateId)
      }
      
      // Atualizar lista de templates
      setTemplates(prev => prev.map(template => 
        template.id === templateId 
          ? { 
              ...template, 
              likes: result.liked ? template.likes + 1 : template.likes - 1,
              userInteraction: { 
                ...template.userInteraction, 
                hasLiked: result.liked 
              }
            }
          : template
      ))
    } catch (error) {
      console.error('Erro ao curtir template:', error)
      toast.error('Erro ao curtir template')
    }
  }, [currentTemplate, getTemplate])

  // Função para favoritar/desfavoritar template
  const toggleFavorite = useCallback(async (templateId: string) => {
    try {
      const result = await MarketplaceService.toggleFavorite(templateId)
      
      if (result.favorited) {
        toast.success('Template adicionado aos favoritos!')
      } else {
        toast.success('Template removido dos favoritos')
      }
      
      // Atualizar template atual se for o mesmo
      if (currentTemplate?.id === templateId) {
        await getTemplate(templateId)
      }
      
      // Atualizar lista de templates
      setTemplates(prev => prev.map(template => 
        template.id === templateId 
          ? { 
              ...template, 
              userInteraction: { 
                ...template.userInteraction, 
                hasFavorited: result.favorited 
              }
            }
          : template
      ))
    } catch (error) {
      console.error('Erro ao favoritar template:', error)
      toast.error('Erro ao favoritar template')
    }
  }, [currentTemplate, getTemplate])

  // Função para adicionar review
  const addReview = useCallback(async (templateId: string, review: { rating: number; comment?: string }) => {
    try {
      setIsLoading(true)
      await MarketplaceService.addReview(templateId, review)
      toast.success('Review adicionado com sucesso!')
      
      // Atualizar template atual se for o mesmo
      if (currentTemplate?.id === templateId) {
        await getTemplate(templateId)
      }
    } catch (error) {
      console.error('Erro ao adicionar review:', error)
      toast.error('Erro ao adicionar review')
    } finally {
      setIsLoading(false)
    }
  }, [currentTemplate, getTemplate])

  // Função para publicar template
  const publishTemplate = useCallback(async (templateId: string, data: { category: string; tags: string[]; deviceTypes: string[] }) => {
    try {
      setIsLoading(true)
      
      // Validar dados
      const validation = MarketplaceService.validatePublishData(data)
      if (!validation.isValid) {
        validation.errors.forEach(error => toast.error(error))
        return
      }
      
      await MarketplaceService.publishTemplate(templateId, data)
      toast.success('Template publicado no marketplace!')
    } catch (error) {
      console.error('Erro ao publicar template:', error)
      toast.error('Erro ao publicar template')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Função para despublicar template
  const unpublishTemplate = useCallback(async (templateId: string) => {
    try {
      setIsLoading(true)
      await MarketplaceService.unpublishTemplate(templateId)
      toast.success('Template removido do marketplace')
    } catch (error) {
      console.error('Erro ao remover template do marketplace:', error)
      toast.error('Erro ao remover template do marketplace')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Função para carregar templates em destaque
  const loadFeaturedTemplates = useCallback(async () => {
    try {
      const featured = await MarketplaceService.getFeaturedTemplates()
      setFeaturedTemplates(featured)
    } catch (error) {
      console.error('Erro ao carregar templates em destaque:', error)
    }
  }, [])

  // Função para carregar categorias
  const loadCategories = useCallback(async () => {
    try {
      const cats = await MarketplaceService.getCategories()
      setCategories(cats)
    } catch (error) {
      console.error('Erro ao carregar categorias:', error)
    }
  }, [])

  // Função para limpar template atual
  const clearCurrentTemplate = useCallback(() => {
    setCurrentTemplate(null)
  }, [])

  // Função para resetar filtros
  const resetFilters = useCallback(() => {
    setFilters(defaultFilters)
  }, [])

  // Carregar dados iniciais
  useEffect(() => {
    loadFeaturedTemplates()
    loadCategories()
  }, [loadFeaturedTemplates, loadCategories])

  // Buscar templates quando filtros mudarem
  useEffect(() => {
    if (filters !== defaultFilters) {
      searchTemplates()
    }
  }, [filters, searchTemplates])

  return {
    // Estado
    templates,
    featuredTemplates,
    categories,
    currentTemplate,
    searchResult,
    isLoading,
    isSearching,
    
    // Filtros
    filters,
    setFilters,
    
    // Ações
    searchTemplates,
    getTemplate,
    downloadTemplate,
    toggleLike,
    toggleFavorite,
    addReview,
    publishTemplate,
    unpublishTemplate,
    
    // Utilitários
    loadFeaturedTemplates,
    loadCategories,
    clearCurrentTemplate,
    resetFilters
  }
}

export default useMarketplace

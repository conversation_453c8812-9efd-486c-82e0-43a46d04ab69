import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Card,
  CardContent,
  CardActions,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Grid,
  Paper,
  Divider
} from '@mui/material';
import {
  Security as SecurityIcon,
  QrCode as QrCodeIcon,
  Smartphone as PhoneIcon,
  Key as KeyIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';
import { useTwoFactorAuth } from '../hooks/useTwoFactorAuth';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const TwoFactorAuth: React.FC = () => {
  const {
    settings,
    isLoading,
    isSetupMode,
    setupData,
    generateSetup,
    enableTwoFactor,
    disableTwoFactor,
    regenerateBackupCodes,
    removeTrustedDevice
  } = useTwoFactorAuth();

  const [activeStep, setActiveStep] = useState(0);
  const [verificationToken, setVerificationToken] = useState('');
  const [password, setPassword] = useState('');
  const [showDisableDialog, setShowDisableDialog] = useState(false);
  const [showBackupCodesDialog, setShowBackupCodesDialog] = useState(false);
  const [newBackupCodes, setNewBackupCodes] = useState<string[]>([]);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleStartSetup = async () => {
    try {
      setError('');
      await generateSetup();
      setActiveStep(0);
    } catch (error) {
      setError('Erro ao iniciar configuração do 2FA');
    }
  };

  const handleVerifySetup = async () => {
    if (!verificationToken.trim()) {
      setError('Digite o código de verificação');
      return;
    }

    try {
      setError('');
      const success = await enableTwoFactor(verificationToken);
      
      if (success) {
        setSuccess('2FA habilitado com sucesso!');
        setVerificationToken('');
        setActiveStep(0);
      } else {
        setError('Código de verificação inválido');
      }
    } catch (error) {
      setError('Erro ao verificar código');
    }
  };

  const handleDisable2FA = async () => {
    if (!password.trim()) {
      setError('Digite sua senha');
      return;
    }

    try {
      setError('');
      const success = await disableTwoFactor(password);
      
      if (success) {
        setSuccess('2FA desabilitado com sucesso');
        setShowDisableDialog(false);
        setPassword('');
      } else {
        setError('Senha incorreta');
      }
    } catch (error) {
      setError('Erro ao desabilitar 2FA');
    }
  };

  const handleRegenerateBackupCodes = async () => {
    if (!password.trim()) {
      setError('Digite sua senha');
      return;
    }

    try {
      setError('');
      const codes = await regenerateBackupCodes(password);
      setNewBackupCodes(codes);
      setShowBackupCodesDialog(true);
      setPassword('');
    } catch (error) {
      setError('Erro ao regenerar códigos de backup');
    }
  };

  const handleDownloadBackupCodes = () => {
    const content = newBackupCodes.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'backup-codes-2fa.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const steps = [
    {
      label: 'Escaneie o QR Code',
      content: (
        <Box>
          <Typography variant="body2" gutterBottom>
            Use um aplicativo autenticador como Google Authenticator, Authy ou Microsoft Authenticator para escanear o QR code:
          </Typography>
          {setupData?.qrCode && (
            <Box sx={{ textAlign: 'center', my: 3 }}>
              <img 
                src={setupData.qrCode} 
                alt="QR Code para 2FA" 
                style={{ maxWidth: 200, height: 'auto' }}
              />
            </Box>
          )}
          <Alert severity="info" sx={{ mt: 2 }}>
            Se não conseguir escanear o QR code, digite manualmente este código no seu aplicativo:
            <Typography variant="body2" fontFamily="monospace" sx={{ mt: 1 }}>
              {setupData?.secret}
            </Typography>
          </Alert>
        </Box>
      )
    },
    {
      label: 'Verifique o código',
      content: (
        <Box>
          <Typography variant="body2" gutterBottom>
            Digite o código de 6 dígitos gerado pelo seu aplicativo autenticador:
          </Typography>
          <TextField
            fullWidth
            label="Código de verificação"
            value={verificationToken}
            onChange={(e) => setVerificationToken(e.target.value.replace(/\D/g, '').slice(0, 6))}
            placeholder="123456"
            inputProps={{ maxLength: 6 }}
            sx={{ mt: 2 }}
          />
        </Box>
      )
    }
  ];

  return (
    <Container maxWidth="md">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Autenticação de Dois Fatores
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Adicione uma camada extra de segurança à sua conta
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Status do 2FA */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <SecurityIcon color={settings?.isEnabled ? 'success' : 'action'} />
                <Box>
                  <Typography variant="h6">
                    Status da Autenticação de Dois Fatores
                  </Typography>
                  <Chip
                    label={settings?.isEnabled ? 'Habilitado' : 'Desabilitado'}
                    color={settings?.isEnabled ? 'success' : 'default'}
                    icon={settings?.isEnabled ? <CheckIcon /> : <WarningIcon />}
                  />
                </Box>
              </Box>

              {settings?.isEnabled && (
                <Box>
                  <Typography variant="body2" color="text.secondary">
                    Última utilização: {settings.lastUsed 
                      ? format(settings.lastUsed, 'dd/MM/yyyy HH:mm', { locale: ptBR })
                      : 'Nunca'
                    }
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Códigos de backup restantes: {settings.backupCodesRemaining}
                  </Typography>
                </Box>
              )}
            </CardContent>

            <CardActions>
              {!settings?.isEnabled ? (
                <Button
                  variant="contained"
                  startIcon={<SecurityIcon />}
                  onClick={handleStartSetup}
                  disabled={isLoading}
                >
                  Habilitar 2FA
                </Button>
              ) : (
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<WarningIcon />}
                  onClick={() => setShowDisableDialog(true)}
                  disabled={isLoading}
                >
                  Desabilitar 2FA
                </Button>
              )}
            </CardActions>
          </Card>
        </Grid>

        {/* Setup do 2FA */}
        {isSetupMode && setupData && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Configurar Autenticação de Dois Fatores
                </Typography>

                <Stepper activeStep={activeStep} orientation="vertical">
                  {steps.map((step, index) => (
                    <Step key={step.label}>
                      <StepLabel>{step.label}</StepLabel>
                      <StepContent>
                        {step.content}
                        <Box sx={{ mt: 2 }}>
                          {index === 0 && (
                            <Button
                              variant="contained"
                              onClick={() => setActiveStep(1)}
                            >
                              Próximo
                            </Button>
                          )}
                          {index === 1 && (
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Button
                                variant="contained"
                                onClick={handleVerifySetup}
                                disabled={!verificationToken || verificationToken.length !== 6}
                              >
                                Verificar e Habilitar
                              </Button>
                              <Button onClick={() => setActiveStep(0)}>
                                Voltar
                              </Button>
                            </Box>
                          )}
                        </Box>
                      </StepContent>
                    </Step>
                  ))}
                </Stepper>

                {/* Códigos de backup */}
                {setupData.backupCodes && setupData.backupCodes.length > 0 && (
                  <Box sx={{ mt: 3 }}>
                    <Alert severity="warning">
                      <Typography variant="subtitle2" gutterBottom>
                        Códigos de Backup
                      </Typography>
                      <Typography variant="body2" gutterBottom>
                        Guarde estes códigos em local seguro. Você pode usá-los para acessar sua conta se perder acesso ao seu dispositivo:
                      </Typography>
                      <Paper sx={{ p: 2, mt: 2, backgroundColor: 'grey.50' }}>
                        <Grid container spacing={1}>
                          {setupData.backupCodes.map((code, index) => (
                            <Grid item xs={6} key={index}>
                              <Typography variant="body2" fontFamily="monospace">
                                {code}
                              </Typography>
                            </Grid>
                          ))}
                        </Grid>
                      </Paper>
                    </Alert>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Códigos de backup */}
        {settings?.isEnabled && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Códigos de Backup
                </Typography>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Use estes códigos para acessar sua conta se perder acesso ao seu dispositivo autenticador.
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Códigos restantes: {settings.backupCodesRemaining}
                </Typography>
              </CardContent>
              <CardActions>
                <Button
                  startIcon={<RefreshIcon />}
                  onClick={() => setShowBackupCodesDialog(true)}
                >
                  Regenerar Códigos
                </Button>
              </CardActions>
            </Card>
          </Grid>
        )}

        {/* Dispositivos confiáveis */}
        {settings?.isEnabled && settings.trustedDevices.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Dispositivos Confiáveis
                </Typography>
                <List>
                  {settings.trustedDevices.map((device) => (
                    <ListItem key={device.id} divider>
                      <ListItemText
                        primary={device.name}
                        secondary={
                          <Box>
                            <Typography variant="caption" display="block">
                              Último acesso: {format(device.lastUsed, 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                            </Typography>
                            <Typography variant="caption" display="block">
                              IP: {device.ipAddress}
                            </Typography>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={() => removeTrustedDevice(device.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>

      {/* Dialog para desabilitar 2FA */}
      <Dialog open={showDisableDialog} onClose={() => setShowDisableDialog(false)}>
        <DialogTitle>Desabilitar Autenticação de Dois Fatores</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Desabilitar o 2FA reduzirá a segurança da sua conta. Tem certeza que deseja continuar?
          </Alert>
          <TextField
            fullWidth
            type="password"
            label="Senha atual"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDisableDialog(false)}>
            Cancelar
          </Button>
          <Button
            onClick={handleDisable2FA}
            color="error"
            variant="contained"
            disabled={!password.trim()}
          >
            Desabilitar 2FA
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para regenerar códigos de backup */}
      <Dialog open={showBackupCodesDialog} onClose={() => setShowBackupCodesDialog(false)}>
        <DialogTitle>
          {newBackupCodes.length > 0 ? 'Novos Códigos de Backup' : 'Regenerar Códigos de Backup'}
        </DialogTitle>
        <DialogContent>
          {newBackupCodes.length > 0 ? (
            <Box>
              <Alert severity="warning" sx={{ mb: 2 }}>
                Guarde estes novos códigos em local seguro. Os códigos anteriores não funcionarão mais.
              </Alert>
              <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                <Grid container spacing={1}>
                  {newBackupCodes.map((code, index) => (
                    <Grid item xs={6} key={index}>
                      <Typography variant="body2" fontFamily="monospace">
                        {code}
                      </Typography>
                    </Grid>
                  ))}
                </Grid>
              </Paper>
            </Box>
          ) : (
            <Box>
              <Alert severity="info" sx={{ mb: 2 }}>
                Isso irá gerar novos códigos de backup e invalidar os códigos existentes.
              </Alert>
              <TextField
                fullWidth
                type="password"
                label="Senha atual"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setShowBackupCodesDialog(false);
            setNewBackupCodes([]);
            setPassword('');
          }}>
            {newBackupCodes.length > 0 ? 'Fechar' : 'Cancelar'}
          </Button>
          {newBackupCodes.length > 0 ? (
            <Button
              onClick={handleDownloadBackupCodes}
              startIcon={<DownloadIcon />}
              variant="contained"
            >
              Baixar Códigos
            </Button>
          ) : (
            <Button
              onClick={handleRegenerateBackupCodes}
              variant="contained"
              disabled={!password.trim()}
            >
              Regenerar
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default TwoFactorAuth;

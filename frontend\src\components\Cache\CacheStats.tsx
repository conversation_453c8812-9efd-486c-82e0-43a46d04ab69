import React from 'react'
import { 
  Database, 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  HardDrive, 
  Zap,
  Clock,
  BarChart3
} from 'lucide-react'
import { CacheStats as CacheStatsType, CacheMetrics } from '../../services/cacheService'

interface CacheStatsProps {
  stats: CacheStatsType | null
  metrics: CacheMetrics | null
  redisHealth: any
  isLoading?: boolean
}

const CacheStats: React.FC<CacheStatsProps> = ({ 
  stats, 
  metrics, 
  redisHealth, 
  isLoading = false 
}) => {
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatPercentage = (value: number): string => {
    return (value * 100).toFixed(1) + '%'
  }

  const formatNumber = (value: number): string => {
    return value.toLocaleString('pt-BR')
  }

  const getStatusColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value >= thresholds.good) return 'text-green-600 bg-green-100'
    if (value >= thresholds.warning) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
              <div className="w-16 h-4 bg-gray-200 rounded"></div>
            </div>
            <div className="mt-4">
              <div className="w-20 h-8 bg-gray-200 rounded"></div>
              <div className="w-24 h-4 bg-gray-200 rounded mt-2"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  const hitRate = stats?.hitRate || 0
  const memoryUsage = stats?.memoryUsage || 0
  const totalKeys = stats?.totalKeys || 0
  const evictions = stats?.evictions || 0

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Hit Rate */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${getStatusColor(hitRate, { good: 0.8, warning: 0.6 })}`}>
              <TrendingUp className="h-6 w-6" />
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {formatPercentage(hitRate)}
            </div>
            <div className="text-sm text-gray-500">Hit Rate</div>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex justify-between text-sm text-gray-600">
            <span>Hits: {formatNumber(stats?.totalHits || 0)}</span>
            <span>Misses: {formatNumber(stats?.totalMisses || 0)}</span>
          </div>
        </div>
      </div>

      {/* Uso de Memória */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
              <HardDrive className="h-6 w-6" />
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {formatBytes(memoryUsage)}
            </div>
            <div className="text-sm text-gray-500">Memória Usada</div>
          </div>
        </div>
        {redisHealth?.maxMemory && (
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ 
                  width: `${Math.min((memoryUsage / redisHealth.maxMemory) * 100, 100)}%` 
                }}
              ></div>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {formatBytes(redisHealth.maxMemory)} total
            </div>
          </div>
        )}
      </div>

      {/* Total de Chaves */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-purple-100 text-purple-600">
              <Database className="h-6 w-6" />
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {formatNumber(totalKeys)}
            </div>
            <div className="text-sm text-gray-500">Chaves Ativas</div>
          </div>
        </div>
        <div className="mt-4">
          <div className="text-sm text-gray-600">
            Evictions: {formatNumber(evictions)}
          </div>
        </div>
      </div>

      {/* Tempo de Resposta Médio */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-green-100 text-green-600">
              <Clock className="h-6 w-6" />
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {metrics?.averageResponseTime ? `${metrics.averageResponseTime.toFixed(0)}ms` : 'N/A'}
            </div>
            <div className="text-sm text-gray-500">Tempo Médio</div>
          </div>
        </div>
        <div className="mt-4">
          <div className="text-sm text-gray-600">
            Resposta do Cache
          </div>
        </div>
      </div>

      {/* Status do Redis */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${redisHealth?.connected ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}>
              <Activity className="h-6 w-6" />
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {redisHealth?.connected ? 'Online' : 'Offline'}
            </div>
            <div className="text-sm text-gray-500">Status Redis</div>
          </div>
        </div>
        <div className="mt-4">
          <div className="text-sm text-gray-600">
            {redisHealth?.version && `v${redisHealth.version}`}
          </div>
        </div>
      </div>

      {/* Clientes Conectados */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-orange-100 text-orange-600">
              <BarChart3 className="h-6 w-6" />
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {redisHealth?.connectedClients || 0}
            </div>
            <div className="text-sm text-gray-500">Clientes</div>
          </div>
        </div>
        <div className="mt-4">
          <div className="text-sm text-gray-600">
            Conexões Ativas
          </div>
        </div>
      </div>

      {/* Taxa de Miss */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${getStatusColor(1 - (metrics?.missRate || 0), { good: 0.8, warning: 0.6 })}`}>
              <TrendingDown className="h-6 w-6" />
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {formatPercentage(metrics?.missRate || 0)}
            </div>
            <div className="text-sm text-gray-500">Miss Rate</div>
          </div>
        </div>
        <div className="mt-4">
          <div className="text-sm text-gray-600">
            Taxa de Falhas
          </div>
        </div>
      </div>

      {/* Uptime */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="p-2 rounded-lg bg-indigo-100 text-indigo-600">
              <Zap className="h-6 w-6" />
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {redisHealth?.uptime ? `${Math.floor(redisHealth.uptime / 86400)}d` : 'N/A'}
            </div>
            <div className="text-sm text-gray-500">Uptime</div>
          </div>
        </div>
        <div className="mt-4">
          <div className="text-sm text-gray-600">
            {redisHealth?.uptime ? `${Math.floor((redisHealth.uptime % 86400) / 3600)}h ${Math.floor((redisHealth.uptime % 3600) / 60)}m` : 'Tempo Online'}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CacheStats

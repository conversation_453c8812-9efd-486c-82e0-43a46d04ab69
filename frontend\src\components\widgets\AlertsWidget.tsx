import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
  Alert
} from '@mui/material';
import {
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  CheckCircle as ResolvedIcon,
  Refresh as RefreshIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { DashboardWidget } from '../../hooks/useDashboard';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface SystemAlert {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  timestamp: Date;
  resolved: boolean;
  source?: string;
}

interface AlertsWidgetProps {
  widget: DashboardWidget;
}

const AlertsWidget: React.FC<AlertsWidgetProps> = ({ widget }) => {
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(false);

  // Mock data - em produção viria da API
  const mockAlerts: SystemAlert[] = [
    {
      id: '1',
      title: 'Alta utilização de CPU',
      description: 'Servidor Router Principal com CPU acima de 80%',
      severity: 'high',
      category: 'Performance',
      timestamp: new Date(Date.now() - 300000),
      resolved: false,
      source: 'Router Principal'
    },
    {
      id: '2',
      title: 'Conexão instável',
      description: 'Múltiplas tentativas de reconexão detectadas',
      severity: 'medium',
      category: 'Conectividade',
      timestamp: new Date(Date.now() - 600000),
      resolved: false,
      source: 'OLT Fibra'
    },
    {
      id: '3',
      title: 'Backup falhou',
      description: 'Backup automático não foi concluído',
      severity: 'medium',
      category: 'Sistema',
      timestamp: new Date(Date.now() - 1800000),
      resolved: false
    },
    {
      id: '4',
      title: 'Servidor offline',
      description: 'Backup Router não responde há mais de 1 hora',
      severity: 'critical',
      category: 'Conectividade',
      timestamp: new Date(Date.now() - 3600000),
      resolved: false,
      source: 'Backup Router'
    },
    {
      id: '5',
      title: 'Memória baixa',
      description: 'Switch Core com menos de 10% de memória livre',
      severity: 'low',
      category: 'Performance',
      timestamp: new Date(Date.now() - 7200000),
      resolved: true,
      source: 'Switch Core'
    }
  ];

  useEffect(() => {
    loadAlerts();
    
    const interval = setInterval(() => {
      loadAlerts();
    }, widget.refreshInterval || 15000);

    return () => clearInterval(interval);
  }, [widget.refreshInterval]);

  const loadAlerts = async () => {
    setLoading(true);
    try {
      // Simular carregamento
      await new Promise(resolve => setTimeout(resolve, 300));
      
      let filteredAlerts = mockAlerts;
      
      // Aplicar filtros baseados na configuração do widget
      if (widget.config.severity && widget.config.severity !== 'all') {
        filteredAlerts = filteredAlerts.filter(alert => alert.severity === widget.config.severity);
      }
      
      // Mostrar apenas alertas não resolvidos por padrão
      if (!widget.config.showResolved) {
        filteredAlerts = filteredAlerts.filter(alert => !alert.resolved);
      }
      
      // Ordenar por severidade e timestamp
      filteredAlerts.sort((a, b) => {
        const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
        if (severityDiff !== 0) return severityDiff;
        return b.timestamp.getTime() - a.timestamp.getTime();
      });
      
      if (widget.config.maxItems) {
        filteredAlerts = filteredAlerts.slice(0, widget.config.maxItems);
      }
      
      setAlerts(filteredAlerts);
    } catch (error) {
      console.error('Erro ao carregar alertas:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityIcon = (severity: SystemAlert['severity']) => {
    switch (severity) {
      case 'critical':
        return <ErrorIcon color="error" />;
      case 'high':
        return <WarningIcon color="error" />;
      case 'medium':
        return <WarningIcon color="warning" />;
      case 'low':
        return <InfoIcon color="info" />;
      default:
        return <InfoIcon />;
    }
  };

  const getSeverityColor = (severity: SystemAlert['severity']) => {
    switch (severity) {
      case 'critical':
        return 'error';
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'default';
    }
  };

  const getSeverityText = (severity: SystemAlert['severity']) => {
    switch (severity) {
      case 'critical':
        return 'Crítico';
      case 'high':
        return 'Alto';
      case 'medium':
        return 'Médio';
      case 'low':
        return 'Baixo';
      default:
        return 'Desconhecido';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Performance':
        return 'primary';
      case 'Conectividade':
        return 'secondary';
      case 'Sistema':
        return 'success';
      case 'Segurança':
        return 'error';
      default:
        return 'default';
    }
  };

  const activeAlerts = alerts.filter(alert => !alert.resolved);
  const criticalAlerts = activeAlerts.filter(alert => alert.severity === 'critical');
  const highAlerts = activeAlerts.filter(alert => alert.severity === 'high');

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Cabeçalho */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="subtitle2" color="text.secondary">
          {activeAlerts.length} alerta(s) ativo(s)
        </Typography>
        <Tooltip title="Atualizar">
          <IconButton size="small" onClick={loadAlerts} disabled={loading}>
            <RefreshIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Loading */}
      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {/* Resumo de alertas críticos */}
      {(criticalAlerts.length > 0 || highAlerts.length > 0) && (
        <Alert 
          severity={criticalAlerts.length > 0 ? 'error' : 'warning'} 
          sx={{ mb: 2 }}
        >
          <Typography variant="body2">
            {criticalAlerts.length > 0 && `${criticalAlerts.length} alerta(s) crítico(s)`}
            {criticalAlerts.length > 0 && highAlerts.length > 0 && ' • '}
            {highAlerts.length > 0 && `${highAlerts.length} alerta(s) de alta prioridade`}
          </Typography>
        </Alert>
      )}

      {/* Lista de alertas */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {alerts.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <CheckCircle sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              Nenhum alerta ativo
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Sistema funcionando normalmente
            </Typography>
          </Box>
        ) : (
          <List dense>
            {alerts.map((alert) => (
              <ListItem 
                key={alert.id} 
                divider
                sx={{ 
                  opacity: alert.resolved ? 0.6 : 1,
                  backgroundColor: alert.resolved ? 'transparent' : 
                    alert.severity === 'critical' ? 'error.light' :
                    alert.severity === 'high' ? 'warning.light' : 'transparent',
                  borderRadius: 1,
                  mb: 0.5
                }}
              >
                <ListItemIcon>
                  {alert.resolved ? <ResolvedIcon color="success" /> : getSeverityIcon(alert.severity)}
                </ListItemIcon>
                
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                      <Typography variant="body2" fontWeight="medium">
                        {alert.title}
                      </Typography>
                      <Chip
                        label={getSeverityText(alert.severity)}
                        size="small"
                        color={getSeverityColor(alert.severity) as any}
                        variant="outlined"
                      />
                      <Chip
                        label={alert.category}
                        size="small"
                        color={getCategoryColor(alert.category) as any}
                        variant="outlined"
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        {alert.description}
                      </Typography>
                      <br />
                      <Typography variant="caption" color="text.secondary">
                        {format(alert.timestamp, 'dd/MM HH:mm', { locale: ptBR })}
                        {alert.source && ` • ${alert.source}`}
                      </Typography>
                    </Box>
                  }
                />
                
                <ListItemSecondaryAction>
                  {!alert.resolved && (
                    <Tooltip title="Resolver alerta">
                      <IconButton 
                        size="small"
                        onClick={() => {
                          // Simular resolução do alerta
                          setAlerts(prev => prev.map(a => 
                            a.id === alert.id ? { ...a, resolved: true } : a
                          ));
                        }}
                      >
                        <CloseIcon fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        )}
      </Box>

      {/* Estatísticas resumidas */}
      <Box sx={{ mt: 'auto', pt: 2, borderTop: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-around', textAlign: 'center' }}>
          <Box>
            <Typography variant="h6" color="error.main">
              {alerts.filter(a => !a.resolved && a.severity === 'critical').length}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Críticos
            </Typography>
          </Box>
          <Box>
            <Typography variant="h6" color="warning.main">
              {alerts.filter(a => !a.resolved && (a.severity === 'high' || a.severity === 'medium')).length}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Importantes
            </Typography>
          </Box>
          <Box>
            <Typography variant="h6" color="success.main">
              {alerts.filter(a => a.resolved).length}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Resolvidos
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default AlertsWidget;

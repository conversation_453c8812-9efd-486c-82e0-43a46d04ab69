import React, { useState } from 'react'
import { X, ChevronDown, ChevronUp } from 'lucide-react'
import { MarketplaceFilters as Filters } from '../../services/marketplaceService'

interface MarketplaceFiltersProps {
  categories: string[]
  filters: Filters
  onFilterChange: (filters: Partial<Filters>) => void
  onReset: () => void
}

const deviceTypes = [
  'CISCO',
  'HUAWEI',
  'NOKIA',
  'JUNIPER',
  'MIKROTIK',
  'UBIQUITI',
  'FORTINET',
  'PFSENSE',
  'LINUX',
  'WINDOWS',
  'OTHER'
]

const MarketplaceFilters: React.FC<MarketplaceFiltersProps> = ({
  categories,
  filters,
  onFilterChange,
  onReset
}) => {
  const [expandedSections, setExpandedSections] = useState({
    category: true,
    tags: false,
    deviceTypes: false
  })

  const [tagInput, setTagInput] = useState('')
  const [selectedTags, setSelectedTags] = useState<string[]>(filters.tags || [])

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const handleCategoryChange = (category: string) => {
    const newCategory = filters.category === category ? undefined : category
    onFilterChange({ category: newCategory })
  }

  const handleDeviceTypeChange = (deviceType: string) => {
    const currentTypes = filters.deviceTypes || []
    const newTypes = currentTypes.includes(deviceType)
      ? currentTypes.filter(t => t !== deviceType)
      : [...currentTypes, deviceType]
    
    onFilterChange({ deviceTypes: newTypes.length > 0 ? newTypes : undefined })
  }

  const handleAddTag = () => {
    if (tagInput.trim() && !selectedTags.includes(tagInput.trim())) {
      const newTags = [...selectedTags, tagInput.trim()]
      setSelectedTags(newTags)
      onFilterChange({ tags: newTags })
      setTagInput('')
    }
  }

  const handleRemoveTag = (tag: string) => {
    const newTags = selectedTags.filter(t => t !== tag)
    setSelectedTags(newTags)
    onFilterChange({ tags: newTags.length > 0 ? newTags : undefined })
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddTag()
    }
  }

  const hasActiveFilters = !!(
    filters.category || 
    (filters.tags && filters.tags.length > 0) || 
    (filters.deviceTypes && filters.deviceTypes.length > 0)
  )

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Filtros</h3>
        {hasActiveFilters && (
          <button
            onClick={onReset}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            Limpar Filtros
          </button>
        )}
      </div>

      <div className="space-y-6">
        {/* Category Filter */}
        <div>
          <button
            onClick={() => toggleSection('category')}
            className="flex items-center justify-between w-full text-left"
          >
            <h4 className="text-sm font-medium text-gray-900">Categoria</h4>
            {expandedSections.category ? (
              <ChevronUp className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </button>
          
          {expandedSections.category && (
            <div className="mt-3 space-y-2">
              {categories.map((category) => (
                <label key={category} className="flex items-center">
                  <input
                    type="radio"
                    name="category"
                    checked={filters.category === category}
                    onChange={() => handleCategoryChange(category)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span className="ml-2 text-sm text-gray-700">{category}</span>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Tags Filter */}
        <div>
          <button
            onClick={() => toggleSection('tags')}
            className="flex items-center justify-between w-full text-left"
          >
            <h4 className="text-sm font-medium text-gray-900">Tags</h4>
            {expandedSections.tags ? (
              <ChevronUp className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </button>
          
          {expandedSections.tags && (
            <div className="mt-3">
              <div className="flex items-center space-x-2 mb-3">
                <input
                  type="text"
                  placeholder="Adicionar tag..."
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  onClick={handleAddTag}
                  disabled={!tagInput.trim()}
                  className="px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Adicionar
                </button>
              </div>
              
              {selectedTags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {selectedTags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {tag}
                      <button
                        onClick={() => handleRemoveTag(tag)}
                        className="ml-1 text-blue-600 hover:text-blue-800"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Device Types Filter */}
        <div>
          <button
            onClick={() => toggleSection('deviceTypes')}
            className="flex items-center justify-between w-full text-left"
          >
            <h4 className="text-sm font-medium text-gray-900">Tipos de Dispositivo</h4>
            {expandedSections.deviceTypes ? (
              <ChevronUp className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </button>
          
          {expandedSections.deviceTypes && (
            <div className="mt-3 grid grid-cols-2 gap-2">
              {deviceTypes.map((deviceType) => (
                <label key={deviceType} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={(filters.deviceTypes || []).includes(deviceType)}
                    onChange={() => handleDeviceTypeChange(deviceType)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{deviceType}</span>
                </label>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h5 className="text-sm font-medium text-gray-900 mb-3">Filtros Ativos</h5>
          <div className="space-y-2">
            {filters.category && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Categoria:</span>
                <span className="font-medium text-gray-900">{filters.category}</span>
              </div>
            )}
            
            {filters.tags && filters.tags.length > 0 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Tags:</span>
                <span className="font-medium text-gray-900">{filters.tags.length} selecionadas</span>
              </div>
            )}
            
            {filters.deviceTypes && filters.deviceTypes.length > 0 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Dispositivos:</span>
                <span className="font-medium text-gray-900">{filters.deviceTypes.length} selecionados</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default MarketplaceFilters

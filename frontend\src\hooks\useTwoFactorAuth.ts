import { useState, useEffect, useCallback } from 'react';

export interface TwoFactorSetup {
  secret: string;
  qrCode: string;
  backupCodes: string[];
  isEnabled: boolean;
}

export interface TwoFactorVerification {
  token: string;
  backupCode?: string;
}

export interface TwoFactorSettings {
  isEnabled: boolean;
  lastUsed?: Date;
  backupCodesRemaining: number;
  trustedDevices: Array<{
    id: string;
    name: string;
    lastUsed: Date;
    userAgent: string;
    ipAddress: string;
  }>;
}

interface UseTwoFactorAuthReturn {
  settings: TwoFactorSettings | null;
  isLoading: boolean;
  isSetupMode: boolean;
  setupData: TwoFactorSetup | null;
  generateSetup: () => Promise<TwoFactorSetup>;
  enableTwoFactor: (token: string) => Promise<boolean>;
  disableTwoFactor: (password: string, token?: string) => Promise<boolean>;
  verifyToken: (verification: TwoFactorVerification) => Promise<boolean>;
  regenerateBackupCodes: (password: string) => Promise<string[]>;
  removeTrustedDevice: (deviceId: string) => Promise<boolean>;
  getTrustedDevices: () => Promise<void>;
  checkTwoFactorRequired: (email: string) => Promise<boolean>;
}

const API_BASE = '/api/auth/2fa';

export const useTwoFactorAuth = (): UseTwoFactorAuthReturn => {
  const [settings, setSettings] = useState<TwoFactorSettings | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSetupMode, setIsSetupMode] = useState(false);
  const [setupData, setSetupData] = useState<TwoFactorSetup | null>(null);

  // Carregar configurações do 2FA
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE}/settings`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setSettings({
          ...data,
          lastUsed: data.lastUsed ? new Date(data.lastUsed) : undefined,
          trustedDevices: data.trustedDevices?.map((device: any) => ({
            ...device,
            lastUsed: new Date(device.lastUsed)
          })) || []
        });
      }
    } catch (error) {
      console.error('Erro ao carregar configurações 2FA:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const generateSetup = useCallback(async (): Promise<TwoFactorSetup> => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE}/setup`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('Erro ao gerar configuração 2FA');
      }
      
      const setup = await response.json();
      setSetupData(setup);
      setIsSetupMode(true);
      return setup;
    } catch (error) {
      console.error('Erro ao gerar setup 2FA:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const enableTwoFactor = useCallback(async (token: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE}/enable`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          token,
          secret: setupData?.secret 
        })
      });
      
      if (response.ok) {
        setIsSetupMode(false);
        setSetupData(null);
        await loadSettings();
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Erro ao habilitar 2FA:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [setupData, loadSettings]);

  const disableTwoFactor = useCallback(async (password: string, token?: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE}/disable`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ password, token })
      });
      
      if (response.ok) {
        await loadSettings();
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Erro ao desabilitar 2FA:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [loadSettings]);

  const verifyToken = useCallback(async (verification: TwoFactorVerification): Promise<boolean> => {
    try {
      const response = await fetch(`${API_BASE}/verify`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(verification)
      });
      
      return response.ok;
    } catch (error) {
      console.error('Erro ao verificar token 2FA:', error);
      return false;
    }
  }, []);

  const regenerateBackupCodes = useCallback(async (password: string): Promise<string[]> => {
    try {
      setIsLoading(true);
      const response = await fetch(`${API_BASE}/backup-codes/regenerate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ password })
      });
      
      if (response.ok) {
        const data = await response.json();
        await loadSettings();
        return data.backupCodes;
      }
      
      throw new Error('Erro ao regenerar códigos de backup');
    } catch (error) {
      console.error('Erro ao regenerar códigos de backup:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [loadSettings]);

  const removeTrustedDevice = useCallback(async (deviceId: string): Promise<boolean> => {
    try {
      const response = await fetch(`${API_BASE}/trusted-devices/${deviceId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        await loadSettings();
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Erro ao remover dispositivo confiável:', error);
      return false;
    }
  }, [loadSettings]);

  const getTrustedDevices = useCallback(async () => {
    await loadSettings();
  }, [loadSettings]);

  const checkTwoFactorRequired = useCallback(async (email: string): Promise<boolean> => {
    try {
      const response = await fetch(`${API_BASE}/required`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      });
      
      if (response.ok) {
        const data = await response.json();
        return data.required;
      }
      
      return false;
    } catch (error) {
      console.error('Erro ao verificar necessidade de 2FA:', error);
      return false;
    }
  }, []);

  return {
    settings,
    isLoading,
    isSetupMode,
    setupData,
    generateSetup,
    enableTwoFactor,
    disableTwoFactor,
    verifyToken,
    regenerateBackupCodes,
    removeTrustedDevice,
    getTrustedDevices,
    checkTwoFactorRequired
  };
};

// Hook para verificação de 2FA no login
export const useTwoFactorLogin = () => {
  const [isRequired, setIsRequired] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);

  const checkRequired = useCallback(async (email: string) => {
    try {
      const response = await fetch(`${API_BASE}/required`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email })
      });
      
      if (response.ok) {
        const data = await response.json();
        setIsRequired(data.required);
        return data.required;
      }
      
      return false;
    } catch (error) {
      console.error('Erro ao verificar 2FA:', error);
      return false;
    }
  }, []);

  const verifyLogin = useCallback(async (email: string, password: string, twoFactorToken?: string) => {
    try {
      setIsVerifying(true);
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          email, 
          password, 
          twoFactorToken 
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        return { success: true, data };
      } else {
        const error = await response.json();
        return { success: false, error: error.message };
      }
    } catch (error) {
      console.error('Erro no login com 2FA:', error);
      return { success: false, error: 'Erro interno do servidor' };
    } finally {
      setIsVerifying(false);
    }
  }, []);

  return {
    isRequired,
    isVerifying,
    checkRequired,
    verifyLogin
  };
};

export default useTwoFactorAuth;

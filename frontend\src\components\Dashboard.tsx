import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  <PERSON>ton,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Card,
  CardContent,
  CardHeader,
  CardActions,
  Fab,
  Tooltip,
  Chip,
  Alert
} from '@mui/material';
import {
  Edit as EditIcon,
  Add as AddIcon,
  MoreVert as MoreIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Dashboard as DashboardIcon,
  Widgets as WidgetsIcon,
  Settings as SettingsIcon,
  Download as ExportIcon,
  Upload as ImportIcon
} from '@mui/icons-material';
import { useDashboard, DashboardWidget } from '../hooks/useDashboard';
import { DragDropList, useDragDrop } from './DragDropList';
import { useResponsive } from '../hooks/useResponsive';

// Componentes de widgets
import StatusWidget from './widgets/StatusWidget';
import ServersWidget from './widgets/ServersWidget';
import AlertsWidget from './widgets/AlertsWidget';
import MetricsWidget from './widgets/MetricsWidget';
import ActivityWidget from './widgets/ActivityWidget';

interface DashboardProps {
  title?: string;
}

const Dashboard: React.FC<DashboardProps> = ({ title = 'Dashboard' }) => {
  const { isMobile } = useResponsive();
  const {
    layouts,
    currentLayout,
    widgets,
    isEditing,
    setEditing,
    createLayout,
    deleteLayout,
    switchLayout,
    updateLayout,
    addWidget,
    updateWidget,
    removeWidget,
    moveWidget,
    resizeWidget,
    resetLayout,
    exportLayout,
    importLayout
  } = useDashboard();

  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
  const [showLayoutDialog, setShowLayoutDialog] = useState(false);
  const [showWidgetDialog, setShowWidgetDialog] = useState(false);
  const [newLayoutName, setNewLayoutName] = useState('');
  const [newLayoutDescription, setNewLayoutDescription] = useState('');

  const { items: sortedWidgets, reorderItems } = useDragDrop(widgets);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
  };

  const handleCreateLayout = () => {
    if (newLayoutName.trim()) {
      createLayout(newLayoutName.trim(), newLayoutDescription.trim() || undefined);
      setNewLayoutName('');
      setNewLayoutDescription('');
      setShowLayoutDialog(false);
    }
  };

  const handleExportLayout = () => {
    if (currentLayout) {
      const data = exportLayout(currentLayout.id);
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `dashboard-${currentLayout.name.toLowerCase().replace(/\s+/g, '-')}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
    handleMenuClose();
  };

  const handleImportLayout = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (importLayout(content)) {
          alert('Layout importado com sucesso!');
        } else {
          alert('Erro ao importar layout. Verifique o formato do arquivo.');
        }
      };
      reader.readAsText(file);
    }
    handleMenuClose();
  };

  const renderWidget = (widget: DashboardWidget) => {
    const WidgetComponent = getWidgetComponent(widget.type);
    
    return (
      <Grid item xs={12} sm={6} md={widget.size.width} key={widget.id}>
        <Card 
          sx={{ 
            height: widget.size.height * 100,
            position: 'relative',
            opacity: widget.visible ? 1 : 0.5
          }}
        >
          <CardHeader
            title={widget.title}
            action={
              isEditing && (
                <Box>
                  <IconButton
                    size="small"
                    onClick={() => updateWidget(widget.id, { visible: !widget.visible })}
                  >
                    <SettingsIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    onClick={() => removeWidget(widget.id)}
                    color="error"
                  >
                    <CancelIcon />
                  </IconButton>
                </Box>
              )
            }
            sx={{ pb: 1 }}
          />
          <CardContent sx={{ pt: 0, height: 'calc(100% - 64px)', overflow: 'auto' }}>
            <WidgetComponent widget={widget} />
          </CardContent>
          
          {widget.refreshInterval && (
            <Chip
              label={`${widget.refreshInterval / 1000}s`}
              size="small"
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                opacity: 0.7
              }}
            />
          )}
        </Card>
      </Grid>
    );
  };

  const getWidgetComponent = (type: string) => {
    switch (type) {
      case 'status': return StatusWidget;
      case 'servers': return ServersWidget;
      case 'alerts': return AlertsWidget;
      case 'metrics': return MetricsWidget;
      case 'activity': return ActivityWidget;
      default: return () => <Typography>Widget não encontrado</Typography>;
    }
  };

  const availableWidgetTypes = [
    { type: 'status', name: 'Status do Sistema', description: 'Visão geral do sistema' },
    { type: 'servers', name: 'Servidores', description: 'Lista de servidores online/offline' },
    { type: 'alerts', name: 'Alertas', description: 'Alertas ativos do sistema' },
    { type: 'metrics', name: 'Métricas', description: 'Gráficos de performance' },
    { type: 'activity', name: 'Atividade', description: 'Atividade recente do sistema' }
  ];

  return (
    <Container maxWidth="xl">
      {/* Cabeçalho */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            {title}
          </Typography>
          {currentLayout && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Chip 
                icon={<DashboardIcon />}
                label={currentLayout.name}
                color="primary"
                variant="outlined"
              />
              {currentLayout.description && (
                <Typography variant="body2" color="text.secondary">
                  {currentLayout.description}
                </Typography>
              )}
            </Box>
          )}
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          {isEditing ? (
            <>
              <Button
                startIcon={<SaveIcon />}
                onClick={() => setEditing(false)}
                variant="contained"
                color="primary"
              >
                Salvar
              </Button>
              <Button
                startIcon={<CancelIcon />}
                onClick={() => setEditing(false)}
                variant="outlined"
              >
                Cancelar
              </Button>
            </>
          ) : (
            <>
              <Button
                startIcon={<EditIcon />}
                onClick={() => setEditing(true)}
                variant="outlined"
              >
                Editar
              </Button>
              <IconButton onClick={handleMenuOpen}>
                <MoreIcon />
              </IconButton>
            </>
          )}
        </Box>
      </Box>

      {/* Menu de opções */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => { setShowLayoutDialog(true); handleMenuClose(); }}>
          <DashboardIcon sx={{ mr: 1 }} />
          Novo Layout
        </MenuItem>
        <MenuItem onClick={handleExportLayout}>
          <ExportIcon sx={{ mr: 1 }} />
          Exportar Layout
        </MenuItem>
        <MenuItem component="label">
          <ImportIcon sx={{ mr: 1 }} />
          Importar Layout
          <input
            type="file"
            accept=".json"
            hidden
            onChange={handleImportLayout}
          />
        </MenuItem>
        <MenuItem onClick={() => { resetLayout(); handleMenuClose(); }}>
          <SettingsIcon sx={{ mr: 1 }} />
          Restaurar Padrão
        </MenuItem>
      </Menu>

      {/* Seletor de layouts */}
      {layouts.length > 1 && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Layouts Disponíveis
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {layouts.map(layout => (
              <Chip
                key={layout.id}
                label={layout.name}
                onClick={() => switchLayout(layout.id)}
                color={layout.id === currentLayout?.id ? 'primary' : 'default'}
                variant={layout.id === currentLayout?.id ? 'filled' : 'outlined'}
                onDelete={!layout.isDefault ? () => deleteLayout(layout.id) : undefined}
              />
            ))}
          </Box>
        </Box>
      )}

      {/* Widgets */}
      {widgets.length > 0 ? (
        <Grid container spacing={3}>
          {isEditing ? (
            <DragDropList
              items={sortedWidgets.map(widget => ({
                id: widget.id,
                content: renderWidget(widget),
                data: widget
              }))}
              onReorder={(newOrder) => {
                const reorderedWidgets = newOrder.map(item => item.data as DashboardWidget);
                reorderItems(reorderedWidgets);
              }}
              orientation="vertical"
              showActions={false}
            />
          ) : (
            widgets.filter(w => w.visible).map(renderWidget)
          )}
        </Grid>
      ) : (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Dashboard Vazio
          </Typography>
          <Typography>
            Adicione widgets para começar a personalizar seu dashboard.
          </Typography>
          <Button
            startIcon={<AddIcon />}
            onClick={() => setShowWidgetDialog(true)}
            sx={{ mt: 2 }}
          >
            Adicionar Widget
          </Button>
        </Alert>
      )}

      {/* FAB para adicionar widgets */}
      {isEditing && (
        <Fab
          color="primary"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={() => setShowWidgetDialog(true)}
        >
          <AddIcon />
        </Fab>
      )}

      {/* Dialog para criar layout */}
      <Dialog open={showLayoutDialog} onClose={() => setShowLayoutDialog(false)}>
        <DialogTitle>Criar Novo Layout</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Nome do Layout"
            fullWidth
            variant="outlined"
            value={newLayoutName}
            onChange={(e) => setNewLayoutName(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Descrição (opcional)"
            fullWidth
            variant="outlined"
            multiline
            rows={3}
            value={newLayoutDescription}
            onChange={(e) => setNewLayoutDescription(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowLayoutDialog(false)}>Cancelar</Button>
          <Button onClick={handleCreateLayout} variant="contained">
            Criar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para adicionar widget */}
      <Dialog open={showWidgetDialog} onClose={() => setShowWidgetDialog(false)}>
        <DialogTitle>Adicionar Widget</DialogTitle>
        <DialogContent>
          <Grid container spacing={2}>
            {availableWidgetTypes.map(widgetType => (
              <Grid item xs={12} sm={6} key={widgetType.type}>
                <Card 
                  sx={{ cursor: 'pointer' }}
                  onClick={() => {
                    addWidget({
                      type: widgetType.type as any,
                      title: widgetType.name,
                      position: { x: 0, y: 0 },
                      size: { width: 4, height: 2 },
                      config: {},
                      visible: true,
                      refreshInterval: 30000
                    });
                    setShowWidgetDialog(false);
                  }}
                >
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {widgetType.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {widgetType.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowWidgetDialog(false)}>Cancelar</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default Dashboard;

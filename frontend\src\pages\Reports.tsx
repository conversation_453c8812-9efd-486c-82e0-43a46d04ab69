import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  LinearProgress,
  Alert,
  Fab
} from '@mui/material';
import {
  Assessment as ReportIcon,
  Add as AddIcon,
  PlayArrow as GenerateIcon,
  Download as ExportIcon,
  Schedule as ScheduleIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  MoreVert as MoreIcon,
  Visibility as ViewIcon,
  DateRange as DateIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ptBR } from 'date-fns/locale';
import { useReports, ReportFilter, ReportTemplate } from '../hooks/useReports';
import { format } from 'date-fns';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
};

const Reports: React.FC = () => {
  const {
    reports,
    templates,
    isGenerating,
    generateReport,
    deleteReport,
    exportReport,
    scheduleReport,
    createTemplate,
    deleteTemplate
  } = useReports();

  const [activeTab, setActiveTab] = useState(0);
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);
  const [showTemplateDialog, setShowTemplateDialog] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<{ element: HTMLElement; reportId: string } | null>(null);
  
  // Filtros para geração de relatório
  const [filters, setFilters] = useState<ReportFilter>({
    dateRange: {
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      end: new Date()
    }
  });

  // Novo template
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    description: '',
    type: 'usage' as ReportTemplate['type']
  });

  const handleGenerateReport = async () => {
    if (!selectedTemplate) return;
    
    try {
      await generateReport(selectedTemplate.id, filters);
      setShowGenerateDialog(false);
      setSelectedTemplate(null);
    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
    }
  };

  const handleExportReport = async (reportId: string, format: 'pdf' | 'excel' | 'csv') => {
    try {
      await exportReport(reportId, format);
      setMenuAnchor(null);
    } catch (error) {
      console.error('Erro ao exportar relatório:', error);
    }
  };

  const handleCreateTemplate = () => {
    if (newTemplate.name.trim()) {
      createTemplate({
        name: newTemplate.name,
        description: newTemplate.description,
        type: newTemplate.type,
        defaultFilters: {},
        fields: []
      });
      setNewTemplate({ name: '', description: '', type: 'usage' });
      setShowTemplateDialog(false);
    }
  };

  const getTypeColor = (type: ReportTemplate['type']) => {
    switch (type) {
      case 'usage': return 'primary';
      case 'performance': return 'secondary';
      case 'errors': return 'error';
      case 'security': return 'warning';
      default: return 'default';
    }
  };

  const getTypeLabel = (type: ReportTemplate['type']) => {
    switch (type) {
      case 'usage': return 'Uso';
      case 'performance': return 'Performance';
      case 'errors': return 'Erros';
      case 'security': return 'Segurança';
      default: return 'Personalizado';
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
      <Container maxWidth="xl">
        {/* Cabeçalho */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Relatórios e Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Gere relatórios detalhados sobre o uso e performance do sistema
          </Typography>
        </Box>

        {/* Loading global */}
        {isGenerating && (
          <Box sx={{ mb: 3 }}>
            <Alert severity="info">
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography>Gerando relatório...</Typography>
                <LinearProgress sx={{ flexGrow: 1 }} />
              </Box>
            </Alert>
          </Box>
        )}

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
            <Tab label="Templates" icon={<ReportIcon />} />
            <Tab label={`Relatórios (${reports.length})`} icon={<ViewIcon />} />
          </Tabs>
        </Box>

        {/* Aba Templates */}
        <TabPanel value={activeTab} index={0}>
          <Grid container spacing={3}>
            {templates.map((template) => (
              <Grid item xs={12} sm={6} md={4} key={template.id}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                      <Typography variant="h6" sx={{ flexGrow: 1 }}>
                        {template.name}
                      </Typography>
                      <Chip
                        label={getTypeLabel(template.type)}
                        color={getTypeColor(template.type) as any}
                        size="small"
                      />
                    </Box>
                    
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {template.description}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      {template.fields.slice(0, 3).map((field) => (
                        <Chip
                          key={field}
                          label={field.replace(/_/g, ' ')}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                      {template.fields.length > 3 && (
                        <Chip
                          label={`+${template.fields.length - 3}`}
                          size="small"
                          variant="outlined"
                        />
                      )}
                    </Box>
                    
                    {template.schedule?.enabled && (
                      <Box sx={{ mt: 2 }}>
                        <Chip
                          icon={<ScheduleIcon />}
                          label={`Agendado ${template.schedule.frequency}`}
                          color="info"
                          size="small"
                        />
                      </Box>
                    )}
                  </CardContent>
                  
                  <CardActions>
                    <Button
                      startIcon={<GenerateIcon />}
                      onClick={() => {
                        setSelectedTemplate(template);
                        setFilters({
                          ...filters,
                          ...template.defaultFilters
                        });
                        setShowGenerateDialog(true);
                      }}
                      disabled={isGenerating}
                    >
                      Gerar
                    </Button>
                    <IconButton
                      onClick={() => {
                        // Implementar edição de template
                      }}
                    >
                      <EditIcon />
                    </IconButton>
                    {!templates.slice(0, 4).includes(template) && (
                      <IconButton
                        onClick={() => deleteTemplate(template.id)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    )}
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Aba Relatórios */}
        <TabPanel value={activeTab} index={1}>
          {reports.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <ReportIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Nenhum relatório gerado
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Use os templates para gerar seus primeiros relatórios
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setActiveTab(0)}
              >
                Ver Templates
              </Button>
            </Box>
          ) : (
            <List>
              {reports.map((report) => (
                <ListItem key={report.id} divider>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1">
                          {report.name}
                        </Typography>
                        <Chip
                          label={getTypeLabel(report.type)}
                          color={getTypeColor(report.type) as any}
                          size="small"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {report.description}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Gerado em {format(report.generatedAt, 'dd/MM/yyyy HH:mm')} • 
                          Período: {format(report.filters.dateRange.start, 'dd/MM')} - {format(report.filters.dateRange.end, 'dd/MM')}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      onClick={(e) => setMenuAnchor({ element: e.currentTarget, reportId: report.id })}
                    >
                      <MoreIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </TabPanel>

        {/* FAB para criar template */}
        <Fab
          color="primary"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={() => setShowTemplateDialog(true)}
        >
          <AddIcon />
        </Fab>

        {/* Menu de ações do relatório */}
        <Menu
          anchorEl={menuAnchor?.element}
          open={Boolean(menuAnchor)}
          onClose={() => setMenuAnchor(null)}
        >
          <MenuItem onClick={() => handleExportReport(menuAnchor!.reportId, 'pdf')}>
            <ExportIcon sx={{ mr: 1 }} />
            Exportar PDF
          </MenuItem>
          <MenuItem onClick={() => handleExportReport(menuAnchor!.reportId, 'excel')}>
            <ExportIcon sx={{ mr: 1 }} />
            Exportar Excel
          </MenuItem>
          <MenuItem onClick={() => handleExportReport(menuAnchor!.reportId, 'csv')}>
            <ExportIcon sx={{ mr: 1 }} />
            Exportar CSV
          </MenuItem>
          <MenuItem
            onClick={() => {
              deleteReport(menuAnchor!.reportId);
              setMenuAnchor(null);
            }}
            sx={{ color: 'error.main' }}
          >
            <DeleteIcon sx={{ mr: 1 }} />
            Excluir
          </MenuItem>
        </Menu>

        {/* Dialog para gerar relatório */}
        <Dialog open={showGenerateDialog} onClose={() => setShowGenerateDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            Gerar Relatório: {selectedTemplate?.name}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <DatePicker
                    label="Data Inicial"
                    value={filters.dateRange.start}
                    onChange={(date) => date && setFilters(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, start: date }
                    }))}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <DatePicker
                    label="Data Final"
                    value={filters.dateRange.end}
                    onChange={(date) => date && setFilters(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, end: date }
                    }))}
                    renderInput={(params) => <TextField {...params} fullWidth />}
                  />
                </Grid>
              </Grid>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowGenerateDialog(false)}>
              Cancelar
            </Button>
            <Button
              onClick={handleGenerateReport}
              variant="contained"
              disabled={isGenerating}
              startIcon={<GenerateIcon />}
            >
              {isGenerating ? 'Gerando...' : 'Gerar Relatório'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Dialog para criar template */}
        <Dialog open={showTemplateDialog} onClose={() => setShowTemplateDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Criar Novo Template</DialogTitle>
          <DialogContent>
            <Box sx={{ pt: 2 }}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    label="Nome do Template"
                    fullWidth
                    value={newTemplate.name}
                    onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    label="Descrição"
                    fullWidth
                    multiline
                    rows={3}
                    value={newTemplate.description}
                    onChange={(e) => setNewTemplate(prev => ({ ...prev, description: e.target.value }))}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Tipo</InputLabel>
                    <Select
                      value={newTemplate.type}
                      onChange={(e) => setNewTemplate(prev => ({ ...prev, type: e.target.value as any }))}
                    >
                      <MenuItem value="usage">Uso</MenuItem>
                      <MenuItem value="performance">Performance</MenuItem>
                      <MenuItem value="errors">Erros</MenuItem>
                      <MenuItem value="security">Segurança</MenuItem>
                      <MenuItem value="custom">Personalizado</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowTemplateDialog(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreateTemplate} variant="contained">
              Criar Template
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </LocalizationProvider>
  );
};

export default Reports;

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Pagination,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ptBR } from 'date-fns/locale';
import { format } from 'date-fns';
import api from '../services/api';

interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  oldValues?: any;
  newValues?: any;
  ipAddress?: string;
  userAgent?: string;
  timestamp: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
}

interface AuditStatistics {
  totalActions: number;
  actionsByType: { action: string; count: number }[];
  actionsByUser: { userId: string; userName: string; count: number }[];
  actionsByResource: { resource: string; count: number }[];
  actionsOverTime: { date: string; count: number }[];
}

const Audit: React.FC = () => {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [statistics, setStatistics] = useState<AuditStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    action: '',
    resource: '',
    userId: '',
    startDate: null as Date | null,
    endDate: null as Date | null
  });

  const limit = 20;

  const actionLabels: { [key: string]: string } = {
    'user.login': 'Login de Usuário',
    'user.logout': 'Logout de Usuário',
    'user.create': 'Criação de Usuário',
    'user.update': 'Atualização de Usuário',
    'user.delete': 'Exclusão de Usuário',
    'server.create': 'Criação de Servidor',
    'server.update': 'Atualização de Servidor',
    'server.delete': 'Exclusão de Servidor',
    'server.connect': 'Conexão com Servidor',
    'command.execute': 'Execução de Comando',
    'command.create': 'Criação de Comando',
    'command.update': 'Atualização de Comando',
    'command.delete': 'Exclusão de Comando'
  };

  const resourceLabels: { [key: string]: string } = {
    user: 'Usuário',
    server: 'Servidor',
    command: 'Comando',
    template: 'Template',
    group: 'Grupo',
    system: 'Sistema',
    alert: 'Alerta'
  };

  const getActionColor = (action: string) => {
    if (action.includes('create')) return 'success';
    if (action.includes('update')) return 'info';
    if (action.includes('delete')) return 'error';
    if (action.includes('login')) return 'primary';
    if (action.includes('execute')) return 'warning';
    return 'default';
  };

  const fetchLogs = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: ((page - 1) * limit).toString()
      });

      if (filters.action) params.append('action', filters.action);
      if (filters.resource) params.append('resource', filters.resource);
      if (filters.userId) params.append('userId', filters.userId);
      if (filters.startDate) params.append('startDate', filters.startDate.toISOString());
      if (filters.endDate) params.append('endDate', filters.endDate.toISOString());

      const response = await api.get(`/audit/logs?${params}`);
      
      if (response.data.success) {
        setLogs(response.data.data.logs);
        setTotalPages(Math.ceil(response.data.data.total / limit));
      } else {
        setError('Erro ao carregar logs de auditoria');
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Erro ao carregar logs de auditoria');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await api.get('/audit/statistics?days=30');
      
      if (response.data.success) {
        setStatistics(response.data.data);
      }
    } catch (err) {
      console.error('Erro ao carregar estatísticas:', err);
    }
  };

  useEffect(() => {
    fetchLogs();
    fetchStatistics();
  }, [page]);

  const handleFilterChange = (field: string, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  const handleSearch = () => {
    setPage(1);
    fetchLogs();
  };

  const handleClearFilters = () => {
    setFilters({
      action: '',
      resource: '',
      userId: '',
      startDate: null,
      endDate: null
    });
    setPage(1);
  };

  const formatTimestamp = (timestamp: string) => {
    return format(new Date(timestamp), 'dd/MM/yyyy HH:mm:ss', { locale: ptBR });
  };

  const renderValuesDiff = (oldValues: any, newValues: any) => {
    if (!oldValues && !newValues) return null;

    return (
      <Box sx={{ mt: 1 }}>
        {oldValues && (
          <Typography variant="caption" color="text.secondary">
            <strong>Valores anteriores:</strong> {JSON.stringify(oldValues, null, 2)}
          </Typography>
        )}
        {newValues && (
          <Typography variant="caption" color="text.secondary">
            <strong>Novos valores:</strong> {JSON.stringify(newValues, null, 2)}
          </Typography>
        )}
      </Box>
    );
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Auditoria do Sistema
        </Typography>

        {/* Estatísticas */}
        {statistics && (
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Total de Ações (30 dias)
                  </Typography>
                  <Typography variant="h5">
                    {statistics.totalActions}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Ação Mais Comum
                  </Typography>
                  <Typography variant="h6">
                    {statistics.actionsByType[0]?.action || 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {statistics.actionsByType[0]?.count || 0} vezes
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Usuário Mais Ativo
                  </Typography>
                  <Typography variant="h6">
                    {statistics.actionsByUser[0]?.userName || 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {statistics.actionsByUser[0]?.count || 0} ações
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Recurso Mais Usado
                  </Typography>
                  <Typography variant="h6">
                    {resourceLabels[statistics.actionsByResource[0]?.resource] || 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {statistics.actionsByResource[0]?.count || 0} ações
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )}

        {/* Filtros */}
        <Accordion sx={{ mb: 3 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography>Filtros de Pesquisa</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Ação</InputLabel>
                  <Select
                    value={filters.action}
                    onChange={(e) => handleFilterChange('action', e.target.value)}
                  >
                    <MenuItem value="">Todas</MenuItem>
                    {Object.entries(actionLabels).map(([key, label]) => (
                      <MenuItem key={key} value={key}>{label}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Recurso</InputLabel>
                  <Select
                    value={filters.resource}
                    onChange={(e) => handleFilterChange('resource', e.target.value)}
                  >
                    <MenuItem value="">Todos</MenuItem>
                    {Object.entries(resourceLabels).map(([key, label]) => (
                      <MenuItem key={key} value={key}>{label}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Data Inicial"
                  value={filters.startDate}
                  onChange={(date) => handleFilterChange('startDate', date)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <DatePicker
                  label="Data Final"
                  value={filters.endDate}
                  onChange={(date) => handleFilterChange('endDate', date)}
                  renderInput={(params) => <TextField {...params} fullWidth />}
                />
              </Grid>
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    startIcon={<SearchIcon />}
                    onClick={handleSearch}
                  >
                    Pesquisar
                  </Button>
                  <Button
                    variant="outlined"
                    onClick={handleClearFilters}
                  >
                    Limpar Filtros
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={() => {
                      fetchLogs();
                      fetchStatistics();
                    }}
                  >
                    Atualizar
                  </Button>
                </Box>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Tabela de Logs */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Logs de Auditoria
            </Typography>
            
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Data/Hora</TableCell>
                        <TableCell>Usuário</TableCell>
                        <TableCell>Ação</TableCell>
                        <TableCell>Recurso</TableCell>
                        <TableCell>IP</TableCell>
                        <TableCell>Detalhes</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {logs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell>
                            {formatTimestamp(log.timestamp)}
                          </TableCell>
                          <TableCell>
                            {log.user?.name || 'Usuário não encontrado'}
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={actionLabels[log.action] || log.action}
                              color={getActionColor(log.action) as any}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {resourceLabels[log.resource] || log.resource}
                            <Typography variant="caption" display="block">
                              ID: {log.resourceId}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            {log.ipAddress || 'N/A'}
                          </TableCell>
                          <TableCell>
                            {(log.oldValues || log.newValues) && (
                              <Button
                                size="small"
                                onClick={() => {
                                  // Implementar modal com detalhes
                                }}
                              >
                                Ver Detalhes
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={(_, newPage) => setPage(newPage)}
                    color="primary"
                  />
                </Box>
              </>
            )}
          </CardContent>
        </Card>
      </Box>
    </LocalizationProvider>
  );
};

export default Audit;

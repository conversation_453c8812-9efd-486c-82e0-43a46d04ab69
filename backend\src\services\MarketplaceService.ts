import { PrismaClient, CommandTemplate, DeviceType } from '@prisma/client'
import { Logger } from '../utils/Logger'

const prisma = new PrismaClient()

export interface MarketplaceFilters {
  category?: string
  tags?: string[]
  deviceTypes?: DeviceType[]
  search?: string
  sortBy?: 'popular' | 'recent' | 'rating' | 'downloads'
  page?: number
  limit?: number
}

export interface TemplateStats {
  totalDownloads: number
  totalLikes: number
  totalReviews: number
  averageRating: number
  recentDownloads: number // Last 30 days
}

export interface MarketplaceTemplate extends CommandTemplate {
  user: {
    id: string
    name: string
    email: string
  }
  stats: TemplateStats
  userInteraction?: {
    hasLiked: boolean
    hasFavorited: boolean
    hasReviewed: boolean
  }
}

/**
 * Serviço para gerenciamento do marketplace de templates
 */
export class MarketplaceService {
  
  /**
   * Busca templates no marketplace com filtros
   */
  static async searchTemplates(filters: MarketplaceFilters, userId?: string): Promise<{
    templates: MarketplaceTemplate[]
    total: number
    page: number
    totalPages: number
  }> {
    try {
      const {
        category,
        tags,
        deviceTypes,
        search,
        sortBy = 'popular',
        page = 1,
        limit = 20
      } = filters

      const skip = (page - 1) * limit

      // Build where clause
      const where: any = {
        isMarketplace: true,
        isPublic: true
      }

      if (category) {
        where.category = category
      }

      if (tags && tags.length > 0) {
        where.tags = {
          hasSome: tags
        }
      }

      if (deviceTypes && deviceTypes.length > 0) {
        where.deviceTypes = {
          hasSome: deviceTypes
        }
      }

      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { tags: { hasSome: [search] } }
        ]
      }

      // Build order by clause
      let orderBy: any = {}
      switch (sortBy) {
        case 'popular':
          orderBy = { likes: 'desc' }
          break
        case 'recent':
          orderBy = { createdAt: 'desc' }
          break
        case 'downloads':
          orderBy = { downloads: 'desc' }
          break
        case 'rating':
          // For rating, we'll need to calculate average in the query
          orderBy = { likes: 'desc' } // Fallback to likes for now
          break
        default:
          orderBy = { createdAt: 'desc' }
      }

      const [templates, total] = await Promise.all([
        prisma.commandTemplate.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            commands: true,
            reviews: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            },
            _count: {
              select: {
                likes_users: true,
                favorites: true,
                reviews: true
              }
            }
          },
          orderBy,
          skip,
          take: limit
        }),
        prisma.commandTemplate.count({ where })
      ])

      // Enrich templates with stats and user interactions
      const enrichedTemplates = await Promise.all(
        templates.map(async (template) => {
          const stats = await this.getTemplateStats(template.id)
          let userInteraction = undefined

          if (userId) {
            userInteraction = await this.getUserInteraction(template.id, userId)
          }

          return {
            ...template,
            stats,
            userInteraction
          } as MarketplaceTemplate
        })
      )

      return {
        templates: enrichedTemplates,
        total,
        page,
        totalPages: Math.ceil(total / limit)
      }
    } catch (error) {
      Logger.error('Erro ao buscar templates no marketplace:', error)
      throw error
    }
  }

  /**
   * Obtém um template específico do marketplace
   */
  static async getTemplate(templateId: string, userId?: string): Promise<MarketplaceTemplate | null> {
    try {
      const template = await prisma.commandTemplate.findFirst({
        where: {
          id: templateId,
          isMarketplace: true,
          isPublic: true
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          commands: {
            orderBy: {
              order: 'asc'
            }
          },
          reviews: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true
                }
              }
            },
            orderBy: {
              createdAt: 'desc'
            }
          }
        }
      })

      if (!template) {
        return null
      }

      const stats = await this.getTemplateStats(templateId)
      let userInteraction = undefined

      if (userId) {
        userInteraction = await this.getUserInteraction(templateId, userId)
      }

      return {
        ...template,
        stats,
        userInteraction
      } as MarketplaceTemplate
    } catch (error) {
      Logger.error('Erro ao obter template do marketplace:', error)
      throw error
    }
  }

  /**
   * Publica um template no marketplace
   */
  static async publishTemplate(templateId: string, userId: string, marketplaceData: {
    category: string
    tags: string[]
    deviceTypes: DeviceType[]
  }): Promise<void> {
    try {
      // Verificar se o usuário é dono do template
      const template = await prisma.commandTemplate.findFirst({
        where: {
          id: templateId,
          userId
        }
      })

      if (!template) {
        throw new Error('Template não encontrado ou sem permissão')
      }

      // Atualizar template para marketplace
      await prisma.commandTemplate.update({
        where: { id: templateId },
        data: {
          isMarketplace: true,
          isPublic: true,
          category: marketplaceData.category,
          tags: marketplaceData.tags,
          deviceTypes: marketplaceData.deviceTypes
        }
      })

      Logger.log(`Template ${templateId} publicado no marketplace por ${userId}`)
    } catch (error) {
      Logger.error('Erro ao publicar template no marketplace:', error)
      throw error
    }
  }

  /**
   * Remove template do marketplace
   */
  static async unpublishTemplate(templateId: string, userId: string): Promise<void> {
    try {
      const template = await prisma.commandTemplate.findFirst({
        where: {
          id: templateId,
          userId
        }
      })

      if (!template) {
        throw new Error('Template não encontrado ou sem permissão')
      }

      await prisma.commandTemplate.update({
        where: { id: templateId },
        data: {
          isMarketplace: false
        }
      })

      Logger.log(`Template ${templateId} removido do marketplace por ${userId}`)
    } catch (error) {
      Logger.error('Erro ao remover template do marketplace:', error)
      throw error
    }
  }

  /**
   * Faz download de um template
   */
  static async downloadTemplate(templateId: string, userId: string): Promise<CommandTemplate> {
    try {
      const template = await prisma.commandTemplate.findFirst({
        where: {
          id: templateId,
          isMarketplace: true,
          isPublic: true
        },
        include: {
          commands: {
            orderBy: {
              order: 'asc'
            }
          }
        }
      })

      if (!template) {
        throw new Error('Template não encontrado no marketplace')
      }

      // Registrar download
      await prisma.templateDownload.create({
        data: {
          templateId,
          userId
        }
      })

      // Incrementar contador de downloads
      await prisma.commandTemplate.update({
        where: { id: templateId },
        data: {
          downloads: {
            increment: 1
          }
        }
      })

      // Criar cópia do template para o usuário
      const newTemplate = await prisma.commandTemplate.create({
        data: {
          name: `${template.name} (Marketplace)`,
          description: template.description,
          userId,
          isPublic: false,
          isMarketplace: false,
          category: template.category,
          tags: template.tags,
          deviceTypes: template.deviceTypes,
          commands: {
            create: template.commands.map(cmd => ({
              name: cmd.name,
              command: cmd.command,
              description: cmd.description,
              order: cmd.order
            }))
          }
        },
        include: {
          commands: true
        }
      })

      Logger.log(`Template ${templateId} baixado por ${userId}`)
      return newTemplate
    } catch (error) {
      Logger.error('Erro ao fazer download do template:', error)
      throw error
    }
  }

  /**
   * Obtém estatísticas de um template
   */
  private static async getTemplateStats(templateId: string): Promise<TemplateStats> {
    try {
      const [downloads, likes, reviews, recentDownloads] = await Promise.all([
        prisma.commandTemplate.findUnique({
          where: { id: templateId },
          select: { downloads: true }
        }),
        prisma.templateLike.count({
          where: { templateId }
        }),
        prisma.templateReview.findMany({
          where: { templateId },
          select: { rating: true }
        }),
        prisma.templateDownload.count({
          where: {
            templateId,
            createdAt: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
            }
          }
        })
      ])

      const averageRating = reviews.length > 0
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
        : 0

      return {
        totalDownloads: downloads?.downloads || 0,
        totalLikes: likes,
        totalReviews: reviews.length,
        averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
        recentDownloads
      }
    } catch (error) {
      Logger.error('Erro ao obter estatísticas do template:', error)
      return {
        totalDownloads: 0,
        totalLikes: 0,
        totalReviews: 0,
        averageRating: 0,
        recentDownloads: 0
      }
    }
  }

  /**
   * Obtém interação do usuário com um template
   */
  private static async getUserInteraction(templateId: string, userId: string) {
    try {
      const [like, favorite, review] = await Promise.all([
        prisma.templateLike.findUnique({
          where: {
            templateId_userId: {
              templateId,
              userId
            }
          }
        }),
        prisma.templateFavorite.findUnique({
          where: {
            templateId_userId: {
              templateId,
              userId
            }
          }
        }),
        prisma.templateReview.findUnique({
          where: {
            templateId_userId: {
              templateId,
              userId
            }
          }
        })
      ])

      return {
        hasLiked: !!like,
        hasFavorited: !!favorite,
        hasReviewed: !!review
      }
    } catch (error) {
      Logger.error('Erro ao obter interação do usuário:', error)
      return {
        hasLiked: false,
        hasFavorited: false,
        hasReviewed: false
      }
    }
  }
}

export default MarketplaceService

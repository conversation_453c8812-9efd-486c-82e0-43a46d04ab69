import { useState, useEffect, useCallback } from 'react';

export interface CacheNode {
  id: string;
  name: string;
  host: string;
  port: number;
  status: 'online' | 'offline' | 'degraded' | 'maintenance';
  type: 'redis' | 'memcached' | 'hazelcast' | 'custom';
  role: 'master' | 'slave' | 'sentinel' | 'cluster';
  region: string;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  connections: {
    active: number;
    total: number;
  };
  performance: {
    hitRate: number;
    missRate: number;
    avgResponseTime: number;
    throughput: number;
  };
  lastHeartbeat: Date;
  config: Record<string, any>;
}

export interface CacheCluster {
  id: string;
  name: string;
  nodes: string[];
  strategy: 'consistent-hashing' | 'round-robin' | 'least-connections' | 'weighted';
  replicationFactor: number;
  isActive: boolean;
  healthScore: number;
  totalMemory: number;
  usedMemory: number;
  config: {
    autoFailover: boolean;
    maxRetries: number;
    timeoutMs: number;
    compressionEnabled: boolean;
    encryptionEnabled: boolean;
  };
}

export interface CacheEntry {
  key: string;
  value: any;
  ttl: number;
  size: number;
  hits: number;
  lastAccessed: Date;
  createdAt: Date;
  tags: string[];
  nodeId: string;
}

export interface CacheStats {
  totalNodes: number;
  onlineNodes: number;
  totalMemory: number;
  usedMemory: number;
  totalEntries: number;
  hitRate: number;
  missRate: number;
  avgResponseTime: number;
  throughput: number;
  topKeys: Array<{ key: string; hits: number; size: number }>;
  nodeDistribution: Array<{ nodeId: string; entries: number; memory: number }>;
}

export interface CacheOperation {
  id: string;
  type: 'get' | 'set' | 'delete' | 'flush' | 'expire';
  key: string;
  nodeId: string;
  timestamp: Date;
  duration: number;
  success: boolean;
  error?: string;
}

interface UseDistributedCacheReturn {
  nodes: CacheNode[];
  clusters: CacheCluster[];
  entries: CacheEntry[];
  operations: CacheOperation[];
  stats: CacheStats;
  isLoading: boolean;
  addNode: (node: Omit<CacheNode, 'id' | 'lastHeartbeat' | 'performance'>) => Promise<CacheNode>;
  updateNode: (nodeId: string, updates: Partial<CacheNode>) => Promise<boolean>;
  removeNode: (nodeId: string) => Promise<boolean>;
  createCluster: (cluster: Omit<CacheCluster, 'id' | 'healthScore'>) => Promise<CacheCluster>;
  updateCluster: (clusterId: string, updates: Partial<CacheCluster>) => Promise<boolean>;
  deleteCluster: (clusterId: string) => Promise<boolean>;
  testNodeConnection: (nodeId: string) => Promise<boolean>;
  flushNode: (nodeId: string) => Promise<boolean>;
  flushCluster: (clusterId: string) => Promise<boolean>;
  getCacheEntry: (key: string) => Promise<CacheEntry | null>;
  setCacheEntry: (key: string, value: any, ttl?: number, tags?: string[]) => Promise<boolean>;
  deleteCacheEntry: (key: string) => Promise<boolean>;
  searchEntries: (pattern: string) => Promise<CacheEntry[]>;
  getNodeMetrics: (nodeId: string, timeRange?: string) => Promise<any[]>;
  optimizeCache: () => Promise<{ evicted: number; compressed: number; redistributed: number }>;
  exportConfiguration: () => Promise<void>;
  importConfiguration: (file: File) => Promise<boolean>;
}

const STORAGE_KEY = 'sem-fronteiras-cache-nodes';
const CLUSTERS_KEY = 'sem-fronteiras-cache-clusters';
const ENTRIES_KEY = 'sem-fronteiras-cache-entries';
const OPERATIONS_KEY = 'sem-fronteiras-cache-operations';

export const useDistributedCache = (): UseDistributedCacheReturn => {
  const [nodes, setNodes] = useState<CacheNode[]>([]);
  const [clusters, setClusters] = useState<CacheCluster[]>([]);
  const [entries, setEntries] = useState<CacheEntry[]>([]);
  const [operations, setOperations] = useState<CacheOperation[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Carregar dados do localStorage
  useEffect(() => {
    try {
      const storedNodes = localStorage.getItem(STORAGE_KEY);
      const storedClusters = localStorage.getItem(CLUSTERS_KEY);
      const storedEntries = localStorage.getItem(ENTRIES_KEY);
      const storedOperations = localStorage.getItem(OPERATIONS_KEY);
      
      if (storedNodes) {
        const parsed = JSON.parse(storedNodes);
        const nodesWithDates = parsed.map((node: any) => ({
          ...node,
          lastHeartbeat: new Date(node.lastHeartbeat)
        }));
        setNodes(nodesWithDates);
      } else {
        generateMockData();
      }
      
      if (storedClusters) {
        setClusters(JSON.parse(storedClusters));
      }
      
      if (storedEntries) {
        const parsed = JSON.parse(storedEntries);
        const entriesWithDates = parsed.map((entry: any) => ({
          ...entry,
          lastAccessed: new Date(entry.lastAccessed),
          createdAt: new Date(entry.createdAt)
        }));
        setEntries(entriesWithDates);
      }
      
      if (storedOperations) {
        const parsed = JSON.parse(storedOperations);
        const operationsWithDates = parsed.map((op: any) => ({
          ...op,
          timestamp: new Date(op.timestamp)
        }));
        setOperations(operationsWithDates);
      }
    } catch (error) {
      console.error('Erro ao carregar cache distribuído:', error);
    }
  }, []);

  // Salvar no localStorage
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(nodes));
    } catch (error) {
      console.error('Erro ao salvar nodes:', error);
    }
  }, [nodes]);

  useEffect(() => {
    try {
      localStorage.setItem(CLUSTERS_KEY, JSON.stringify(clusters));
    } catch (error) {
      console.error('Erro ao salvar clusters:', error);
    }
  }, [clusters]);

  useEffect(() => {
    try {
      localStorage.setItem(ENTRIES_KEY, JSON.stringify(entries));
    } catch (error) {
      console.error('Erro ao salvar entries:', error);
    }
  }, [entries]);

  useEffect(() => {
    try {
      localStorage.setItem(OPERATIONS_KEY, JSON.stringify(operations));
    } catch (error) {
      console.error('Erro ao salvar operations:', error);
    }
  }, [operations]);

  const generateMockData = useCallback(() => {
    const mockNodes: CacheNode[] = [
      {
        id: '1',
        name: 'Redis Master',
        host: '*********',
        port: 6379,
        status: 'online',
        type: 'redis',
        role: 'master',
        region: 'us-east-1',
        memory: { used: 2048, total: 4096, percentage: 50 },
        connections: { active: 150, total: 200 },
        performance: { hitRate: 95.5, missRate: 4.5, avgResponseTime: 0.8, throughput: 10000 },
        lastHeartbeat: new Date(Date.now() - 30000),
        config: { maxMemory: '4gb', evictionPolicy: 'allkeys-lru' }
      },
      {
        id: '2',
        name: 'Redis Slave 1',
        host: '*********',
        port: 6379,
        status: 'online',
        type: 'redis',
        role: 'slave',
        region: 'us-east-1',
        memory: { used: 2048, total: 4096, percentage: 50 },
        connections: { active: 80, total: 200 },
        performance: { hitRate: 94.2, missRate: 5.8, avgResponseTime: 1.2, throughput: 5000 },
        lastHeartbeat: new Date(Date.now() - 45000),
        config: { maxMemory: '4gb', evictionPolicy: 'allkeys-lru' }
      },
      {
        id: '3',
        name: 'Redis Slave 2',
        host: '*********',
        port: 6379,
        status: 'degraded',
        type: 'redis',
        role: 'slave',
        region: 'us-west-1',
        memory: { used: 3584, total: 4096, percentage: 87.5 },
        connections: { active: 180, total: 200 },
        performance: { hitRate: 89.1, missRate: 10.9, avgResponseTime: 2.5, throughput: 3000 },
        lastHeartbeat: new Date(Date.now() - 120000),
        config: { maxMemory: '4gb', evictionPolicy: 'allkeys-lru' }
      }
    ];

    const mockClusters: CacheCluster[] = [
      {
        id: '1',
        name: 'Production Cluster',
        nodes: ['1', '2', '3'],
        strategy: 'consistent-hashing',
        replicationFactor: 2,
        isActive: true,
        healthScore: 92.5,
        totalMemory: 12288,
        usedMemory: 7680,
        config: {
          autoFailover: true,
          maxRetries: 3,
          timeoutMs: 5000,
          compressionEnabled: true,
          encryptionEnabled: false
        }
      }
    ];

    const mockEntries: CacheEntry[] = [];
    const keys = ['user:1001', 'session:abc123', 'config:app', 'metrics:cpu', 'cache:query:users'];
    
    for (let i = 0; i < 100; i++) {
      const key = keys[Math.floor(Math.random() * keys.length)] + ':' + i;
      mockEntries.push({
        key,
        value: { data: `mock data for ${key}` },
        ttl: Math.floor(Math.random() * 3600) + 300,
        size: Math.floor(Math.random() * 1024) + 100,
        hits: Math.floor(Math.random() * 1000),
        lastAccessed: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
        tags: ['production', 'user-data'],
        nodeId: mockNodes[Math.floor(Math.random() * mockNodes.length)].id
      });
    }

    const mockOperations: CacheOperation[] = [];
    const operationTypes: CacheOperation['type'][] = ['get', 'set', 'delete', 'flush', 'expire'];
    
    for (let i = 0; i < 200; i++) {
      mockOperations.push({
        id: `op-${i}`,
        type: operationTypes[Math.floor(Math.random() * operationTypes.length)],
        key: `key-${Math.floor(Math.random() * 1000)}`,
        nodeId: mockNodes[Math.floor(Math.random() * mockNodes.length)].id,
        timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
        duration: Math.random() * 10,
        success: Math.random() > 0.1
      });
    }

    setNodes(mockNodes);
    setClusters(mockClusters);
    setEntries(mockEntries);
    setOperations(mockOperations);
  }, []);

  const addNode = useCallback(async (nodeData: Omit<CacheNode, 'id' | 'lastHeartbeat' | 'performance'>): Promise<CacheNode> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newNode: CacheNode = {
        ...nodeData,
        id: Date.now().toString(),
        lastHeartbeat: new Date(),
        performance: { hitRate: 0, missRate: 0, avgResponseTime: 0, throughput: 0 }
      };
      
      setNodes(prev => [newNode, ...prev]);
      return newNode;
    } catch (error) {
      console.error('Erro ao adicionar node:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateNode = useCallback(async (nodeId: string, updates: Partial<CacheNode>): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setNodes(prev => prev.map(node => 
        node.id === nodeId ? { ...node, ...updates } : node
      ));
      
      return true;
    } catch (error) {
      console.error('Erro ao atualizar node:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const removeNode = useCallback(async (nodeId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setNodes(prev => prev.filter(node => node.id !== nodeId));
      setEntries(prev => prev.filter(entry => entry.nodeId !== nodeId));
      
      return true;
    } catch (error) {
      console.error('Erro ao remover node:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createCluster = useCallback(async (clusterData: Omit<CacheCluster, 'id' | 'healthScore'>): Promise<CacheCluster> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newCluster: CacheCluster = {
        ...clusterData,
        id: Date.now().toString(),
        healthScore: 100
      };
      
      setClusters(prev => [newCluster, ...prev]);
      return newCluster;
    } catch (error) {
      console.error('Erro ao criar cluster:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateCluster = useCallback(async (clusterId: string, updates: Partial<CacheCluster>): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setClusters(prev => prev.map(cluster => 
        cluster.id === clusterId ? { ...cluster, ...updates } : cluster
      ));
      
      return true;
    } catch (error) {
      console.error('Erro ao atualizar cluster:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteCluster = useCallback(async (clusterId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setClusters(prev => prev.filter(cluster => cluster.id !== clusterId));
      return true;
    } catch (error) {
      console.error('Erro ao deletar cluster:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const testNodeConnection = useCallback(async (nodeId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const success = Math.random() > 0.2;
      
      setNodes(prev => prev.map(node => 
        node.id === nodeId 
          ? { ...node, status: success ? 'online' : 'offline', lastHeartbeat: new Date() }
          : node
      ));
      
      return success;
    } catch (error) {
      console.error('Erro ao testar conexão:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const flushNode = useCallback(async (nodeId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setEntries(prev => prev.filter(entry => entry.nodeId !== nodeId));
      
      const operation: CacheOperation = {
        id: Date.now().toString(),
        type: 'flush',
        key: '*',
        nodeId,
        timestamp: new Date(),
        duration: 1000,
        success: true
      };
      
      setOperations(prev => [operation, ...prev.slice(0, 199)]);
      
      return true;
    } catch (error) {
      console.error('Erro ao limpar node:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const flushCluster = useCallback(async (clusterId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const cluster = clusters.find(c => c.id === clusterId);
      if (cluster) {
        setEntries(prev => prev.filter(entry => !cluster.nodes.includes(entry.nodeId)));
      }
      
      return true;
    } catch (error) {
      console.error('Erro ao limpar cluster:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [clusters]);

  const getCacheEntry = useCallback(async (key: string): Promise<CacheEntry | null> => {
    try {
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const entry = entries.find(e => e.key === key);
      if (entry) {
        // Atualizar hits e lastAccessed
        setEntries(prev => prev.map(e => 
          e.key === key 
            ? { ...e, hits: e.hits + 1, lastAccessed: new Date() }
            : e
        ));
      }
      
      return entry || null;
    } catch (error) {
      console.error('Erro ao buscar entrada:', error);
      return null;
    }
  }, [entries]);

  const setCacheEntry = useCallback(async (key: string, value: any, ttl: number = 3600, tags: string[] = []): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 200));
      
      const nodeId = nodes.find(n => n.status === 'online')?.id || nodes[0]?.id;
      if (!nodeId) return false;
      
      const newEntry: CacheEntry = {
        key,
        value,
        ttl,
        size: JSON.stringify(value).length,
        hits: 0,
        lastAccessed: new Date(),
        createdAt: new Date(),
        tags,
        nodeId
      };
      
      setEntries(prev => {
        const filtered = prev.filter(e => e.key !== key);
        return [newEntry, ...filtered];
      });
      
      return true;
    } catch (error) {
      console.error('Erro ao definir entrada:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [nodes]);

  const deleteCacheEntry = useCallback(async (key: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 100));
      
      setEntries(prev => prev.filter(e => e.key !== key));
      return true;
    } catch (error) {
      console.error('Erro ao deletar entrada:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const searchEntries = useCallback(async (pattern: string): Promise<CacheEntry[]> => {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const regex = new RegExp(pattern.replace('*', '.*'), 'i');
      return entries.filter(entry => regex.test(entry.key));
    } catch (error) {
      console.error('Erro ao buscar entradas:', error);
      return [];
    }
  }, [entries]);

  const getNodeMetrics = useCallback(async (nodeId: string, timeRange: string = '1h'): Promise<any[]> => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Simular métricas históricas
      const metrics = Array.from({ length: 20 }, (_, i) => ({
        timestamp: new Date(Date.now() - (20 - i) * 60 * 1000),
        memory: Math.random() * 100,
        connections: Math.floor(Math.random() * 200),
        hitRate: 90 + Math.random() * 10,
        throughput: Math.floor(Math.random() * 10000)
      }));
      
      return metrics;
    } catch (error) {
      console.error('Erro ao buscar métricas:', error);
      return [];
    }
  }, []);

  const optimizeCache = useCallback(async (): Promise<{ evicted: number; compressed: number; redistributed: number }> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const result = {
        evicted: Math.floor(Math.random() * 50),
        compressed: Math.floor(Math.random() * 100),
        redistributed: Math.floor(Math.random() * 30)
      };
      
      return result;
    } catch (error) {
      console.error('Erro ao otimizar cache:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const exportConfiguration = useCallback(async (): Promise<void> => {
    try {
      const exportData = {
        exportedAt: new Date().toISOString(),
        nodes: nodes.map(n => ({ ...n, config: { ...n.config, password: '***HIDDEN***' } })),
        clusters
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `cache-config-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao exportar configuração:', error);
      throw error;
    }
  }, [nodes, clusters]);

  const importConfiguration = useCallback(async (file: File): Promise<boolean> => {
    setIsLoading(true);
    try {
      const text = await file.text();
      const data = JSON.parse(text);
      
      if (data.nodes && Array.isArray(data.nodes)) {
        setNodes(data.nodes);
      }
      
      if (data.clusters && Array.isArray(data.clusters)) {
        setClusters(data.clusters);
      }
      
      return true;
    } catch (error) {
      console.error('Erro ao importar configuração:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Calcular estatísticas
  const stats: CacheStats = {
    totalNodes: nodes.length,
    onlineNodes: nodes.filter(n => n.status === 'online').length,
    totalMemory: nodes.reduce((sum, n) => sum + n.memory.total, 0),
    usedMemory: nodes.reduce((sum, n) => sum + n.memory.used, 0),
    totalEntries: entries.length,
    hitRate: nodes.length > 0 
      ? nodes.reduce((sum, n) => sum + n.performance.hitRate, 0) / nodes.length 
      : 0,
    missRate: nodes.length > 0 
      ? nodes.reduce((sum, n) => sum + n.performance.missRate, 0) / nodes.length 
      : 0,
    avgResponseTime: nodes.length > 0 
      ? nodes.reduce((sum, n) => sum + n.performance.avgResponseTime, 0) / nodes.length 
      : 0,
    throughput: nodes.reduce((sum, n) => sum + n.performance.throughput, 0),
    topKeys: entries
      .sort((a, b) => b.hits - a.hits)
      .slice(0, 10)
      .map(e => ({ key: e.key, hits: e.hits, size: e.size })),
    nodeDistribution: nodes.map(node => ({
      nodeId: node.id,
      entries: entries.filter(e => e.nodeId === node.id).length,
      memory: node.memory.used
    }))
  };

  return {
    nodes,
    clusters,
    entries,
    operations,
    stats,
    isLoading,
    addNode,
    updateNode,
    removeNode,
    createCluster,
    updateCluster,
    deleteCluster,
    testNodeConnection,
    flushNode,
    flushCluster,
    getCacheEntry,
    setCacheEntry,
    deleteCacheEntry,
    searchEntries,
    getNodeMetrics,
    optimizeCache,
    exportConfiguration,
    importConfiguration
  };
};

export default useDistributedCache;

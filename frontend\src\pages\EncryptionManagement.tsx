import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Switch,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper
} from '@mui/material';
import {
  Security as SecurityIcon,
  Key as KeyIcon,
  Refresh as RotateIcon,
  Download as ExportIcon,
  Upload as ImportIcon,
  Visibility as ViewIcon,
  VisibilityOff as HideIcon,
  Generate as GenerateIcon,
  Check as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Shield as ShieldIcon
} from '@mui/icons-material';
import { useEncryption } from '../hooks/useEncryption';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const EncryptionManagement: React.FC = () => {
  const {
    config,
    stats,
    isLoading,
    updateConfig,
    rotateEncryptionKey,
    exportEncryptedData,
    importEncryptedData,
    generateSecurePassword,
    checkPasswordStrength
  } = useEncryption();

  const [showRotateDialog, setShowRotateDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showPasswordGenerator, setShowPasswordGenerator] = useState(false);
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importPassword, setImportPassword] = useState('');
  const [showPasswords, setShowPasswords] = useState(false);
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [passwordLength, setPasswordLength] = useState(32);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleToggleEncryption = async (enabled: boolean) => {
    try {
      setError('');
      const success = await updateConfig({ enabled });
      if (success) {
        setSuccess(enabled ? 'Criptografia habilitada com sucesso!' : 'Criptografia desabilitada');
      } else {
        setError('Erro ao alterar configuração de criptografia');
      }
    } catch (error) {
      setError('Erro interno ao alterar criptografia');
    }
  };

  const handleUpdateConfig = async (updates: any) => {
    try {
      setError('');
      const success = await updateConfig(updates);
      if (success) {
        setSuccess('Configuração atualizada com sucesso!');
      } else {
        setError('Erro ao atualizar configuração');
      }
    } catch (error) {
      setError('Erro interno ao atualizar configuração');
    }
  };

  const handleRotateKey = async () => {
    if (!currentPassword.trim()) {
      setError('Digite a senha atual');
      return;
    }

    if (newPassword && newPassword !== confirmPassword) {
      setError('As senhas não coincidem');
      return;
    }

    try {
      setError('');
      const success = await rotateEncryptionKey(currentPassword, newPassword || undefined);
      if (success) {
        setSuccess('Chave de criptografia rotacionada com sucesso!');
        setShowRotateDialog(false);
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');
      } else {
        setError('Erro ao rotacionar chave. Verifique a senha atual.');
      }
    } catch (error) {
      setError('Erro interno ao rotacionar chave');
    }
  };

  const handleImport = async () => {
    if (!importFile || !importPassword.trim()) {
      setError('Selecione um arquivo e digite a senha');
      return;
    }

    try {
      setError('');
      const success = await importEncryptedData(importFile, importPassword);
      if (success) {
        setSuccess('Dados importados com sucesso!');
        setShowImportDialog(false);
        setImportFile(null);
        setImportPassword('');
      } else {
        setError('Erro ao importar dados. Verifique o arquivo e a senha.');
      }
    } catch (error) {
      setError('Erro interno ao importar dados');
    }
  };

  const handleGeneratePassword = () => {
    const password = generateSecurePassword(passwordLength);
    setGeneratedPassword(password);
  };

  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'very-strong': return 'success';
      case 'strong': return 'info';
      case 'medium': return 'warning';
      case 'weak': return 'error';
      default: return 'default';
    }
  };

  const getStrengthIcon = (strength: string) => {
    switch (strength) {
      case 'very-strong': return <ShieldIcon color="success" />;
      case 'strong': return <SecurityIcon color="info" />;
      case 'medium': return <WarningIcon color="warning" />;
      case 'weak': return <ErrorIcon color="error" />;
      default: return <SecurityIcon />;
    }
  };

  const passwordStrength = newPassword ? checkPasswordStrength(newPassword) : null;

  return (
    <Container maxWidth="lg">
      {/* Cabeçalho */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Gerenciamento de Criptografia
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Configure e gerencie a criptografia de dados em repouso
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {isLoading && <LinearProgress sx={{ mb: 3 }} />}

      <Grid container spacing={3}>
        {/* Status da Criptografia */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  {getStrengthIcon(stats.encryptionStrength)}
                  <Box>
                    <Typography variant="h6">
                      Status da Criptografia
                    </Typography>
                    <Chip
                      label={config.enabled ? 'Habilitada' : 'Desabilitada'}
                      color={config.enabled ? 'success' : 'error'}
                      icon={config.enabled ? <CheckIcon /> : <ErrorIcon />}
                    />
                  </Box>
                </Box>
                
                <Switch
                  checked={config.enabled}
                  onChange={(e) => handleToggleEncryption(e.target.checked)}
                  disabled={isLoading}
                />
              </Box>

              {config.enabled && (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Algoritmo
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {config.algorithm}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Derivação de Chave
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {config.keyDerivation}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Iterações
                    </Typography>
                    <Typography variant="body1" fontWeight="bold">
                      {config.iterations.toLocaleString()}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      Força
                    </Typography>
                    <Chip
                      label={stats.encryptionStrength.replace('-', ' ')}
                      color={getStrengthColor(stats.encryptionStrength) as any}
                      size="small"
                    />
                  </Grid>
                </Grid>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Estatísticas */}
        {config.enabled && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Estatísticas de Criptografia
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="primary">
                        {stats.totalEncryptedItems}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total de Itens
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="secondary">
                        {stats.encryptedCredentials}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Credenciais
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="info.main">
                        {stats.encryptedBackups}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Backups
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="warning.main">
                        {stats.encryptedLogs}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Logs
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Configurações */}
        {config.enabled && (
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Configurações de Criptografia
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>Algoritmo</InputLabel>
                      <Select
                        value={config.algorithm}
                        onChange={(e) => handleUpdateConfig({ algorithm: e.target.value })}
                        disabled={isLoading}
                      >
                        <MenuItem value="AES-256-GCM">AES-256-GCM (Recomendado)</MenuItem>
                        <MenuItem value="AES-256-CBC">AES-256-CBC</MenuItem>
                        <MenuItem value="ChaCha20-Poly1305">ChaCha20-Poly1305</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>Derivação de Chave</InputLabel>
                      <Select
                        value={config.keyDerivation}
                        onChange={(e) => handleUpdateConfig({ keyDerivation: e.target.value })}
                        disabled={isLoading}
                      >
                        <MenuItem value="PBKDF2">PBKDF2</MenuItem>
                        <MenuItem value="Argon2id">Argon2id (Mais Seguro)</MenuItem>
                        <MenuItem value="scrypt">scrypt</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      type="number"
                      label="Iterações"
                      value={config.iterations}
                      onChange={(e) => handleUpdateConfig({ iterations: parseInt(e.target.value) })}
                      disabled={isLoading}
                      inputProps={{ min: 10000, max: 1000000 }}
                      helperText="Mais iterações = mais segurança, mas processamento mais lento"
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      type="number"
                      label="Intervalo de Rotação (dias)"
                      value={config.rotationInterval}
                      onChange={(e) => handleUpdateConfig({ rotationInterval: parseInt(e.target.value) })}
                      disabled={isLoading}
                      inputProps={{ min: 30, max: 365 }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Gerenciamento de Chaves */}
        {config.enabled && (
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Gerenciamento de Chaves
                </Typography>
                
                {stats.lastKeyRotation && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      Última rotação: {format(stats.lastKeyRotation, 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                    </Typography>
                    {stats.nextKeyRotation && (
                      <Typography variant="body2" color="text.secondary">
                        Próxima rotação: {format(stats.nextKeyRotation, 'dd/MM/yyyy', { locale: ptBR })}
                      </Typography>
                    )}
                  </Box>
                )}
                
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <RotateIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Rotacionar Chave"
                      secondary="Gerar nova chave de criptografia"
                    />
                    <Button
                      variant="outlined"
                      onClick={() => setShowRotateDialog(true)}
                      disabled={isLoading}
                    >
                      Rotacionar
                    </Button>
                  </ListItem>
                  
                  <Divider />
                  
                  <ListItem>
                    <ListItemIcon>
                      <ExportIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Exportar Dados"
                      secondary="Baixar dados criptografados"
                    />
                    <Button
                      variant="outlined"
                      onClick={exportEncryptedData}
                      disabled={isLoading}
                    >
                      Exportar
                    </Button>
                  </ListItem>
                  
                  <Divider />
                  
                  <ListItem>
                    <ListItemIcon>
                      <ImportIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Importar Dados"
                      secondary="Restaurar dados criptografados"
                    />
                    <Button
                      variant="outlined"
                      onClick={() => setShowImportDialog(true)}
                      disabled={isLoading}
                    >
                      Importar
                    </Button>
                  </ListItem>
                  
                  <Divider />
                  
                  <ListItem>
                    <ListItemIcon>
                      <GenerateIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Gerador de Senhas"
                      secondary="Criar senhas seguras"
                    />
                    <Button
                      variant="outlined"
                      onClick={() => setShowPasswordGenerator(true)}
                    >
                      Gerar
                    </Button>
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>

      {/* Dialog para rotacionar chave */}
      <Dialog open={showRotateDialog} onClose={() => setShowRotateDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Rotacionar Chave de Criptografia</DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Esta operação irá gerar uma nova chave de criptografia. Certifique-se de ter um backup antes de continuar.
          </Alert>
          
          <TextField
            fullWidth
            type={showPasswords ? 'text' : 'password'}
            label="Senha Atual"
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
            sx={{ mb: 2 }}
            InputProps={{
              endAdornment: (
                <IconButton onClick={() => setShowPasswords(!showPasswords)}>
                  {showPasswords ? <VisibilityOff /> : <ViewIcon />}
                </IconButton>
              )
            }}
          />
          
          <TextField
            fullWidth
            type={showPasswords ? 'text' : 'password'}
            label="Nova Senha (opcional)"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            sx={{ mb: 2 }}
            helperText="Deixe em branco para manter a senha atual"
          />
          
          {newPassword && (
            <>
              <TextField
                fullWidth
                type={showPasswords ? 'text' : 'password'}
                label="Confirmar Nova Senha"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                sx={{ mb: 2 }}
                error={newPassword !== confirmPassword}
                helperText={newPassword !== confirmPassword ? 'Senhas não coincidem' : ''}
              />
              
              {passwordStrength && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" gutterBottom>
                    Força da senha: {passwordStrength.score}/5
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={(passwordStrength.score / 5) * 100}
                    color={passwordStrength.score >= 4 ? 'success' : passwordStrength.score >= 3 ? 'warning' : 'error'}
                  />
                  {passwordStrength.feedback.length > 0 && (
                    <List dense>
                      {passwordStrength.feedback.map((feedback, index) => (
                        <ListItem key={index}>
                          <Typography variant="caption" color="text.secondary">
                            • {feedback}
                          </Typography>
                        </ListItem>
                      ))}
                    </List>
                  )}
                </Box>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowRotateDialog(false)}>Cancelar</Button>
          <Button
            onClick={handleRotateKey}
            variant="contained"
            disabled={!currentPassword.trim() || (newPassword && newPassword !== confirmPassword)}
          >
            Rotacionar Chave
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para importar dados */}
      <Dialog open={showImportDialog} onClose={() => setShowImportDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Importar Dados Criptografados</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <input
              type="file"
              accept=".json"
              onChange={(e) => setImportFile(e.target.files?.[0] || null)}
              style={{ marginBottom: 16 }}
            />
            
            <TextField
              fullWidth
              type="password"
              label="Senha do Arquivo"
              value={importPassword}
              onChange={(e) => setImportPassword(e.target.value)}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowImportDialog(false)}>Cancelar</Button>
          <Button
            onClick={handleImport}
            variant="contained"
            disabled={!importFile || !importPassword.trim()}
          >
            Importar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para gerador de senhas */}
      <Dialog open={showPasswordGenerator} onClose={() => setShowPasswordGenerator(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Gerador de Senhas Seguras</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              type="number"
              label="Comprimento da Senha"
              value={passwordLength}
              onChange={(e) => setPasswordLength(parseInt(e.target.value))}
              inputProps={{ min: 8, max: 128 }}
              sx={{ mb: 2 }}
            />
            
            <Button
              fullWidth
              variant="contained"
              onClick={handleGeneratePassword}
              sx={{ mb: 2 }}
            >
              Gerar Senha
            </Button>
            
            {generatedPassword && (
              <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                <Typography variant="body2" fontFamily="monospace" sx={{ wordBreak: 'break-all' }}>
                  {generatedPassword}
                </Typography>
                <Button
                  size="small"
                  onClick={() => navigator.clipboard.writeText(generatedPassword)}
                  sx={{ mt: 1 }}
                >
                  Copiar
                </Button>
              </Paper>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPasswordGenerator(false)}>Fechar</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default EncryptionManagement;

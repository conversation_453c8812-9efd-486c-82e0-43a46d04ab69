# Solução para Execução de Múltiplos Comandos

Este documento descreve a implementação da solução para executar múltiplos comandos em servidores, incluindo dispositivos Huawei/HarmonyOS.

## Problema

Foram identificados os seguintes problemas com a execução de múltiplos comandos:

1. **Servidores Huawei**: Estavam escrevendo todos os comandos múltiplos na mesma linha, em vez de executar um por vez.
2. **Outros servidores**: Pararam de executar múltiplos comandos linha por linha, o que funcionava anteriormente.

## Solução Implementada

Foi implementada uma solução que detecta comandos com múltiplas linhas e os executa sequencialmente, um por vez, coletando os resultados.

### 1. Detecção de Comandos com Múltiplas Linhas

```typescript
// Verificar se o comando contém múltiplas linhas
if (command.includes('\n')) {
  console.log('Detectado comando com múltiplas linhas, executando linha por linha')
  return await this.executeMultilineCommand(command)
}
```

### 2. Método para Executar Comandos com Múltiplas Linhas

```typescript
// Método para executar comandos com múltiplas linhas
private async executeMultilineCommand(command: string): Promise<CommandResult> {
  // Dividir o comando em linhas individuais
  const lines = command.split('\n').filter(line => line.trim() !== '')
  console.log(`Executando ${lines.length} comandos separados`)
  
  let combinedOutput = ''
  let combinedError = ''
  let lastCode = 0
  
  // Executar cada linha separadamente
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    console.log(`Executando linha ${i+1}/${lines.length}: ${line}`)
    
    try {
      // Executar o comando individual
      const result = await this.executeCommand(line)
      
      // Adicionar a saída à saída combinada
      combinedOutput += `\n--- Comando: ${line} ---\n${result.stdout}\n`
      
      // Se houver erro, adicionar ao erro combinado
      if (result.stderr) {
        combinedError += `\n--- Erro no comando: ${line} ---\n${result.stderr}\n`
      }
      
      // Atualizar o código de saída
      if (result.code !== 0) {
        lastCode = result.code
      }
      
      // Aguardar um momento antes de executar o próximo comando
      if (i < lines.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    } catch (error) {
      console.error(`Erro ao executar linha ${i+1}: ${line}`, error)
      combinedError += `\n--- Erro ao executar: ${line} ---\n${error instanceof Error ? error.message : 'Erro desconhecido'}\n`
      lastCode = 1
    }
  }
  
  return {
    stdout: combinedOutput.trim(),
    stderr: combinedError.trim(),
    code: lastCode
  }
}
```

### 3. Melhoria na Detecção de Conclusão de Comando para Huawei

```typescript
// Verificar se temos o prompt de conclusão
if (chunk.includes('<RTR-PE-RBO-VLG-')) {
  // Se detectamos o prompt, podemos enviar o comando ou finalizar
  if (output.includes(command) && output.includes('Error: Too many parameters')) {
    // Se já enviamos o comando e recebemos erro, finalizar
    console.log('Detectado erro de parâmetros, finalizando comando')
    if (!commandCompleted) {
      commandCompleted = true
      cleanup()
      resolve({
        stdout: output,
        stderr: errorOutput,
        code: 0
      })
    }
  } else if (output.includes(command) && !commandCompleted) {
    // Se já enviamos o comando e recebemos o prompt novamente, podemos considerar concluído
    console.log('Detectado prompt de conclusão após comando')
    setTimeout(() => {
      if (!commandCompleted) {
        commandCompleted = true
        cleanup()
        resolve({
          stdout: output,
          stderr: errorOutput,
          code: 0
        })
      }
    }, 1000) // Aguardar 1 segundo para capturar qualquer saída adicional
  }
}
```

## Como Funciona

1. Quando um comando contendo quebras de linha (`\n`) é detectado, o sistema ativa o modo de execução de múltiplas linhas.
2. O comando é dividido em linhas individuais, removendo linhas vazias.
3. Cada linha é executada separadamente, uma após a outra, com um atraso de 1 segundo entre elas.
4. As saídas de cada comando são coletadas e combinadas em um único resultado.
5. Erros são tratados individualmente para cada linha, permitindo que o processo continue mesmo se uma linha falhar.
6. Para dispositivos Huawei, foi melhorada a detecção de conclusão de comando, verificando se o prompt reaparece após o comando ter sido enviado.

## Benefícios

1. **Execução sequencial**: Cada comando é executado separadamente, garantindo que um comando seja concluído antes do próximo começar.
2. **Saída organizada**: A saída de cada comando é claramente identificada e separada.
3. **Tratamento de erros robusto**: Erros em um comando não impedem a execução dos comandos subsequentes.
4. **Compatibilidade universal**: Funciona tanto para servidores Huawei quanto para outros tipos de servidores.
5. **Detecção de conclusão melhorada**: Reconhece quando um comando foi concluído com base no reaparecimento do prompt.

## Limitações

1. O atraso fixo de 1 segundo entre comandos pode não ser ideal para todos os cenários.
2. Comandos que dependem do contexto estabelecido por comandos anteriores podem não funcionar como esperado.
3. A detecção de conclusão baseada no prompt pode não funcionar em todos os tipos de servidores.

## Mensagem de Commit

```
Implementa execução de múltiplos comandos linha por linha

- Adiciona detecção de comandos com múltiplas linhas
- Implementa método executeMultilineCommand para executar comandos sequencialmente
- Melhora a detecção de conclusão de comando para dispositivos Huawei
- Organiza a saída de múltiplos comandos com separadores claros
- Implementa tratamento de erros robusto para comandos individuais
```

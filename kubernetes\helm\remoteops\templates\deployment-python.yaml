{{- if .Values.pythonService.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "remoteops.fullname" . }}-python
  labels:
    {{- include "remoteops.labels" . | nindent 4 }}
    app.kubernetes.io/component: python-service
spec:
  {{- if not .Values.pythonService.autoscaling.enabled }}
  replicas: {{ .Values.pythonService.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "remoteops.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: python-service
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
      labels:
        {{- include "remoteops.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: python-service
    spec:
      {{- with .Values.image.pullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "remoteops.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: python-service
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.registry }}/{{ .Values.pythonService.image.repository }}:{{ .Values.pythonService.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.pythonService.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.pythonService.service.targetPort }}
              protocol: TCP
          env:
            - name: REDIS_URL
              valueFrom:
                secretKeyRef:
                  name: {{ include "remoteops.fullname" . }}-secret
                  key: redis-url
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: {{ include "remoteops.fullname" . }}-secret
                  key: database-url
            {{- range $key, $value := .Values.pythonService.env }}
            {{- if not (has $key (list "REDIS_URL" "DATABASE_URL")) }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          livenessProbe:
            {{- toYaml .Values.pythonService.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.pythonService.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.pythonService.resources | nindent 12 }}
          volumeMounts:
            - name: config
              mountPath: /app/config
              readOnly: true
      volumes:
        - name: config
          configMap:
            name: {{ include "remoteops.fullname" . }}-config
      {{- with .Values.pythonService.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.pythonService.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.pythonService.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

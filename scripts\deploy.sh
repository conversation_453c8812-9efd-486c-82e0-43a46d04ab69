#!/bin/bash

# ===========================================
# SCRIPT DE DEPLOY PARA PRODUÇÃO
# REMOTEOPS SSH Management System
# ===========================================

set -e  # Parar em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funções auxiliares
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar se está rodando como root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "Este script não deve ser executado como root por segurança"
        exit 1
    fi
}

# Verificar dependências
check_dependencies() {
    log_info "Verificando dependências..."
    
    local deps=("docker" "docker-compose" "git" "curl")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Dependências faltando: ${missing_deps[*]}"
        log_info "Instale as dependências e execute novamente"
        exit 1
    fi
    
    log_success "Todas as dependências estão instaladas"
}

# Verificar arquivo de ambiente
check_env_file() {
    log_info "Verificando arquivo de ambiente..."
    
    if [ ! -f ".env.production" ]; then
        log_warning "Arquivo .env.production não encontrado"
        
        if [ -f ".env.production.example" ]; then
            log_info "Copiando .env.production.example para .env.production"
            cp .env.production.example .env.production
            log_warning "IMPORTANTE: Configure as variáveis em .env.production antes de continuar"
            log_warning "Especialmente: POSTGRES_PASSWORD, JWT_SECRET, e outras senhas"
            read -p "Pressione Enter após configurar o arquivo .env.production..."
        else
            log_error "Arquivo .env.production.example não encontrado"
            exit 1
        fi
    fi
    
    log_success "Arquivo de ambiente verificado"
}

# Criar diretórios necessários
create_directories() {
    log_info "Criando diretórios necessários..."
    
    local dirs=("./data/postgres" "./data/redis" "./backups" "./logs" "./nginx/ssl")
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "Diretório criado: $dir"
        fi
    done
    
    log_success "Diretórios criados"
}

# Fazer backup antes do deploy
backup_before_deploy() {
    if [ "$1" = "--skip-backup" ]; then
        log_warning "Pulando backup pré-deploy"
        return
    fi
    
    log_info "Criando backup antes do deploy..."
    
    local backup_dir="./backups/pre-deploy-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Backup do banco se estiver rodando
    if docker-compose -f docker-compose.prod.yml ps postgres | grep -q "Up"; then
        log_info "Fazendo backup do banco de dados..."
        docker-compose -f docker-compose.prod.yml exec -T postgres pg_dumpall -U postgres > "$backup_dir/database.sql"
    fi
    
    # Backup dos dados do Redis se estiver rodando
    if docker-compose -f docker-compose.prod.yml ps redis | grep -q "Up"; then
        log_info "Fazendo backup do Redis..."
        docker-compose -f docker-compose.prod.yml exec -T redis redis-cli BGSAVE
        sleep 2
        docker cp $(docker-compose -f docker-compose.prod.yml ps -q redis):/data/dump.rdb "$backup_dir/redis.rdb" 2>/dev/null || true
    fi
    
    log_success "Backup pré-deploy criado em: $backup_dir"
}

# Build das imagens
build_images() {
    log_info "Fazendo build das imagens Docker..."
    
    # Build do backend
    log_info "Building backend..."
    docker-compose -f docker-compose.prod.yml build backend
    
    # Build do Python service
    log_info "Building python-service..."
    docker-compose -f docker-compose.prod.yml build python-service
    
    # Build do frontend
    log_info "Building frontend..."
    docker-compose -f docker-compose.prod.yml build frontend
    
    log_success "Build das imagens concluído"
}

# Executar migrações do banco
run_migrations() {
    log_info "Executando migrações do banco de dados..."
    
    # Aguardar o banco estar pronto
    log_info "Aguardando banco de dados..."
    timeout 60 bash -c 'until docker-compose -f docker-compose.prod.yml exec postgres pg_isready -U postgres; do sleep 2; done'
    
    # Executar migrações
    docker-compose -f docker-compose.prod.yml exec backend npx prisma migrate deploy
    
    log_success "Migrações executadas"
}

# Verificar saúde dos serviços
check_health() {
    log_info "Verificando saúde dos serviços..."
    
    local services=("postgres" "redis" "backend" "python-service" "frontend")
    local max_attempts=30
    local attempt=1
    
    for service in "${services[@]}"; do
        log_info "Verificando $service..."
        
        while [ $attempt -le $max_attempts ]; do
            if docker-compose -f docker-compose.prod.yml ps "$service" | grep -q "healthy\|Up"; then
                log_success "$service está saudável"
                break
            fi
            
            if [ $attempt -eq $max_attempts ]; then
                log_error "$service não está saudável após $max_attempts tentativas"
                return 1
            fi
            
            log_info "Tentativa $attempt/$max_attempts para $service..."
            sleep 5
            ((attempt++))
        done
        
        attempt=1
    done
    
    log_success "Todos os serviços estão saudáveis"
}

# Deploy principal
deploy() {
    log_info "Iniciando deploy para produção..."
    
    # Parar serviços existentes
    log_info "Parando serviços existentes..."
    docker-compose -f docker-compose.prod.yml down || true
    
    # Iniciar serviços de infraestrutura primeiro
    log_info "Iniciando serviços de infraestrutura..."
    docker-compose -f docker-compose.prod.yml up -d postgres redis
    
    # Aguardar serviços de infraestrutura
    sleep 10
    
    # Executar migrações
    run_migrations
    
    # Iniciar serviços da aplicação
    log_info "Iniciando serviços da aplicação..."
    docker-compose -f docker-compose.prod.yml up -d backend python-service
    
    # Aguardar backend estar pronto
    sleep 15
    
    # Iniciar frontend
    log_info "Iniciando frontend..."
    docker-compose -f docker-compose.prod.yml up -d frontend
    
    # Verificar saúde
    check_health
    
    log_success "Deploy concluído com sucesso!"
}

# Mostrar status dos serviços
show_status() {
    log_info "Status dos serviços:"
    docker-compose -f docker-compose.prod.yml ps
    
    echo ""
    log_info "URLs de acesso:"
    echo "  Frontend: http://localhost"
    echo "  Backend API: http://localhost:3001"
    echo "  Python Service: http://localhost:8000"
    echo "  Grafana (se habilitado): http://localhost:3000"
    echo "  Prometheus (se habilitado): http://localhost:9090"
}

# Função principal
main() {
    echo "========================================"
    echo "  REMOTEOPS - DEPLOY PRODUÇÃO"
    echo "========================================"
    echo ""
    
    # Verificações iniciais
    check_root
    check_dependencies
    check_env_file
    create_directories
    
    # Backup pré-deploy
    backup_before_deploy "$1"
    
    # Build e deploy
    build_images
    deploy
    
    # Status final
    show_status
    
    echo ""
    log_success "Deploy concluído com sucesso!"
    log_info "Monitore os logs com: docker-compose -f docker-compose.prod.yml logs -f"
    log_warning "Lembre-se de configurar SSL/TLS e backup externo para produção"
}

# Verificar argumentos
case "${1:-}" in
    --help|-h)
        echo "Uso: $0 [--skip-backup] [--help]"
        echo ""
        echo "Opções:"
        echo "  --skip-backup    Pula o backup pré-deploy"
        echo "  --help, -h       Mostra esta ajuda"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # Testes do Backend
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: Install dependencies
      working-directory: ./backend
      run: npm ci

    - name: Generate Prisma client
      working-directory: ./backend
      run: npx prisma generate

    - name: Run database migrations
      working-directory: ./backend
      run: npx prisma migrate deploy
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

    - name: Run linting
      working-directory: ./backend
      run: npm run lint

    - name: Run tests
      working-directory: ./backend
      run: npm run test:coverage
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379
        JWT_SECRET: test-secret-key-for-ci

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage/lcov.info
        flags: backend

  # Testes do Frontend
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Install dependencies
      working-directory: ./frontend
      run: npm ci

    - name: Run linting
      working-directory: ./frontend
      run: npm run lint

    - name: Run tests
      working-directory: ./frontend
      run: npm run test:coverage

    - name: Build application
      working-directory: ./frontend
      run: npm run build

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend

  # Testes do Python Service
  python-tests:
    name: Python Service Tests
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('python-service/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      working-directory: ./python-service
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov flake8 black

    - name: Run linting
      working-directory: ./python-service
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Check code formatting
      working-directory: ./python-service
      run: black --check .

    - name: Run tests
      working-directory: ./python-service
      run: pytest --cov=. --cov-report=xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./python-service/coverage.xml
        flags: python

  # Build e teste de Docker
  docker-build:
    name: Docker Build Test
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, python-tests]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        file: ./backend/Dockerfile.prod
        push: false
        tags: sem-fronteiras-backend:test

    - name: Build frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        file: ./frontend/Dockerfile.prod
        push: false
        tags: sem-fronteiras-frontend:test

    - name: Build python service image
      uses: docker/build-push-action@v5
      with:
        context: ./python-service
        file: ./python-service/Dockerfile.prod
        push: false
        tags: sem-fronteiras-python:test

    - name: Test docker-compose
      run: |
        cp .env.production.example .env.production
        docker-compose -f docker-compose.prod.yml config

  # Análise de segurança
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Deploy para staging (apenas na branch develop)
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, python-tests, docker-build]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    environment:
      name: staging
      url: https://staging.sem-fronteiras.com

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "🚀 Deploying to staging environment..."
        # Aqui você adicionaria os comandos específicos para deploy
        # Por exemplo: SSH para servidor, Docker deploy, etc.

  # Deploy para produção (apenas na branch main com tag)
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, python-tests, docker-build]
    if: github.ref == 'refs/heads/main' && startsWith(github.ref, 'refs/tags/v')
    
    environment:
      name: production
      url: https://sem-fronteiras.com

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "🚀 Deploying to production environment..."
        # Aqui você adicionaria os comandos específicos para deploy em produção

  # Notificação de status
  notify:
    name: Notify Status
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, python-tests, docker-build]
    if: always()

    steps:
    - name: Notify success
      if: ${{ needs.backend-tests.result == 'success' && needs.frontend-tests.result == 'success' && needs.python-tests.result == 'success' && needs.docker-build.result == 'success' }}
      run: |
        echo "✅ All tests passed successfully!"
        # Aqui você pode adicionar notificações para Slack, Discord, etc.

    - name: Notify failure
      if: ${{ needs.backend-tests.result == 'failure' || needs.frontend-tests.result == 'failure' || needs.python-tests.result == 'failure' || needs.docker-build.result == 'failure' }}
      run: |
        echo "❌ Some tests failed!"
        # Aqui você pode adicionar notificações de falha

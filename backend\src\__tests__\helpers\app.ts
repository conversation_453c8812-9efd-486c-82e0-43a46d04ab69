import Fastify, { FastifyInstance } from 'fastify';
import cors from '@fastify/cors';
import jwt from '@fastify/jwt';
import { authRoutes } from '../../routes/auth';
import { monitoringRoutes } from '../../routes/monitoring';

export async function build(): Promise<FastifyInstance> {
  const app = Fastify({
    logger: false // Desabilitar logs durante testes
  });

  // Registrar plugins
  await app.register(cors, {
    origin: true,
    credentials: true
  });

  await app.register(jwt, {
    secret: process.env.JWT_SECRET || 'test-secret'
  });

  // Registrar rotas
  await app.register(authRoutes);
  await app.register(monitoringRoutes, { prefix: '/api/monitoring' });

  // Mock das rotas de autenticação para testes
  app.post('/login', async (request, reply) => {
    const { email, password } = request.body as { email: string; password: string };
    
    // Validação simples para testes
    if (email === '<EMAIL>' && password === 'password123') {
      const token = app.jwt.sign({ 
        userId: '1', 
        email: '<EMAIL>', 
        role: 'ADMIN' 
      });
      
      return { 
        success: true, 
        token, 
        user: { 
          id: '1', 
          email: '<EMAIL>', 
          name: 'Admin', 
          role: 'ADMIN' 
        } 
      };
    }
    
    reply.status(401).send({ success: false, message: 'Credenciais inválidas' });
  });

  return app;
}

import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>con<PERSON>utt<PERSON>,
  <PERSON><PERSON><PERSON>,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Box,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  Badge,
  BottomNavigation,
  BottomNavigationAction,
  useTheme,
  useMediaQ<PERSON>y,
  Collapse
} from '@mui/material';
import {
  Menu as MenuIcon,
  Close as CloseIcon,
  ExpandLess,
  ExpandMore,
  Notifications as NotificationsIcon,
  AccountCircle as AccountIcon
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';
import { useResponsive } from '../hooks/useResponsive';
import ThemeToggle from './ThemeToggle';

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path?: string;
  onClick?: () => void;
  badge?: number;
  children?: NavigationItem[];
  adminOnly?: boolean;
  divider?: boolean;
}

interface ResponsiveNavigationProps {
  title: string;
  logo?: React.ReactNode;
  navigationItems: NavigationItem[];
  user?: {
    name: string;
    email: string;
    avatar?: string;
    role: string;
  };
  onLogout?: () => void;
  notifications?: number;
  onNotificationClick?: () => void;
}

export const ResponsiveNavigation: React.FC<ResponsiveNavigationProps> = ({
  title,
  logo,
  navigationItems,
  user,
  onLogout,
  notifications = 0,
  onNotificationClick
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { isMobile, isTablet } = useResponsive();
  
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  // Filtrar itens baseado no papel do usuário
  const filteredItems = navigationItems.filter(item => 
    !item.adminOnly || user?.role === 'ADMIN'
  );

  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  const handleNavigation = (item: NavigationItem) => {
    if (item.onClick) {
      item.onClick();
    } else if (item.path) {
      navigate(item.path);
    }
    
    if (isMobile) {
      setDrawerOpen(false);
    }
  };

  const handleExpandToggle = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  const isActiveItem = (item: NavigationItem): boolean => {
    if (item.path) {
      return location.pathname === item.path;
    }
    return false;
  };

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);
    const isActive = isActiveItem(item);

    return (
      <React.Fragment key={item.id}>
        {item.divider && <Divider />}
        
        <ListItem disablePadding>
          <ListItemButton
            onClick={() => {
              if (hasChildren) {
                handleExpandToggle(item.id);
              } else {
                handleNavigation(item);
              }
            }}
            selected={isActive}
            sx={{
              pl: 2 + level * 2,
              '&.Mui-selected': {
                backgroundColor: theme.palette.primary.main + '20',
                borderRight: `3px solid ${theme.palette.primary.main}`
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 40 }}>
              <Badge badgeContent={item.badge} color="error">
                {item.icon}
              </Badge>
            </ListItemIcon>
            <ListItemText 
              primary={item.label}
              primaryTypographyProps={{
                variant: level > 0 ? 'body2' : 'body1',
                fontWeight: isActive ? 600 : 400
              }}
            />
            {hasChildren && (
              isExpanded ? <ExpandLess /> : <ExpandMore />
            )}
          </ListItemButton>
        </ListItem>

        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children!.map(child => renderNavigationItem(child, level + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  const drawerContent = (
    <Box sx={{ width: 280 }}>
      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
        {logo}
        <Typography variant="h6" noWrap>
          {title}
        </Typography>
      </Box>
      <Divider />
      
      <List>
        {filteredItems.map(item => renderNavigationItem(item))}
      </List>
      
      <Box sx={{ flexGrow: 1 }} />
      
      {user && (
        <>
          <Divider />
          <Box sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Avatar src={user.avatar} sx={{ width: 40, height: 40 }}>
                {user.name.charAt(0)}
              </Avatar>
              <Box>
                <Typography variant="subtitle2">{user.name}</Typography>
                <Typography variant="caption" color="text.secondary">
                  {user.role === 'ADMIN' ? 'Administrador' : 'Usuário'}
                </Typography>
              </Box>
            </Box>
            <ThemeToggle variant="menu" showLabel />
          </Box>
        </>
      )}
    </Box>
  );

  // Navegação inferior para mobile
  const bottomNavigationItems = filteredItems
    .filter(item => !item.children || item.children.length === 0)
    .slice(0, 5); // Máximo 5 itens na navegação inferior

  if (isMobile) {
    return (
      <>
        {/* AppBar superior */}
        <AppBar position="fixed">
          <Toolbar>
            <IconButton
              color="inherit"
              edge="start"
              onClick={handleDrawerToggle}
            >
              <MenuIcon />
            </IconButton>
            
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexGrow: 1 }}>
              {logo}
              <Typography variant="h6" noWrap>
                {title}
              </Typography>
            </Box>

            <IconButton color="inherit" onClick={onNotificationClick}>
              <Badge badgeContent={notifications} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>

            <IconButton color="inherit" onClick={handleUserMenuOpen}>
              <AccountIcon />
            </IconButton>
          </Toolbar>
        </AppBar>

        {/* Drawer lateral */}
        <Drawer
          anchor="left"
          open={drawerOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', p: 1 }}>
            <IconButton onClick={handleDrawerToggle}>
              <CloseIcon />
            </IconButton>
          </Box>
          {drawerContent}
        </Drawer>

        {/* Navegação inferior */}
        <BottomNavigation
          value={location.pathname}
          onChange={(_, newValue) => navigate(newValue)}
          sx={{
            position: 'fixed',
            bottom: 0,
            left: 0,
            right: 0,
            zIndex: theme.zIndex.appBar,
            borderTop: `1px solid ${theme.palette.divider}`
          }}
        >
          {bottomNavigationItems.map(item => (
            <BottomNavigationAction
              key={item.id}
              label={item.label}
              value={item.path}
              icon={
                <Badge badgeContent={item.badge} color="error">
                  {item.icon}
                </Badge>
              }
            />
          ))}
        </BottomNavigation>

        {/* Menu do usuário */}
        <Menu
          anchorEl={userMenuAnchor}
          open={Boolean(userMenuAnchor)}
          onClose={handleUserMenuClose}
        >
          <MenuItem onClick={handleUserMenuClose}>
            <ListItemIcon><AccountIcon /></ListItemIcon>
            Perfil
          </MenuItem>
          <Divider />
          <MenuItem onClick={onLogout}>
            Sair
          </MenuItem>
        </Menu>
      </>
    );
  }

  // Layout desktop/tablet
  return (
    <>
      <AppBar position="fixed" sx={{ zIndex: theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexGrow: 1 }}>
            {logo}
            <Typography variant="h6" noWrap>
              {title}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ThemeToggle variant="icon" />
            
            <IconButton color="inherit" onClick={onNotificationClick}>
              <Badge badgeContent={notifications} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>

            {user && (
              <>
                <IconButton color="inherit" onClick={handleUserMenuOpen}>
                  <Avatar src={user.avatar} sx={{ width: 32, height: 32 }}>
                    {user.name.charAt(0)}
                  </Avatar>
                </IconButton>
                
                <Menu
                  anchorEl={userMenuAnchor}
                  open={Boolean(userMenuAnchor)}
                  onClose={handleUserMenuClose}
                >
                  <Box sx={{ px: 2, py: 1 }}>
                    <Typography variant="subtitle2">{user.name}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {user.email}
                    </Typography>
                  </Box>
                  <Divider />
                  <MenuItem onClick={handleUserMenuClose}>
                    <ListItemIcon><AccountIcon /></ListItemIcon>
                    Perfil
                  </MenuItem>
                  <Divider />
                  <MenuItem onClick={onLogout}>
                    Sair
                  </MenuItem>
                </Menu>
              </>
            )}
          </Box>
        </Toolbar>
      </AppBar>

      <Drawer
        variant="permanent"
        sx={{
          width: 280,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 280,
            boxSizing: 'border-box',
            top: 64 // Altura do AppBar
          }
        }}
      >
        {drawerContent}
      </Drawer>
    </>
  );
};

export default ResponsiveNavigation;

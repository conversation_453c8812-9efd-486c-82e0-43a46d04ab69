import { api } from '../lib/api'

export interface Organization {
  id: string
  name: string
  slug: string
  description?: string
  website?: string
  logo?: string
  settings?: any
  createdAt: string
  updatedAt: string
  planType: 'STARTER' | 'PROFESSIONAL' | 'BUSINESS' | 'ENTERPRISE'
  subscriptionStatus: 'TRIAL' | 'ACTIVE' | 'PAST_DUE' | 'CANCELED' | 'INCOMPLETE' | 'INCOMPLETE_EXPIRED' | 'UNPAID'
  trialEndsAt?: string
  maxServers: number
  maxUsers: number
  ownerId: string
}

export interface OrganizationWithStats extends Organization {
  _count: {
    users: number
    servers: number
    serverGroups: number
    commandTemplates: number
  }
  currentUsage: {
    serversCount: number
    usersCount: number
    commandsExecuted: number
    apiCalls: number
    storageUsed: number
  }
}

export interface CreateOrganizationDTO {
  name: string
  slug: string
  description?: string
  website?: string
  planType?: 'STARTER' | 'PROFESSIONAL' | 'BUSINESS' | 'ENTERPRISE'
}

export interface InviteUserDTO {
  email: string
  role: 'OWNER' | 'ADMIN' | 'MEMBER' | 'VIEWER'
}

export interface OrganizationLimits {
  canAddServer: boolean
  canAddUser: boolean
  currentLimits: {
    servers: number
    users: number
    maxServers: number
    maxUsers: number
  }
}

/**
 * Serviço para gerenciamento de organizações
 */
export class OrganizationService {
  
  /**
   * Cria uma nova organização
   */
  static async createOrganization(data: CreateOrganizationDTO): Promise<Organization> {
    const response = await api.post('/organizations', data)
    return response.data.data
  }

  /**
   * Obtém organização atual do usuário
   */
  static async getCurrentOrganization(): Promise<OrganizationWithStats | null> {
    try {
      const response = await api.get('/organizations/current')
      return response.data.data
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null
      }
      throw error
    }
  }

  /**
   * Lista organizações do usuário
   */
  static async getUserOrganizations(): Promise<Organization[]> {
    const response = await api.get('/organizations/my')
    return response.data.data
  }

  /**
   * Convida usuário para organização
   */
  static async inviteUser(data: InviteUserDTO): Promise<void> {
    await api.post('/organizations/invite', data)
  }

  /**
   * Aceita convite de organização
   */
  static async acceptInvite(token: string): Promise<Organization> {
    const response = await api.post(`/organizations/accept-invite/${token}`)
    return response.data.data
  }

  /**
   * Remove usuário da organização
   */
  static async removeUser(userId: string): Promise<void> {
    await api.delete(`/organizations/users/${userId}`)
  }

  /**
   * Verifica limites da organização
   */
  static async checkLimits(): Promise<OrganizationLimits> {
    const response = await api.get('/organizations/limits')
    return response.data.data
  }

  /**
   * Troca de organização
   */
  static async switchOrganization(organizationId: string): Promise<void> {
    await api.post(`/organizations/switch/${organizationId}`)
  }

  /**
   * Atualiza organização atual
   */
  static async updateOrganization(data: {
    name?: string
    description?: string
    website?: string
    settings?: any
  }): Promise<Organization> {
    const response = await api.put('/organizations/current', data)
    return response.data.data
  }

  /**
   * Valida slug da organização
   */
  static validateSlug(slug: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!slug) {
      errors.push('Slug é obrigatório')
      return { isValid: false, errors }
    }

    if (slug.length < 3) {
      errors.push('Slug deve ter pelo menos 3 caracteres')
    }

    if (slug.length > 50) {
      errors.push('Slug deve ter no máximo 50 caracteres')
    }

    if (!/^[a-z0-9-]+$/.test(slug)) {
      errors.push('Slug deve conter apenas letras minúsculas, números e hífens')
    }

    if (slug.startsWith('-') || slug.endsWith('-')) {
      errors.push('Slug não pode começar ou terminar com hífen')
    }

    if (slug.includes('--')) {
      errors.push('Slug não pode conter hífens consecutivos')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Gera slug a partir do nome
   */
  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
      .replace(/\s+/g, '-') // Substitui espaços por hífens
      .replace(/-+/g, '-') // Remove hífens consecutivos
      .replace(/^-|-$/g, '') // Remove hífens do início e fim
  }

  /**
   * Formata informações do plano
   */
  static getPlanInfo(planType: string) {
    const plans = {
      STARTER: {
        name: 'Starter',
        price: 'Gratuito',
        maxServers: 5,
        maxUsers: 1,
        features: ['5 servidores', '1 usuário', 'Templates básicos', 'Suporte por email']
      },
      PROFESSIONAL: {
        name: 'Professional',
        price: '$29/mês',
        maxServers: 50,
        maxUsers: 5,
        features: ['50 servidores', '5 usuários', 'Templates avançados', 'Backup automático', 'Suporte prioritário']
      },
      BUSINESS: {
        name: 'Business',
        price: '$99/mês',
        maxServers: 200,
        maxUsers: 20,
        features: ['200 servidores', '20 usuários', 'Auditoria completa', 'SSO', 'Suporte dedicado']
      },
      ENTERPRISE: {
        name: 'Enterprise',
        price: 'Personalizado',
        maxServers: -1,
        maxUsers: -1,
        features: ['Servidores ilimitados', 'Usuários ilimitados', 'White-label', 'SLA garantido', 'Gerente de conta']
      }
    }

    return plans[planType as keyof typeof plans] || plans.STARTER
  }

  /**
   * Calcula dias restantes do trial
   */
  static getTrialDaysRemaining(trialEndsAt?: string): number {
    if (!trialEndsAt) return 0
    
    const trialEnd = new Date(trialEndsAt)
    const now = new Date()
    const diffTime = trialEnd.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return Math.max(0, diffDays)
  }

  /**
   * Verifica se organização está em trial
   */
  static isInTrial(organization: Organization): boolean {
    return organization.subscriptionStatus === 'TRIAL' && 
           this.getTrialDaysRemaining(organization.trialEndsAt) > 0
  }

  /**
   * Verifica se organização está ativa
   */
  static isActive(organization: Organization): boolean {
    return ['TRIAL', 'ACTIVE'].includes(organization.subscriptionStatus)
  }

  /**
   * Calcula porcentagem de uso
   */
  static getUsagePercentage(current: number, max: number): number {
    if (max <= 0) return 0 // Ilimitado
    return Math.round((current / max) * 100)
  }

  /**
   * Verifica se está próximo do limite
   */
  static isNearLimit(current: number, max: number, threshold: number = 80): boolean {
    if (max <= 0) return false // Ilimitado
    return this.getUsagePercentage(current, max) >= threshold
  }

  /**
   * Formata status da subscription
   */
  static getSubscriptionStatusInfo(status: string) {
    const statusInfo = {
      TRIAL: { label: 'Trial', color: 'blue', description: 'Período de avaliação' },
      ACTIVE: { label: 'Ativo', color: 'green', description: 'Assinatura ativa' },
      PAST_DUE: { label: 'Vencido', color: 'yellow', description: 'Pagamento em atraso' },
      CANCELED: { label: 'Cancelado', color: 'red', description: 'Assinatura cancelada' },
      INCOMPLETE: { label: 'Incompleto', color: 'orange', description: 'Pagamento pendente' },
      INCOMPLETE_EXPIRED: { label: 'Expirado', color: 'red', description: 'Pagamento expirado' },
      UNPAID: { label: 'Não pago', color: 'red', description: 'Pagamento não realizado' }
    }

    return statusInfo[status as keyof typeof statusInfo] || statusInfo.TRIAL
  }

  /**
   * Verifica se usuário pode realizar ação
   */
  static canPerformAction(userRole: string, action: string): boolean {
    const permissions = {
      OWNER: ['all'],
      ADMIN: ['invite', 'remove', 'update', 'view'],
      MEMBER: ['view'],
      VIEWER: ['view']
    }

    const userPermissions = permissions[userRole as keyof typeof permissions] || []
    return userPermissions.includes('all') || userPermissions.includes(action)
  }
}

export default OrganizationService

import { PrismaClient } from '@prisma/client';
import { Logger } from '../../utils/logger';
import * as fs from 'fs/promises';
import * as path from 'path';
import { createGzip, createGunzip } from 'zlib';
import { pipeline } from 'stream/promises';
import { createReadStream, createWriteStream } from 'fs';

interface BackupMetadata {
  id: string;
  timestamp: Date;
  version: string;
  size: number;
  tables: string[];
  checksum: string;
  description?: string;
}

interface BackupOptions {
  includeHistory: boolean;
  includeLogs: boolean;
  compress: boolean;
  description?: string;
  maxBackups?: number;
}

interface RestoreOptions {
  backupId: string;
  skipTables?: string[];
  dryRun?: boolean;
}

/**
 * Serviço de backup e recuperação do sistema
 * Gerencia backups automáticos e manuais do banco de dados e configurações
 */
export class BackupService {
  private prisma: PrismaClient;
  private backupDir: string;
  private maxBackups: number = 30; // Manter últimos 30 backups
  private autoBackupInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.prisma = new PrismaClient();
    this.backupDir = process.env.BACKUP_DIR || path.join(process.cwd(), 'backups');
    this.ensureBackupDirectory();
    this.startAutoBackup();
  }

  /**
   * Garante que o diretório de backup existe
   */
  private async ensureBackupDirectory(): Promise<void> {
    try {
      await fs.access(this.backupDir);
    } catch {
      await fs.mkdir(this.backupDir, { recursive: true });
      Logger.log(`Diretório de backup criado: ${this.backupDir}`);
    }
  }

  /**
   * Inicia backup automático
   */
  private startAutoBackup(): void {
    const interval = parseInt(process.env.AUTO_BACKUP_INTERVAL || '86400000'); // 24 horas
    
    this.autoBackupInterval = setInterval(async () => {
      try {
        await this.createBackup({
          includeHistory: true,
          includeLogs: false,
          compress: true,
          description: 'Backup automático'
        });
        
        await this.cleanOldBackups();
      } catch (error) {
        Logger.error('Erro no backup automático:', error);
      }
    }, interval);

    Logger.log(`Backup automático configurado para cada ${interval / 1000 / 60 / 60} horas`);
  }

  /**
   * Para o backup automático
   */
  stopAutoBackup(): void {
    if (this.autoBackupInterval) {
      clearInterval(this.autoBackupInterval);
      this.autoBackupInterval = null;
      Logger.log('Backup automático parado');
    }
  }

  /**
   * Cria um backup completo do sistema
   */
  async createBackup(options: BackupOptions = {
    includeHistory: true,
    includeLogs: true,
    compress: true
  }): Promise<string> {
    const backupId = this.generateBackupId();
    const timestamp = new Date();
    
    Logger.log(`Iniciando backup: ${backupId}`);

    try {
      // Coletar dados de todas as tabelas
      const backupData = await this.collectDatabaseData(options);
      
      // Criar metadata do backup
      const metadata: BackupMetadata = {
        id: backupId,
        timestamp,
        version: process.env.APP_VERSION || '1.0.0',
        size: 0,
        tables: Object.keys(backupData),
        checksum: '',
        description: options.description
      };

      // Salvar backup
      const backupPath = await this.saveBackup(backupId, backupData, metadata, options.compress);
      
      // Calcular tamanho e checksum
      const stats = await fs.stat(backupPath);
      metadata.size = stats.size;
      metadata.checksum = await this.calculateChecksum(backupPath);

      // Salvar metadata
      await this.saveMetadata(backupId, metadata);

      Logger.log(`Backup criado com sucesso: ${backupId} (${this.formatBytes(metadata.size)})`);
      return backupId;

    } catch (error) {
      Logger.error(`Erro ao criar backup ${backupId}:`, error);
      throw error;
    }
  }

  /**
   * Coleta dados do banco de dados
   */
  private async collectDatabaseData(options: BackupOptions): Promise<any> {
    const data: any = {};

    try {
      // Usuários
      data.users = await this.prisma.user.findMany();
      
      // Servidores
      data.servers = await this.prisma.server.findMany();
      
      // Grupos de servidores
      data.serverGroups = await this.prisma.serverGroup.findMany();
      
      // Templates de comandos
      data.commandTemplates = await this.prisma.commandTemplate.findMany();
      
      // Acessos de usuários a servidores
      data.serverUserAccess = await this.prisma.serverUserAccess.findMany();

      // Histórico de comandos (opcional)
      if (options.includeHistory) {
        data.commandHistory = await this.prisma.commandHistory.findMany({
          orderBy: { executedAt: 'desc' },
          take: 10000 // Limitar a 10k registros mais recentes
        });
      }

      Logger.log(`Dados coletados: ${Object.keys(data).length} tabelas`);
      return data;

    } catch (error) {
      Logger.error('Erro ao coletar dados do banco:', error);
      throw error;
    }
  }

  /**
   * Salva o backup no disco
   */
  private async saveBackup(
    backupId: string, 
    data: any, 
    metadata: BackupMetadata, 
    compress: boolean
  ): Promise<string> {
    const fileName = `${backupId}.json${compress ? '.gz' : ''}`;
    const backupPath = path.join(this.backupDir, fileName);
    
    const backupContent = JSON.stringify({
      metadata,
      data
    }, null, 2);

    if (compress) {
      // Salvar comprimido
      const readStream = Buffer.from(backupContent);
      const writeStream = createWriteStream(backupPath);
      const gzipStream = createGzip({ level: 9 });
      
      await pipeline(
        async function* () {
          yield readStream;
        },
        gzipStream,
        writeStream
      );
    } else {
      // Salvar sem compressão
      await fs.writeFile(backupPath, backupContent, 'utf8');
    }

    return backupPath;
  }

  /**
   * Salva metadata do backup
   */
  private async saveMetadata(backupId: string, metadata: BackupMetadata): Promise<void> {
    const metadataPath = path.join(this.backupDir, `${backupId}.meta.json`);
    await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2), 'utf8');
  }

  /**
   * Lista todos os backups disponíveis
   */
  async listBackups(): Promise<BackupMetadata[]> {
    try {
      const files = await fs.readdir(this.backupDir);
      const metadataFiles = files.filter(f => f.endsWith('.meta.json'));
      
      const backups: BackupMetadata[] = [];
      
      for (const file of metadataFiles) {
        try {
          const metadataPath = path.join(this.backupDir, file);
          const content = await fs.readFile(metadataPath, 'utf8');
          const metadata = JSON.parse(content) as BackupMetadata;
          backups.push(metadata);
        } catch (error) {
          Logger.error(`Erro ao ler metadata ${file}:`, error);
        }
      }

      // Ordenar por timestamp (mais recente primeiro)
      return backups.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    } catch (error) {
      Logger.error('Erro ao listar backups:', error);
      return [];
    }
  }

  /**
   * Restaura um backup
   */
  async restoreBackup(options: RestoreOptions): Promise<void> {
    const { backupId, skipTables = [], dryRun = false } = options;
    
    Logger.log(`${dryRun ? 'Simulando' : 'Iniciando'} restauração do backup: ${backupId}`);

    try {
      // Carregar backup
      const backupData = await this.loadBackup(backupId);
      
      if (!backupData) {
        throw new Error(`Backup não encontrado: ${backupId}`);
      }

      // Verificar integridade
      await this.verifyBackupIntegrity(backupId);

      if (dryRun) {
        Logger.log('Simulação de restauração:');
        Logger.log(`- Tabelas a serem restauradas: ${Object.keys(backupData.data).filter(t => !skipTables.includes(t)).join(', ')}`);
        Logger.log(`- Tabelas ignoradas: ${skipTables.join(', ') || 'nenhuma'}`);
        return;
      }

      // Fazer backup atual antes da restauração
      const currentBackupId = await this.createBackup({
        includeHistory: true,
        includeLogs: false,
        compress: true,
        description: `Backup antes da restauração de ${backupId}`
      });

      Logger.log(`Backup atual criado: ${currentBackupId}`);

      // Restaurar dados
      await this.restoreData(backupData.data, skipTables);

      Logger.log(`Backup ${backupId} restaurado com sucesso`);

    } catch (error) {
      Logger.error(`Erro ao restaurar backup ${backupId}:`, error);
      throw error;
    }
  }

  /**
   * Carrega um backup do disco
   */
  private async loadBackup(backupId: string): Promise<any> {
    const jsonPath = path.join(this.backupDir, `${backupId}.json`);
    const gzPath = path.join(this.backupDir, `${backupId}.json.gz`);
    
    let backupPath: string;
    let isCompressed = false;

    // Verificar qual arquivo existe
    try {
      await fs.access(gzPath);
      backupPath = gzPath;
      isCompressed = true;
    } catch {
      try {
        await fs.access(jsonPath);
        backupPath = jsonPath;
      } catch {
        return null;
      }
    }

    if (isCompressed) {
      // Ler arquivo comprimido
      const readStream = createReadStream(backupPath);
      const gunzipStream = createGunzip();
      
      const chunks: Buffer[] = [];
      
      await pipeline(
        readStream,
        gunzipStream,
        async function* (source) {
          for await (const chunk of source) {
            chunks.push(chunk);
            yield chunk;
          }
        }
      );

      const content = Buffer.concat(chunks).toString('utf8');
      return JSON.parse(content);
    } else {
      // Ler arquivo normal
      const content = await fs.readFile(backupPath, 'utf8');
      return JSON.parse(content);
    }
  }

  /**
   * Verifica integridade do backup
   */
  private async verifyBackupIntegrity(backupId: string): Promise<boolean> {
    try {
      const metadataPath = path.join(this.backupDir, `${backupId}.meta.json`);
      const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf8')) as BackupMetadata;
      
      // Verificar se arquivo de backup existe
      const backupPath = await this.findBackupFile(backupId);
      if (!backupPath) {
        throw new Error('Arquivo de backup não encontrado');
      }

      // Verificar checksum
      const currentChecksum = await this.calculateChecksum(backupPath);
      if (currentChecksum !== metadata.checksum) {
        throw new Error('Checksum do backup não confere - arquivo pode estar corrompido');
      }

      Logger.log(`Integridade do backup ${backupId} verificada com sucesso`);
      return true;

    } catch (error) {
      Logger.error(`Erro na verificação de integridade do backup ${backupId}:`, error);
      throw error;
    }
  }

  /**
   * Restaura dados no banco
   */
  private async restoreData(data: any, skipTables: string[]): Promise<void> {
    const transaction = await this.prisma.$transaction(async (tx) => {
      // Ordem de restauração (respeitando dependências)
      const restoreOrder = [
        'users',
        'serverGroups', 
        'servers',
        'commandTemplates',
        'serverUserAccess',
        'commandHistory'
      ];

      for (const tableName of restoreOrder) {
        if (skipTables.includes(tableName) || !data[tableName]) {
          continue;
        }

        Logger.log(`Restaurando tabela: ${tableName}`);
        
        try {
          // Limpar tabela atual
          await this.clearTable(tx, tableName);
          
          // Inserir dados do backup
          await this.insertTableData(tx, tableName, data[tableName]);
          
          Logger.log(`Tabela ${tableName} restaurada: ${data[tableName].length} registros`);
        } catch (error) {
          Logger.error(`Erro ao restaurar tabela ${tableName}:`, error);
          throw error;
        }
      }
    });

    return transaction;
  }

  /**
   * Limpa uma tabela
   */
  private async clearTable(tx: any, tableName: string): Promise<void> {
    switch (tableName) {
      case 'users':
        await tx.user.deleteMany({});
        break;
      case 'servers':
        await tx.server.deleteMany({});
        break;
      case 'serverGroups':
        await tx.serverGroup.deleteMany({});
        break;
      case 'commandTemplates':
        await tx.commandTemplate.deleteMany({});
        break;
      case 'serverUserAccess':
        await tx.serverUserAccess.deleteMany({});
        break;
      case 'commandHistory':
        await tx.commandHistory.deleteMany({});
        break;
    }
  }

  /**
   * Insere dados em uma tabela
   */
  private async insertTableData(tx: any, tableName: string, records: any[]): Promise<void> {
    if (records.length === 0) return;

    switch (tableName) {
      case 'users':
        await tx.user.createMany({ data: records });
        break;
      case 'servers':
        await tx.server.createMany({ data: records });
        break;
      case 'serverGroups':
        await tx.serverGroup.createMany({ data: records });
        break;
      case 'commandTemplates':
        await tx.commandTemplate.createMany({ data: records });
        break;
      case 'serverUserAccess':
        await tx.serverUserAccess.createMany({ data: records });
        break;
      case 'commandHistory':
        await tx.commandHistory.createMany({ data: records });
        break;
    }
  }

  /**
   * Remove backups antigos
   */
  async cleanOldBackups(): Promise<void> {
    try {
      const backups = await this.listBackups();
      
      if (backups.length <= this.maxBackups) {
        return;
      }

      const backupsToDelete = backups.slice(this.maxBackups);
      
      for (const backup of backupsToDelete) {
        await this.deleteBackup(backup.id);
      }

      Logger.log(`${backupsToDelete.length} backups antigos removidos`);

    } catch (error) {
      Logger.error('Erro ao limpar backups antigos:', error);
    }
  }

  /**
   * Remove um backup específico
   */
  async deleteBackup(backupId: string): Promise<void> {
    try {
      const backupPath = await this.findBackupFile(backupId);
      const metadataPath = path.join(this.backupDir, `${backupId}.meta.json`);

      if (backupPath) {
        await fs.unlink(backupPath);
      }

      try {
        await fs.unlink(metadataPath);
      } catch {
        // Metadata pode não existir
      }

      Logger.log(`Backup removido: ${backupId}`);

    } catch (error) {
      Logger.error(`Erro ao remover backup ${backupId}:`, error);
      throw error;
    }
  }

  // Métodos auxiliares
  private generateBackupId(): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return `backup-${timestamp}`;
  }

  private async findBackupFile(backupId: string): Promise<string | null> {
    const jsonPath = path.join(this.backupDir, `${backupId}.json`);
    const gzPath = path.join(this.backupDir, `${backupId}.json.gz`);

    try {
      await fs.access(gzPath);
      return gzPath;
    } catch {
      try {
        await fs.access(jsonPath);
        return jsonPath;
      } catch {
        return null;
      }
    }
  }

  private async calculateChecksum(filePath: string): Promise<string> {
    const crypto = await import('crypto');
    const hash = crypto.createHash('sha256');
    const stream = createReadStream(filePath);
    
    for await (const chunk of stream) {
      hash.update(chunk);
    }
    
    return hash.digest('hex');
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

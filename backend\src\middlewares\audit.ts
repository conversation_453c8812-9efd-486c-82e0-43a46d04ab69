import { FastifyRequest, FastifyReply } from 'fastify';
import { auditService } from '../services/AuditService';

export interface AuditContext {
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: any;
  newValues?: any;
}

/**
 * Middleware para registrar ações de auditoria automaticamente
 */
export function auditMiddleware(context: AuditContext) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    // Registrar a ação após a resposta ser enviada
    reply.addHook('onSend', async (request, reply, payload) => {
      try {
        const user = (request as any).user;
        
        if (user && reply.statusCode >= 200 && reply.statusCode < 300) {
          // Extrair informações da requisição
          const ipAddress = request.ip || 
            request.headers['x-forwarded-for'] as string ||
            request.headers['x-real-ip'] as string ||
            'unknown';
          
          const userAgent = request.headers['user-agent'] || 'unknown';
          
          // Determinar resourceId se não foi fornecido
          let resourceId = context.resourceId;
          if (!resourceId && request.params) {
            const params = request.params as any;
            resourceId = params.id || params.serverId || params.userId || params.commandId;
          }
          
          // Registrar a ação de auditoria
          await auditService.logAction(
            user.id,
            context.action,
            context.resource,
            resourceId || 'unknown',
            context.oldValues,
            context.newValues,
            {
              ipAddress,
              userAgent
            }
          );
        }
      } catch (error) {
        // Não falhar a requisição por erro de auditoria
        console.error('Erro ao registrar auditoria:', error);
      }
      
      return payload;
    });
  };
}

/**
 * Helper para registrar ações de auditoria manualmente
 */
export async function logAuditAction(
  request: FastifyRequest,
  action: string,
  resource: string,
  resourceId: string,
  oldValues?: any,
  newValues?: any
) {
  try {
    const user = (request as any).user;
    
    if (!user) {
      return;
    }
    
    const ipAddress = request.ip || 
      request.headers['x-forwarded-for'] as string ||
      request.headers['x-real-ip'] as string ||
      'unknown';
    
    const userAgent = request.headers['user-agent'] || 'unknown';
    
    await auditService.logAction(
      user.id,
      action,
      resource,
      resourceId,
      oldValues,
      newValues,
      {
        ipAddress,
        userAgent
      }
    );
  } catch (error) {
    console.error('Erro ao registrar auditoria manual:', error);
  }
}

/**
 * Decorador para registrar ações de auditoria automaticamente
 */
export function Audit(action: string, resource: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const request = args[0] as FastifyRequest;
      const reply = args[1] as FastifyReply;
      
      try {
        // Executar o método original
        const result = await originalMethod.apply(this, args);
        
        // Registrar auditoria após sucesso
        if (reply.statusCode >= 200 && reply.statusCode < 300) {
          const user = (request as any).user;
          
          if (user) {
            const ipAddress = request.ip || 
              request.headers['x-forwarded-for'] as string ||
              request.headers['x-real-ip'] as string ||
              'unknown';
            
            const userAgent = request.headers['user-agent'] || 'unknown';
            
            // Determinar resourceId
            let resourceId = 'unknown';
            if (request.params) {
              const params = request.params as any;
              resourceId = params.id || params.serverId || params.userId || params.commandId || 'unknown';
            }
            
            await auditService.logAction(
              user.id,
              action,
              resource,
              resourceId,
              undefined,
              undefined,
              {
                ipAddress,
                userAgent
              }
            );
          }
        }
        
        return result;
      } catch (error) {
        throw error;
      }
    };
    
    return descriptor;
  };
}

/**
 * Constantes para ações de auditoria
 */
export const AUDIT_ACTIONS = {
  // Usuários
  USER_LOGIN: 'user.login',
  USER_LOGOUT: 'user.logout',
  USER_CREATE: 'user.create',
  USER_UPDATE: 'user.update',
  USER_DELETE: 'user.delete',
  USER_PASSWORD_CHANGE: 'user.password_change',
  
  // Servidores
  SERVER_CREATE: 'server.create',
  SERVER_UPDATE: 'server.update',
  SERVER_DELETE: 'server.delete',
  SERVER_CONNECT: 'server.connect',
  SERVER_DISCONNECT: 'server.disconnect',
  
  // Comandos
  COMMAND_CREATE: 'command.create',
  COMMAND_UPDATE: 'command.update',
  COMMAND_DELETE: 'command.delete',
  COMMAND_EXECUTE: 'command.execute',
  
  // Templates
  TEMPLATE_CREATE: 'template.create',
  TEMPLATE_UPDATE: 'template.update',
  TEMPLATE_DELETE: 'template.delete',
  
  // Grupos
  GROUP_CREATE: 'group.create',
  GROUP_UPDATE: 'group.update',
  GROUP_DELETE: 'group.delete',
  GROUP_ADD_SERVER: 'group.add_server',
  GROUP_REMOVE_SERVER: 'group.remove_server',
  
  // Sistema
  SYSTEM_BACKUP: 'system.backup',
  SYSTEM_RESTORE: 'system.restore',
  SYSTEM_CONFIG_CHANGE: 'system.config_change',
  
  // Alertas
  ALERT_CREATE: 'alert.create',
  ALERT_UPDATE: 'alert.update',
  ALERT_DELETE: 'alert.delete',
  ALERT_TRIGGER: 'alert.trigger'
} as const;

/**
 * Constantes para recursos de auditoria
 */
export const AUDIT_RESOURCES = {
  USER: 'user',
  SERVER: 'server',
  COMMAND: 'command',
  TEMPLATE: 'template',
  GROUP: 'group',
  SYSTEM: 'system',
  ALERT: 'alert'
} as const;

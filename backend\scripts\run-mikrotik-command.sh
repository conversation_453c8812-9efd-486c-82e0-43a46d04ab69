#!/bin/bash
# Script para executar o comando Mikrotik
# Este script pode ser executado diretamente no servidor

# Definir diretório do projeto
if [ -d "/var/www/sem-fronteiras-ssh" ]; then
  # Ambiente de produção
  PROJETO_DIR="/var/www/sem-fronteiras-ssh"
  CONTAINER_NAME="sem-fronteiras-ssh-backend-1"
else
  # Ambiente de desenvolvimento
  PROJETO_DIR="$(pwd)"
  CONTAINER_NAME="sem-fronteiras-backend-1"
fi

echo "Executando script de comando Mikrotik..."

# Verificar se estamos dentro de um container Docker
if [ -f "/.dockerenv" ]; then
  echo "Executando dentro do container Docker..."
  # Executar diretamente
  node scripts/mikrotik-command.js
else
  echo "Executando no host, usando Docker..."
  # Executar dentro do container
  docker exec -it $CONTAINER_NAME node scripts/mikrotik-command.js
fi

echo "Script concluído!"

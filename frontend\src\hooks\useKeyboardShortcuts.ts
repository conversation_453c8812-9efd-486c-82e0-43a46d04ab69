import { useEffect, useCallback, useRef } from 'react';

export interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  action: () => void;
  description: string;
  category?: string;
  preventDefault?: boolean;
}

interface UseKeyboardShortcutsOptions {
  enabled?: boolean;
  target?: HTMLElement | Document;
}

export const useKeyboardShortcuts = (
  shortcuts: KeyboardShortcut[],
  options: UseKeyboardShortcutsOptions = {}
) => {
  const { enabled = true, target = document } = options;
  const shortcutsRef = useRef(shortcuts);

  // Atualizar referência dos shortcuts
  useEffect(() => {
    shortcutsRef.current = shortcuts;
  }, [shortcuts]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // Ignorar se estiver digitando em um input/textarea
    const activeElement = document.activeElement;
    const isInputActive = activeElement && (
      activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      activeElement.getAttribute('contenteditable') === 'true'
    );

    shortcutsRef.current.forEach(shortcut => {
      const {
        key,
        ctrlKey = false,
        altKey = false,
        shiftKey = false,
        metaKey = false,
        action,
        preventDefault = true
      } = shortcut;

      const keyMatches = event.key.toLowerCase() === key.toLowerCase();
      const modifiersMatch = 
        event.ctrlKey === ctrlKey &&
        event.altKey === altKey &&
        event.shiftKey === shiftKey &&
        event.metaKey === metaKey;

      if (keyMatches && modifiersMatch) {
        // Permitir alguns atalhos mesmo em inputs (como Ctrl+S)
        if (isInputActive && !['s', 'z', 'y', 'a', 'c', 'v', 'x'].includes(key.toLowerCase())) {
          return;
        }

        if (preventDefault) {
          event.preventDefault();
        }
        action();
      }
    });
  }, [enabled]);

  useEffect(() => {
    if (enabled) {
      target.addEventListener('keydown', handleKeyDown as EventListener);
      return () => {
        target.removeEventListener('keydown', handleKeyDown as EventListener);
      };
    }
  }, [enabled, target, handleKeyDown]);
};

// Hook para atalhos globais da aplicação
export const useGlobalShortcuts = () => {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'h',
      ctrlKey: true,
      action: () => {
        // Mostrar ajuda de atalhos
        const event = new CustomEvent('show-shortcuts-help');
        window.dispatchEvent(event);
      },
      description: 'Mostrar ajuda de atalhos',
      category: 'Geral'
    },
    {
      key: 's',
      ctrlKey: true,
      action: () => {
        // Salvar (será interceptado pelos componentes específicos)
        const event = new CustomEvent('global-save');
        window.dispatchEvent(event);
      },
      description: 'Salvar',
      category: 'Geral'
    },
    {
      key: 'n',
      ctrlKey: true,
      action: () => {
        // Novo item
        const event = new CustomEvent('global-new');
        window.dispatchEvent(event);
      },
      description: 'Novo item',
      category: 'Geral'
    },
    {
      key: 'f',
      ctrlKey: true,
      action: () => {
        // Buscar
        const event = new CustomEvent('global-search');
        window.dispatchEvent(event);
      },
      description: 'Buscar',
      category: 'Geral'
    },
    {
      key: 'Escape',
      action: () => {
        // Fechar modais/menus
        const event = new CustomEvent('global-escape');
        window.dispatchEvent(event);
      },
      description: 'Fechar modal/menu',
      category: 'Navegação'
    },
    {
      key: '1',
      ctrlKey: true,
      action: () => {
        // Navegar para servidores
        window.location.hash = '#/';
      },
      description: 'Ir para Servidores',
      category: 'Navegação'
    },
    {
      key: '2',
      ctrlKey: true,
      action: () => {
        // Navegar para histórico
        window.location.hash = '#/command-history';
      },
      description: 'Ir para Histórico',
      category: 'Navegação'
    },
    {
      key: '3',
      ctrlKey: true,
      action: () => {
        // Navegar para templates (se admin)
        window.location.hash = '#/command-templates';
      },
      description: 'Ir para Templates (Admin)',
      category: 'Navegação'
    },
    {
      key: '4',
      ctrlKey: true,
      action: () => {
        // Navegar para usuários (se admin)
        window.location.hash = '#/users';
      },
      description: 'Ir para Usuários (Admin)',
      category: 'Navegação'
    },
    {
      key: '5',
      ctrlKey: true,
      action: () => {
        // Navegar para monitoramento (se admin)
        window.location.hash = '#/monitoring';
      },
      description: 'Ir para Monitoramento (Admin)',
      category: 'Navegação'
    },
    {
      key: '6',
      ctrlKey: true,
      action: () => {
        // Navegar para auditoria (se admin)
        window.location.hash = '#/audit';
      },
      description: 'Ir para Auditoria (Admin)',
      category: 'Navegação'
    }
  ];

  useKeyboardShortcuts(shortcuts);

  return shortcuts;
};

// Hook para atalhos específicos de comandos
export const useCommandShortcuts = (callbacks: {
  onExecute?: () => void;
  onSave?: () => void;
  onClear?: () => void;
  onShowFavorites?: () => void;
  onShowHistory?: () => void;
}) => {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'Enter',
      ctrlKey: true,
      action: () => callbacks.onExecute?.(),
      description: 'Executar comando',
      category: 'Comandos'
    },
    {
      key: 's',
      ctrlKey: true,
      action: () => callbacks.onSave?.(),
      description: 'Salvar comando',
      category: 'Comandos'
    },
    {
      key: 'k',
      ctrlKey: true,
      action: () => callbacks.onClear?.(),
      description: 'Limpar comando',
      category: 'Comandos'
    },
    {
      key: 'b',
      ctrlKey: true,
      action: () => callbacks.onShowFavorites?.(),
      description: 'Mostrar favoritos',
      category: 'Comandos'
    },
    {
      key: 'h',
      ctrlKey: true,
      altKey: true,
      action: () => callbacks.onShowHistory?.(),
      description: 'Mostrar histórico',
      category: 'Comandos'
    }
  ];

  useKeyboardShortcuts(shortcuts);

  return shortcuts;
};

// Hook para atalhos de navegação em listas
export const useListShortcuts = (callbacks: {
  onSelectNext?: () => void;
  onSelectPrevious?: () => void;
  onSelectFirst?: () => void;
  onSelectLast?: () => void;
  onActivateSelected?: () => void;
}) => {
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'ArrowDown',
      action: () => callbacks.onSelectNext?.(),
      description: 'Próximo item',
      category: 'Navegação'
    },
    {
      key: 'ArrowUp',
      action: () => callbacks.onSelectPrevious?.(),
      description: 'Item anterior',
      category: 'Navegação'
    },
    {
      key: 'Home',
      action: () => callbacks.onSelectFirst?.(),
      description: 'Primeiro item',
      category: 'Navegação'
    },
    {
      key: 'End',
      action: () => callbacks.onSelectLast?.(),
      description: 'Último item',
      category: 'Navegação'
    },
    {
      key: 'Enter',
      action: () => callbacks.onActivateSelected?.(),
      description: 'Ativar item selecionado',
      category: 'Navegação'
    }
  ];

  useKeyboardShortcuts(shortcuts);

  return shortcuts;
};

// Utilitário para formatar atalhos para exibição
export const formatShortcut = (shortcut: KeyboardShortcut): string => {
  const parts: string[] = [];
  
  if (shortcut.ctrlKey) parts.push('Ctrl');
  if (shortcut.altKey) parts.push('Alt');
  if (shortcut.shiftKey) parts.push('Shift');
  if (shortcut.metaKey) parts.push('Cmd');
  
  parts.push(shortcut.key.toUpperCase());
  
  return parts.join(' + ');
};

// Utilitário para agrupar atalhos por categoria
export const groupShortcutsByCategory = (shortcuts: KeyboardShortcut[]) => {
  return shortcuts.reduce((groups, shortcut) => {
    const category = shortcut.category || 'Outros';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(shortcut);
    return groups;
  }, {} as Record<string, KeyboardShortcut[]>);
};

export default useKeyboardShortcuts;

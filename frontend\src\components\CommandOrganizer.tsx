import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Chip,
  Alert,
  IconButton,
  Toolt<PERSON>,
  Di<PERSON><PERSON>,
  Card,
  CardContent
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
  DragIndicator as DragIcon,
  Reorder as ReorderIcon
} from '@mui/icons-material';
import { DragDropList, DragDropItem, useDragDrop } from './DragDropList';

interface Command {
  id: string;
  name: string;
  command: string;
  description?: string;
  order: number;
}

interface CommandOrganizerProps {
  open: boolean;
  onClose: () => void;
  commands: Command[];
  onSave: (reorderedCommands: Command[]) => void;
  serverName?: string;
  loading?: boolean;
}

export const CommandOrganizer: React.FC<CommandOrganizerProps> = ({
  open,
  onClose,
  commands,
  onSave,
  serverName,
  loading = false
}) => {
  const {
    items: organizedCommands,
    hasChanges,
    reorderItems,
    resetChanges,
    saveChanges
  } = useDragDrop(commands.sort((a, b) => a.order - b.order));

  const [isSaving, setIsSaving] = useState(false);

  // Converter comandos para formato DragDropItem
  const dragDropItems: DragDropItem[] = organizedCommands.map((command, index) => ({
    id: command.id,
    content: (
      <CommandItem 
        command={command} 
        index={index + 1}
        totalCount={organizedCommands.length}
      />
    ),
    data: command
  }));

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Atualizar a ordem dos comandos
      const reorderedCommands = saveChanges().map((command, index) => ({
        ...command,
        order: index + 1
      }));
      
      await onSave(reorderedCommands);
      onClose();
    } catch (error) {
      console.error('Erro ao salvar ordem dos comandos:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      if (window.confirm('Você tem alterações não salvas. Deseja descartar?')) {
        resetChanges();
        onClose();
      }
    } else {
      onClose();
    }
  };

  const handleReorder = (newOrder: DragDropItem[]) => {
    const reorderedCommands = newOrder.map(item => item.data as Command);
    reorderItems(reorderedCommands);
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleCancel} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '70vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ReorderIcon color="primary" />
          <Typography variant="h6">Organizar Comandos</Typography>
          {serverName && (
            <Chip label={serverName} size="small" color="primary" />
          )}
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              Arraste e solte os comandos para reorganizá-los. A nova ordem será salva no servidor.
            </Typography>
          </Alert>

          {hasChanges && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Typography variant="body2">
                  Você tem alterações não salvas na ordem dos comandos.
                </Typography>
                <Tooltip title="Desfazer alterações">
                  <IconButton size="small" onClick={resetChanges}>
                    <RefreshIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Alert>
          )}
        </Box>

        <Card variant="outlined">
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <DragIcon color="action" />
              <Typography variant="subtitle2" color="text.secondary">
                {organizedCommands.length} comando(s) • Arraste para reordenar
              </Typography>
            </Box>
            
            <Divider sx={{ mb: 2 }} />
            
            <DragDropList
              items={dragDropItems}
              onReorder={handleReorder}
              disabled={loading || isSaving}
              showActions={false}
            />
          </CardContent>
        </Card>

        {organizedCommands.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography variant="body1" color="text.secondary">
              Nenhum comando encontrado para organizar
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Adicione comandos ao servidor para poder organizá-los
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button 
          onClick={handleCancel}
          disabled={isSaving}
        >
          Cancelar
        </Button>
        
        <Button
          onClick={resetChanges}
          disabled={!hasChanges || isSaving}
          startIcon={<RefreshIcon />}
        >
          Desfazer
        </Button>
        
        <Button
          onClick={handleSave}
          disabled={!hasChanges || isSaving || loading}
          variant="contained"
          startIcon={<SaveIcon />}
        >
          {isSaving ? 'Salvando...' : 'Salvar Ordem'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Componente para exibir um comando individual
interface CommandItemProps {
  command: Command;
  index: number;
  totalCount: number;
}

const CommandItem: React.FC<CommandItemProps> = ({ command, index, totalCount }) => {
  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
        <Chip 
          label={`#${index}`} 
          size="small" 
          color="primary" 
          variant="outlined"
          sx={{ minWidth: 40 }}
        />
        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
          {command.name}
        </Typography>
      </Box>
      
      <Typography 
        variant="body2" 
        sx={{ 
          fontFamily: 'monospace',
          backgroundColor: 'action.hover',
          p: 1,
          borderRadius: 1,
          mb: 1,
          fontSize: '0.875rem'
        }}
      >
        {command.command}
      </Typography>
      
      {command.description && (
        <Typography variant="caption" color="text.secondary">
          {command.description}
        </Typography>
      )}
    </Box>
  );
};

export default CommandOrganizer;

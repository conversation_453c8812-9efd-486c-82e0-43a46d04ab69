import { useState, useEffect, useCallback } from 'react'
import { CacheService, CacheStats, PopularCommand, CacheConfig, CacheEntry, CacheMetrics } from '../services/cacheService'
import toast from 'react-hot-toast'

interface UseCacheReturn {
  // Estado
  stats: CacheStats | null
  popularCommands: PopularCommand[]
  config: CacheConfig | null
  entries: CacheEntry[]
  metrics: CacheMetrics | null
  redisHealth: any
  serverPerformance: any[]
  recommendations: any[]
  isLoading: boolean
  isRefreshing: boolean
  
  // Paginação
  currentPage: number
  totalPages: number
  totalEntries: number
  
  // Ações
  refreshStats: () => Promise<void>
  clearCache: () => Promise<void>
  invalidateServer: (serverId: string) => Promise<void>
  invalidatePattern: (pattern: string) => Promise<void>
  updateConfig: (config: Partial<CacheConfig>) => Promise<void>
  loadEntries: (page?: number) => Promise<void>
  removeEntry: (key: string) => Promise<void>
  refreshServer: (serverId: string) => Promise<void>
  executeRedisCommand: (command: string) => Promise<any>
  exportConfig: () => Promise<void>
  importConfig: (file: File) => Promise<void>
  applyOptimizations: () => Promise<void>
  
  // Configurações
  autoRefresh: boolean
  setAutoRefresh: (enabled: boolean) => void
  refreshInterval: number
  setRefreshInterval: (interval: number) => void
}

const STORAGE_KEY = 'sem-fronteiras-cache-settings'

export const useCache = (): UseCacheReturn => {
  // Estado principal
  const [stats, setStats] = useState<CacheStats | null>(null)
  const [popularCommands, setPopularCommands] = useState<PopularCommand[]>([])
  const [config, setConfig] = useState<CacheConfig | null>(null)
  const [entries, setEntries] = useState<CacheEntry[]>([])
  const [metrics, setMetrics] = useState<CacheMetrics | null>(null)
  const [redisHealth, setRedisHealth] = useState<any>(null)
  const [serverPerformance, setServerPerformance] = useState<any[]>([])
  const [recommendations, setRecommendations] = useState<any[]>([])
  
  // Estado de carregamento
  const [isLoading, setIsLoading] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  
  // Paginação
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalEntries, setTotalEntries] = useState(0)
  
  // Configurações
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [refreshInterval, setRefreshInterval] = useState(30000) // 30 segundos

  // Carregar configurações do localStorage
  useEffect(() => {
    const saved = localStorage.getItem(STORAGE_KEY)
    if (saved) {
      try {
        const settings = JSON.parse(saved)
        setAutoRefresh(settings.autoRefresh ?? true)
        setRefreshInterval(settings.refreshInterval ?? 30000)
      } catch (error) {
        console.error('Erro ao carregar configurações do cache:', error)
      }
    }
  }, [])

  // Salvar configurações no localStorage
  useEffect(() => {
    const settings = { autoRefresh, refreshInterval }
    localStorage.setItem(STORAGE_KEY, JSON.stringify(settings))
  }, [autoRefresh, refreshInterval])

  // Função para carregar estatísticas
  const refreshStats = useCallback(async () => {
    try {
      setIsRefreshing(true)
      
      const [
        statsData,
        popularData,
        configData,
        metricsData,
        healthData,
        performanceData,
        recommendationsData
      ] = await Promise.all([
        CacheService.getStats(),
        CacheService.getPopularCommands(10),
        CacheService.getConfig(),
        CacheService.getMetrics(),
        CacheService.getRedisHealth(),
        CacheService.getServerPerformance(),
        CacheService.getOptimizationRecommendations()
      ])
      
      setStats(statsData)
      setPopularCommands(popularData)
      setConfig(configData)
      setMetrics(metricsData)
      setRedisHealth(healthData)
      setServerPerformance(performanceData)
      setRecommendations(recommendationsData)
      
    } catch (error) {
      console.error('Erro ao carregar estatísticas do cache:', error)
      toast.error('Erro ao carregar dados do cache')
    } finally {
      setIsRefreshing(false)
    }
  }, [])

  // Função para limpar cache
  const clearCache = useCallback(async () => {
    try {
      setIsLoading(true)
      await CacheService.clearCache()
      toast.success('Cache limpo com sucesso')
      await refreshStats()
    } catch (error) {
      console.error('Erro ao limpar cache:', error)
      toast.error('Erro ao limpar cache')
    } finally {
      setIsLoading(false)
    }
  }, [refreshStats])

  // Função para invalidar servidor
  const invalidateServer = useCallback(async (serverId: string) => {
    try {
      setIsLoading(true)
      await CacheService.invalidateServer(serverId)
      toast.success('Cache do servidor invalidado')
      await refreshStats()
    } catch (error) {
      console.error('Erro ao invalidar cache do servidor:', error)
      toast.error('Erro ao invalidar cache do servidor')
    } finally {
      setIsLoading(false)
    }
  }, [refreshStats])

  // Função para invalidar por padrão
  const invalidatePattern = useCallback(async (pattern: string) => {
    try {
      setIsLoading(true)
      await CacheService.invalidatePattern(pattern)
      toast.success('Cache invalidado por padrão')
      await refreshStats()
    } catch (error) {
      console.error('Erro ao invalidar cache por padrão:', error)
      toast.error('Erro ao invalidar cache por padrão')
    } finally {
      setIsLoading(false)
    }
  }, [refreshStats])

  // Função para atualizar configuração
  const updateConfig = useCallback(async (newConfig: Partial<CacheConfig>) => {
    try {
      setIsLoading(true)
      await CacheService.updateConfig(newConfig)
      toast.success('Configuração atualizada')
      await refreshStats()
    } catch (error) {
      console.error('Erro ao atualizar configuração:', error)
      toast.error('Erro ao atualizar configuração')
    } finally {
      setIsLoading(false)
    }
  }, [refreshStats])

  // Função para carregar entradas
  const loadEntries = useCallback(async (page: number = 1) => {
    try {
      setIsLoading(true)
      const result = await CacheService.getCacheEntries(page, 50)
      setEntries(result.entries)
      setCurrentPage(result.page)
      setTotalPages(result.totalPages)
      setTotalEntries(result.total)
    } catch (error) {
      console.error('Erro ao carregar entradas do cache:', error)
      toast.error('Erro ao carregar entradas do cache')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Função para remover entrada
  const removeEntry = useCallback(async (key: string) => {
    try {
      await CacheService.removeEntry(key)
      toast.success('Entrada removida do cache')
      await loadEntries(currentPage)
      await refreshStats()
    } catch (error) {
      console.error('Erro ao remover entrada:', error)
      toast.error('Erro ao remover entrada')
    }
  }, [currentPage, loadEntries, refreshStats])

  // Função para refresh de servidor
  const refreshServer = useCallback(async (serverId: string) => {
    try {
      await CacheService.refreshServer(serverId)
      toast.success('Cache do servidor atualizado')
      await refreshStats()
    } catch (error) {
      console.error('Erro ao atualizar cache do servidor:', error)
      toast.error('Erro ao atualizar cache do servidor')
    }
  }, [refreshStats])

  // Função para executar comando Redis
  const executeRedisCommand = useCallback(async (command: string) => {
    try {
      const result = await CacheService.executeRedisCommand(command)
      toast.success('Comando executado com sucesso')
      return result
    } catch (error) {
      console.error('Erro ao executar comando Redis:', error)
      toast.error('Erro ao executar comando Redis')
      throw error
    }
  }, [])

  // Função para exportar configuração
  const exportConfig = useCallback(async () => {
    try {
      const blob = await CacheService.exportConfig()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `cache-config-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
      toast.success('Configuração exportada')
    } catch (error) {
      console.error('Erro ao exportar configuração:', error)
      toast.error('Erro ao exportar configuração')
    }
  }, [])

  // Função para importar configuração
  const importConfig = useCallback(async (file: File) => {
    try {
      setIsLoading(true)
      await CacheService.importConfig(file)
      toast.success('Configuração importada')
      await refreshStats()
    } catch (error) {
      console.error('Erro ao importar configuração:', error)
      toast.error('Erro ao importar configuração')
    } finally {
      setIsLoading(false)
    }
  }, [refreshStats])

  // Função para aplicar otimizações
  const applyOptimizations = useCallback(async () => {
    try {
      setIsLoading(true)
      const result = await CacheService.applyOptimizations()
      toast.success(`${result.applied} otimizações aplicadas`)
      await refreshStats()
    } catch (error) {
      console.error('Erro ao aplicar otimizações:', error)
      toast.error('Erro ao aplicar otimizações')
    } finally {
      setIsLoading(false)
    }
  }, [refreshStats])

  // Auto-refresh
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(refreshStats, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, refreshStats])

  // Carregar dados iniciais
  useEffect(() => {
    refreshStats()
  }, [refreshStats])

  return {
    // Estado
    stats,
    popularCommands,
    config,
    entries,
    metrics,
    redisHealth,
    serverPerformance,
    recommendations,
    isLoading,
    isRefreshing,
    
    // Paginação
    currentPage,
    totalPages,
    totalEntries,
    
    // Ações
    refreshStats,
    clearCache,
    invalidateServer,
    invalidatePattern,
    updateConfig,
    loadEntries,
    removeEntry,
    refreshServer,
    executeRedisCommand,
    exportConfig,
    importConfig,
    applyOptimizations,
    
    // Configurações
    autoRefresh,
    setAutoRefresh,
    refreshInterval,
    setRefreshInterval
  }
}

export default useCache

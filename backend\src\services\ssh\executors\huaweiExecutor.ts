import { NodeSSH } from 'node-ssh';
import { CommandResult } from '../../../types/server';
import { Logger } from '../../../utils/logger';
import { BaseExecutor } from './baseExecutor';

/**
 * Executor de comandos para dispositivos Huawei
 */
export class HuaweiExecutor extends BaseExecutor {
  constructor(ssh: NodeSSH) {
    super(ssh);
  }

  /**
   * Executa um comando em um dispositivo Huawei
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  async executeCommand(command: string): Promise<CommandResult> {
    try {
      // Verificar se o comando contém múltiplas linhas
      if (command.includes('\n')) {
        Logger.log('Detectado comando com múltiplas linhas em dispositivo Huawei, usando modo especial');
        return await this.executeHuaweiMultilineCommand(command);
      }

      // Limpar o comando para remover caracteres invisíveis e espaços extras
      const cleanCommand = command.trim().replace(/\s+/g, ' ');

      Logger.log(`Executando comando Huawei: ${cleanCommand.substring(0, 50)}${cleanCommand.length > 50 ? '...' : ''}`);

      // Para dispositivos Huawei, sempre usar shell interativo com abordagem simplificada
      return await this.executeHuaweiCommand(cleanCommand);
    } catch (error) {
      Logger.error('Erro ao executar comando Huawei:', error);
      throw new Error(`Falha ao executar comando Huawei: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
    }
  }

  /**
   * Executa um comando em um dispositivo Huawei usando shell interativo
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  private executeHuaweiCommand(command: string): Promise<CommandResult> {
    return new Promise((resolve, reject) => {
      let shell: any = null;
      let commandTimeout: NodeJS.Timeout | null = null;
      let globalTimeout: NodeJS.Timeout | null = null;

      // Calcular timeout dinâmico para comando único (pode conter múltiplos subcomandos)
      // Estimar a complexidade do comando contando o número de operações
      const commandComplexity = Math.max(2, command.split(';').length + command.split('|').length);
      const MAX_EXECUTION_TIME = this.calculateDynamicTimeout(commandComplexity);

      // Timeout de inatividade reduzido para comandos maiores para detectar conclusão mais rapidamente
      // Para comandos complexos, usar timeout menor para evitar espera desnecessária
      const INACTIVITY_TIMEOUT = commandComplexity > 3 ? 15000 : 20000; // 15-20 segundos baseado na complexidade
      const PROMPT_COMPLETION_DELAY = 3000; // 3 segundos para capturar saída adicional após prompt

      Logger.log(`Usando timeout dinâmico de ${MAX_EXECUTION_TIME}ms para comando Huawei com complexidade ${commandComplexity}, inatividade: ${INACTIVITY_TIMEOUT}ms`)

      try {
        let output = '';
        let errorOutput = '';
        let lastDataTime = Date.now();
        let commandCompleted = false;

        // Função para limpar recursos
        const cleanup = () => {
          if (commandTimeout) {
            clearTimeout(commandTimeout);
            commandTimeout = null;
          }

          if (globalTimeout) {
            clearTimeout(globalTimeout);
            globalTimeout = null;
          }

          if (shell) {
            // Remover todos os listeners para evitar vazamentos de memória
            shell.removeAllListeners('data');
            shell.removeAllListeners('error');
            shell.removeAllListeners('close');
            shell.removeAllListeners('end');

            if (!shell.ended) {
              try {
                shell.end();
              } catch (e) {
                Logger.error('Erro ao fechar shell:', e);
              }
            }
            shell = null;
          }
        };

        // Timeout global para garantir que o comando não execute indefinidamente
        globalTimeout = setTimeout(() => {
          Logger.warn(`Timeout global atingido após ${MAX_EXECUTION_TIME}ms para o comando: ${command}`);
          cleanup();
          resolve({
            stdout: output,
            stderr: errorOutput + '\n[ERRO] Timeout global atingido após 90 segundos',
            code: 124 // Código de timeout
          });
        }, MAX_EXECUTION_TIME);

        // Função para verificar se o comando foi concluído por inatividade
        const checkCompletion = () => {
          const now = Date.now();
          const timeSinceLastData = now - lastDataTime;

          if (timeSinceLastData >= INACTIVITY_TIMEOUT && !commandCompleted) {
            // Para comandos maiores, verificar se a saída parece completa
            const outputLines = output.split('\n');
            const hasCommandEcho = output.includes(command);
            const hasPromptAtEnd = outputLines[outputLines.length - 1]?.trim().match(/[>\]#$]$/);

            Logger.log(`Comando concluído após ${timeSinceLastData}ms de inatividade. Linhas de saída: ${outputLines.length}, Echo: ${hasCommandEcho}, Prompt final: ${!!hasPromptAtEnd}`);
            commandCompleted = true;
            cleanup();
            resolve({
              stdout: output,
              stderr: errorOutput,
              code: 0
            });
          } else if (!commandCompleted) {
            // Para comandos complexos, verificar com mais frequência
            const checkInterval = commandComplexity > 3 ? 500 : 1000;
            commandTimeout = setTimeout(checkCompletion, checkInterval);
          }
        };

        // Iniciar shell interativo com configurações básicas
        this.ssh.requestShell({
          term: 'dumb', // Terminal mais simples
          rows: 80,
          cols: 120
        }).then(shellInstance => {
          shell = shellInstance;

          shell.on('data', (data: Buffer) => {
            const chunk = data.toString('utf8');
            output += chunk;
            lastDataTime = Date.now();
            Logger.log(`stdout (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);

            // Verificar se há prompt de paginação primeiro (mais prioritário)
            const paginationPatterns = [
              'More', '--More--', '-- More --', 'Press any key to continue',
              'Press SPACE to continue', 'Continue? (y/n)', '(more)',
              'Press any key', 'Hit any key', 'Press <SPACE>'
            ];

            const hasPagination = paginationPatterns.some(pattern =>
              chunk.toLowerCase().includes(pattern.toLowerCase())
            );

            if (hasPagination) {
              Logger.log('Prompt de paginação detectado, enviando espaço');
              shell.write(' ');
              lastDataTime = Date.now();
              return; // Não processar outros padrões quando há paginação
            }

            // Verificar se temos o prompt de conclusão
            const trimmedChunk = chunk.trim();
            const hasPrompt = trimmedChunk.endsWith('<RTR-PE-RBO-VLG-') ||
                             trimmedChunk.endsWith('>') ||
                             trimmedChunk.endsWith(']') ||
                             /^<[A-Za-z0-9\-_]+>$/.test(trimmedChunk) ||
                             /^\[[A-Za-z0-9\-_~\/]+\]$/.test(trimmedChunk);

            if (hasPrompt) {
              // Se detectamos o prompt, podemos enviar o comando ou finalizar
              if (output.includes(command) && output.includes('Error: Too many parameters')) {
                // Se já enviamos o comando e recebemos erro, finalizar
                Logger.log('Detectado erro de parâmetros, finalizando comando');
                if (!commandCompleted) {
                  commandCompleted = true;
                  cleanup();
                  resolve({
                    stdout: output,
                    stderr: errorOutput,
                    code: 0
                  });
                }
              } else if (output.includes(command) && !commandCompleted) {
                // Se já enviamos o comando e recebemos o prompt novamente, podemos considerar concluído
                Logger.log('Detectado prompt de conclusão após comando, aguardando saída adicional');
                setTimeout(() => {
                  if (!commandCompleted) {
                    commandCompleted = true;
                    cleanup();
                    resolve({
                      stdout: output,
                      stderr: errorOutput,
                      code: 0
                    });
                  }
                }, PROMPT_COMPLETION_DELAY); // Aguardar mais tempo para capturar qualquer saída adicional
              }
            }
          });

          shell.stderr?.on('data', (data: Buffer) => {
            const chunk = data.toString('utf8');
            errorOutput += chunk;
            lastDataTime = Date.now();
            Logger.error(`stderr (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);
          });

          shell.on('error', (err: Error) => {
            Logger.error('Shell error:', err);
            errorOutput += `\n[ERRO] ${err.message}`;
            cleanup();
            reject(err);
          });

          shell.on('close', () => {
            Logger.log('Shell fechado');
            if (!commandCompleted) {
              commandCompleted = true;
              cleanup();
              resolve({
                stdout: output,
                stderr: errorOutput,
                code: 0
              });
            }
          });

          // Aguardar um momento para o shell inicializar
          setTimeout(() => {
            // Enviar um Enter para limpar qualquer prompt
            shell.write('\n');

            // Aguardar mais tempo antes de enviar o comando
            setTimeout(() => {
              if (!commandCompleted && shell) {
                Logger.log(`Enviando comando: ${command}`);
                shell.write(command + '\n');

                // Iniciar verificação de conclusão após enviar o comando
                commandTimeout = setTimeout(checkCompletion, INACTIVITY_TIMEOUT);
              }
            }, 3000); // 3 segundos após o Enter inicial
          }, 2000); // 2 segundos para inicialização
        }).catch(error => {
          Logger.error('Erro ao criar shell interativo:', error);
          cleanup();
          reject(error);
        });
      } catch (error) {
        Logger.error('Erro no shell interativo:', error);
        if (shell) {
          // Limpar recursos em caso de erro
          if (commandTimeout) {
            clearTimeout(commandTimeout);
            commandTimeout = null;
          }
          if (globalTimeout) {
            clearTimeout(globalTimeout);
            globalTimeout = null;
          }
          try {
            shell.removeAllListeners();
            if (!shell.ended) shell.end();
          } catch (e) {
            Logger.error('Erro ao limpar shell em caso de erro:', e);
          }
        }
        reject(error);
      }
    });
  }

  /**
   * Executa um comando com múltiplas linhas em um dispositivo Huawei
   * @param command Comando com múltiplas linhas
   * @returns Resultado do comando
   */
  private executeHuaweiMultilineCommand(command: string): Promise<CommandResult> {
    return new Promise((resolve, reject) => {
      Logger.log('Executando comando multilinhas em modo especial para Huawei');

      // Para Huawei, precisamos manter uma única sessão e enviar os comandos sequencialmente
      const lines = command.split('\n').filter(line => line.trim() !== '');
      Logger.log(`Executando ${lines.length} comandos em uma única sessão para Huawei`);

      let shell: any = null;
      let commandTimeout: NodeJS.Timeout | null = null;
      let globalTimeout: NodeJS.Timeout | null = null;
      let currentLineIndex = 0;

      // Calcular timeout dinâmico com base na quantidade de comandos
      const commandCount = lines.length;
      const MAX_EXECUTION_TIME = this.calculateDynamicTimeout(commandCount);

      // Timeout de inatividade ajustado para múltiplos comandos
      // Para muitos comandos, usar timeout menor para detectar conclusão mais rapidamente
      const INACTIVITY_TIMEOUT = commandCount > 5 ? 12000 : 18000; // 12-18 segundos baseado na quantidade
      const COMMAND_DELAY = 3000; // 3 segundos entre comandos
      const FINAL_COMPLETION_DELAY = 5000; // 5 segundos após último comando

      Logger.log(`Usando timeout dinâmico de ${MAX_EXECUTION_TIME}ms para ${commandCount} comandos Huawei, inatividade: ${INACTIVITY_TIMEOUT}ms`)

      let output = '';
      let errorOutput = '';
      let lastDataTime = Date.now();
      let commandCompleted = false;

      // Função para limpar recursos
      const cleanup = () => {
        if (commandTimeout) {
          clearTimeout(commandTimeout);
          commandTimeout = null;
        }

        if (globalTimeout) {
          clearTimeout(globalTimeout);
          globalTimeout = null;
        }

        if (shell) {
          // Remover todos os listeners para evitar vazamentos de memória
          shell.removeAllListeners('data');
          shell.removeAllListeners('error');
          shell.removeAllListeners('close');
          shell.removeAllListeners('end');

          if (!shell.ended) {
            try {
              shell.end();
            } catch (e) {
              Logger.error('Erro ao fechar shell:', e);
            }
          }
          shell = null;
        }
      };

      // Timeout global para garantir que o comando não execute indefinidamente
      globalTimeout = setTimeout(() => {
        Logger.warn(`Timeout global atingido após ${MAX_EXECUTION_TIME}ms para o script Huawei`);
        cleanup();
        resolve({
          stdout: output,
          stderr: errorOutput + '\n[ERRO] Timeout global atingido após 120 segundos',
          code: 124 // Código de timeout
        });
      }, MAX_EXECUTION_TIME);

      // Função para verificar se o comando foi concluído por inatividade
      const checkCompletion = () => {
        const now = Date.now();
        const timeSinceLastData = now - lastDataTime;

        if (timeSinceLastData >= INACTIVITY_TIMEOUT && !commandCompleted) {
          // Para scripts multilinhas, verificar se todos os comandos foram processados
          const outputLines = output.split('\n');
          const hasPromptAtEnd = outputLines[outputLines.length - 1]?.trim().match(/[>\]#$]$/);

          Logger.log(`Script concluído após ${timeSinceLastData}ms de inatividade. Comandos processados: ${currentLineIndex}/${lines.length}, Linhas de saída: ${outputLines.length}, Prompt final: ${!!hasPromptAtEnd}`);
          commandCompleted = true;
          cleanup();
          resolve({
            stdout: output,
            stderr: errorOutput,
            code: 0
          });
        } else if (!commandCompleted) {
          // Para scripts com muitos comandos, verificar com mais frequência
          const checkInterval = commandCount > 5 ? 500 : 1000;
          commandTimeout = setTimeout(checkCompletion, checkInterval);
        }
      };

      // Função para enviar o próximo comando
      const sendNextCommand = () => {
        if (currentLineIndex >= lines.length || commandCompleted) {
          Logger.log('Todos os comandos foram enviados');
          return;
        }

        const line = lines[currentLineIndex].trim();
        Logger.log(`Enviando comando ${currentLineIndex+1}/${lines.length} para Huawei: ${line}`);

        if (shell && !commandCompleted) {
          shell.write(line + '\n');
          lastDataTime = Date.now(); // Resetar o tempo do último dado
          currentLineIndex++;
        }
      };

      try {
        // Iniciar shell interativo com configurações específicas para Huawei
        this.ssh.requestShell({
          term: 'dumb', // Terminal mais simples para Huawei
          rows: 80,
          cols: 120
        }).then(shellInstance => {
          shell = shellInstance;

          shell.on('data', (data: Buffer) => {
            const chunk = data.toString('utf8');
            output += chunk;
            lastDataTime = Date.now();
            Logger.log(`stdout (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);

            // Verificar se há prompt de paginação primeiro (mais prioritário)
            const paginationPatterns = [
              'More', '--More--', '-- More --', 'Press any key to continue',
              'Press SPACE to continue', 'Continue? (y/n)', '(more)',
              'Press any key', 'Hit any key', 'Press <SPACE>'
            ];

            const hasPagination = paginationPatterns.some(pattern =>
              chunk.toLowerCase().includes(pattern.toLowerCase())
            );

            if (hasPagination) {
              Logger.log('Prompt de paginação detectado em script multilinhas, enviando espaço');
              shell.write(' ');
              lastDataTime = Date.now();
              return; // Não processar outros padrões quando há paginação
            }

            // Verificar se temos o prompt do Huawei (padrões mais abrangentes)
            const trimmedChunk = chunk.trim();
            const genericPromptMatch = trimmedChunk.match(/^<[A-Za-z0-9\-_]+>$/) || trimmedChunk.match(/^\[[A-Za-z0-9\-_~\/]+\]$/);
            const hasPrompt = trimmedChunk.endsWith('<RTR-PE-RBO-VLG-') ||
                             trimmedChunk.endsWith('[~RTR-PE-RBO-VLG-') ||
                             trimmedChunk.endsWith('[RTR-PE-RBO-VLG-') ||
                             trimmedChunk.endsWith('>') ||
                             trimmedChunk.endsWith(']') ||
                             genericPromptMatch ||
                             trimmedChunk.includes('Error:');

            if (hasPrompt) {
              Logger.log('Detectado prompt Huawei, aguardando antes de enviar próximo comando');

              // Aguardar um momento antes de enviar o próximo comando
              setTimeout(() => {
                if (!commandCompleted && currentLineIndex < lines.length) {
                  sendNextCommand();
                } else if (currentLineIndex >= lines.length && !commandCompleted) {
                  // Se todos os comandos foram enviados, aguardar mais um pouco e finalizar
                  setTimeout(() => {
                    if (!commandCompleted) {
                      Logger.log('Todos os comandos foram processados, finalizando');
                      commandCompleted = true;
                      cleanup();
                      resolve({
                        stdout: output,
                        stderr: errorOutput,
                        code: 0
                      });
                    }
                  }, FINAL_COMPLETION_DELAY); // Aguardar mais tempo após o último comando
                }
              }, COMMAND_DELAY); // Aguardar entre comandos
            }

            // Verificar se há erro de canal SSH ou outros erros comuns em Huawei
            if (chunk.includes('Channel open failure') ||
                chunk.includes('open failed') ||
                chunk.includes('Error: Too many parameters') ||
                chunk.includes('Unrecognized command')) {

              Logger.log('Detectado erro no Huawei:', chunk.trim());

              // Adicionar mensagem de erro apropriada
              if (chunk.includes('Channel open failure') || chunk.includes('open failed')) {
                errorOutput += '\n[ERRO] Falha no canal SSH. Isso pode ocorrer quando o dispositivo não suporta múltiplos comandos.\n';
              } else if (chunk.includes('Too many parameters')) {
                errorOutput += '\n[ERRO] Muitos parâmetros detectados. O comando pode estar incorreto para este dispositivo.\n';
              } else if (chunk.includes('Unrecognized command')) {
                errorOutput += '\n[ERRO] Comando não reconhecido pelo dispositivo.\n';
              }

              // Continuar com o próximo comando após um tempo maior
              setTimeout(() => {
                if (!commandCompleted && currentLineIndex < lines.length) {
                  Logger.log('Tentando continuar após erro...');
                  sendNextCommand();
                }
              }, 5000); // Aguardar 5 segundos após erro
            }


          });

          shell.stderr?.on('data', (data: Buffer) => {
            const chunk = data.toString('utf8');
            errorOutput += chunk;
            lastDataTime = Date.now();
            Logger.error(`stderr (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`);
          });

          shell.on('error', (err: Error) => {
            Logger.error('Shell error:', err);
            errorOutput += `\n[ERRO] ${err.message}`;

            // Se ocorrer um erro, tentar continuar com o próximo comando
            setTimeout(() => {
              if (!commandCompleted && currentLineIndex < lines.length) {
                Logger.log('Tentando continuar após erro...');
                sendNextCommand();
              }
            }, 5000); // Aguardar 5 segundos após erro
          });

          shell.on('close', () => {
            Logger.log('Shell fechado');
            if (!commandCompleted) {
              commandCompleted = true;
              cleanup();
              resolve({
                stdout: output,
                stderr: errorOutput,
                code: 0
              });
            }
          });

          // Aguardar um momento para o shell inicializar
          setTimeout(() => {
            // Enviar um Enter para limpar qualquer prompt
            shell.write('\n');

            // Aguardar antes de enviar o primeiro comando
            setTimeout(() => {
              if (!commandCompleted) {
                // Iniciar o envio de comandos
                sendNextCommand();

                // Iniciar verificação de conclusão
                commandTimeout = setTimeout(checkCompletion, INACTIVITY_TIMEOUT);
              }
            }, 3000); // 3 segundos após o Enter inicial
          }, 2000); // 2 segundos para inicialização
        }).catch(error => {
          Logger.error('Erro ao criar shell interativo para Huawei:', error);
          cleanup();
          reject(error);
        });
      } catch (error) {
        Logger.error('Erro no shell interativo para Huawei:', error);
        cleanup();
        reject(error);
      }
    });
  }
}

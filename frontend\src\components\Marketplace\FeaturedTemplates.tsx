import React from 'react'
import { 
  Star, 
  Download, 
  Heart, 
  User, 
  TrendingUp,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { MarketplaceTemplate, MarketplaceService } from '../../services/marketplaceService'

interface FeaturedTemplatesProps {
  templates: MarketplaceTemplate[]
  onDownload: (templateId: string) => Promise<void>
  onLike: (templateId: string) => Promise<void>
  onFavorite: (templateId: string) => Promise<void>
}

const FeaturedTemplates: React.FC<FeaturedTemplatesProps> = ({
  templates,
  onDownload,
  onLike,
  onFavorite
}) => {
  const [currentIndex, setCurrentIndex] = React.useState(0)
  const itemsPerPage = 3

  const nextSlide = () => {
    setCurrentIndex((prev) => 
      prev + itemsPerPage >= templates.length ? 0 : prev + itemsPerPage
    )
  }

  const prevSlide = () => {
    setCurrentIndex((prev) => 
      prev === 0 ? Math.max(0, templates.length - itemsPerPage) : Math.max(0, prev - itemsPerPage)
    )
  }

  const visibleTemplates = templates.slice(currentIndex, currentIndex + itemsPerPage)

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  const renderStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    
    for (let i = 0; i < 5; i++) {
      stars.push(
        <Star 
          key={i} 
          className={`h-4 w-4 ${
            i < fullStars 
              ? 'fill-yellow-400 text-yellow-400' 
              : 'text-gray-300'
          }`} 
        />
      )
    }
    
    return stars
  }

  if (templates.length === 0) {
    return null
  }

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <TrendingUp className="h-6 w-6 mr-2 text-blue-600" />
            Templates em Destaque
          </h2>
          <p className="text-gray-600 mt-1">
            Os templates mais populares e bem avaliados da comunidade
          </p>
        </div>
        
        {templates.length > itemsPerPage && (
          <div className="flex items-center space-x-2">
            <button
              onClick={prevSlide}
              className="p-2 rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow text-gray-600 hover:text-gray-900"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            <button
              onClick={nextSlide}
              className="p-2 rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow text-gray-600 hover:text-gray-900"
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {visibleTemplates.map((template) => (
          <div
            key={template.id}
            className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 overflow-hidden"
          >
            {/* Header with Featured Badge */}
            <div className="relative">
              <div className="absolute top-4 right-4 z-10">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                  ⭐ Destaque
                </span>
              </div>
              
              <div className="bg-gradient-to-br from-blue-500 to-purple-600 h-24 flex items-center justify-center">
                <div className="text-white text-center">
                  <div className="text-2xl mb-1">
                    {MarketplaceService.getCategoryIcon(template.category || 'Other')}
                  </div>
                  <div className="text-xs font-medium opacity-90">
                    {template.category || 'Template'}
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6">
              {/* Title and Description */}
              <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                {template.name}
              </h3>
              
              {template.description && (
                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                  {template.description}
                </p>
              )}

              {/* Stats Row */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <Download className="h-4 w-4 mr-1" />
                    {formatNumber(template.downloads)}
                  </div>
                  <div className="flex items-center">
                    <Heart className={`h-4 w-4 mr-1 ${template.userInteraction?.hasLiked ? 'fill-red-500 text-red-500' : ''}`} />
                    {formatNumber(template.likes)}
                  </div>
                </div>
                
                {template.stats.averageRating > 0 && (
                  <div className="flex items-center">
                    <div className="flex items-center mr-2">
                      {renderStars(template.stats.averageRating)}
                    </div>
                    <span className="text-sm text-gray-600">
                      {template.stats.averageRating.toFixed(1)}
                    </span>
                  </div>
                )}
              </div>

              {/* Author */}
              <div className="flex items-center text-sm text-gray-500 mb-4">
                <User className="h-4 w-4 mr-1" />
                <span>por {template.user.name}</span>
              </div>

              {/* Tags */}
              {template.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-4">
                  {template.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700"
                    >
                      {tag}
                    </span>
                  ))}
                  {template.tags.length > 3 && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-500">
                      +{template.tags.length - 3}
                    </span>
                  )}
                </div>
              )}

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => onLike(template.id)}
                    className={`p-2 rounded-lg transition-colors ${
                      template.userInteraction?.hasLiked
                        ? 'bg-red-100 text-red-600 hover:bg-red-200'
                        : 'bg-gray-100 text-gray-400 hover:text-red-500 hover:bg-red-50'
                    }`}
                    title="Curtir"
                  >
                    <Heart className={`h-4 w-4 ${template.userInteraction?.hasLiked ? 'fill-current' : ''}`} />
                  </button>
                  
                  <button
                    onClick={() => onFavorite(template.id)}
                    className={`p-2 rounded-lg transition-colors ${
                      template.userInteraction?.hasFavorited
                        ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                        : 'bg-gray-100 text-gray-400 hover:text-blue-500 hover:bg-blue-50'
                    }`}
                    title="Favoritar"
                  >
                    <Star className={`h-4 w-4 ${template.userInteraction?.hasFavorited ? 'fill-current' : ''}`} />
                  </button>
                </div>
                
                <button
                  onClick={() => onDownload(template.id)}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  <Download className="h-4 w-4 mr-1" />
                  Baixar
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination Dots */}
      {templates.length > itemsPerPage && (
        <div className="flex items-center justify-center mt-6 space-x-2">
          {Array.from({ length: Math.ceil(templates.length / itemsPerPage) }).map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index * itemsPerPage)}
              className={`w-2 h-2 rounded-full transition-colors ${
                Math.floor(currentIndex / itemsPerPage) === index
                  ? 'bg-blue-600'
                  : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default FeaturedTemplates

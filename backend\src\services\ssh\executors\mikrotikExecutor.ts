import { NodeSSH } from 'node-ssh';
import { RouterOSAPI } from 'node-routeros-v2';
import { CommandResult } from '../../../types/server';
import { Logger } from '../../../utils/logger';
import { BaseExecutor } from './baseExecutor';

/**
 * Executor de comandos para dispositivos Mikrotik
 * Implementação híbrida que usa API nativa do Mikrotik quando possível
 * e fallback para SSH quando necessário
 */
export class MikrotikExecutor extends BaseExecutor {
  private routerOsApi: RouterOSAPI | null = null;
  private apiConnected: boolean = false;
  private host: string = '';
  private username: string = '';
  private password: string = '';
  private port: number = 8728; // Porta padrão da API Mikrotik
  private connectionAttempts: number = 0;
  private maxConnectionAttempts: number = 3;

  constructor(ssh: NodeSSH) {
    super(ssh);

    // Configurar timeouts para Mikrotik
    this.BASE_TIMEOUT = 60000; // 60 segundos como base
    this.TIMEOUT_PER_COMMAND = 10000; // 10 segundos adicionais por comando
    this.MAX_TIMEOUT = 180000; // Limite máximo de 3 minutos

    // Extrair informações de conexão do SSH para usar na API
    if (ssh.connection) {
      const config = ssh.connection.config;
      this.host = config.host || '';
      this.username = config.username || '';
      this.password = config.password || '';

      // Inicializar a API do RouterOS
      this.initializeApi();
    }

    // Configurar handler para limpar recursos quando o SSH for encerrado
    ssh.connection?.on('close', () => {
      this.closeApi();
    });
  }

  /**
   * Fecha a conexão com a API do RouterOS
   */
  public async closeApi(): Promise<void> {
    if (this.routerOsApi && this.apiConnected) {
      try {
        Logger.log(`Fechando conexão API Mikrotik para ${this.host}`);
        await this.routerOsApi.close();
        this.apiConnected = false;
      } catch (error) {
        Logger.error('Erro ao fechar conexão API Mikrotik:', error);
      }
    }
  }

  /**
   * Inicializa a API do RouterOS
   */
  private initializeApi(): void {
    try {
      this.routerOsApi = new RouterOSAPI({
        host: this.host,
        user: this.username,
        password: this.password,
        port: this.port,
        keepalive: true,
        timeout: 30000
      });

      Logger.log(`API Mikrotik inicializada para ${this.host}`);
    } catch (error) {
      Logger.error('Erro ao inicializar API Mikrotik:', error);
      this.routerOsApi = null;
    }
  }

  /**
   * Conecta à API do RouterOS
   */
  private async connectApi(): Promise<boolean> {
    if (!this.routerOsApi) {
      this.initializeApi();
      if (!this.routerOsApi) {
        return false;
      }
    }

    try {
      if (this.apiConnected) {
        // Verificar se a conexão ainda está ativa
        try {
          // Teste simples para verificar se a conexão está ativa
          await this.routerOsApi.write('/system/identity/print');
          return true;
        } catch (error) {
          Logger.log('Conexão API Mikrotik perdida, reconectando...');
          this.apiConnected = false;
        }
      }

      this.connectionAttempts++;
      Logger.log(`Conectando à API Mikrotik (${this.host}) - Tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts}`);

      // Criar uma promise com timeout de 5 segundos
      const connectPromise = this.routerOsApi.connect();
      const timeoutPromise = new Promise<void>((_, reject) => {
        setTimeout(() => {
          reject(new Error('API_TIMEOUT'));
        }, 5000); // 5 segundos
      });

      // Executar com timeout
      await Promise.race([connectPromise, timeoutPromise]);

      this.apiConnected = true;
      this.connectionAttempts = 0;

      Logger.log(`Conexão API Mikrotik estabelecida com sucesso: ${this.host}`);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage === 'API_TIMEOUT') {
        Logger.error(`Timeout ao conectar à API Mikrotik (tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts})`);
      } else {
        Logger.error(`Falha ao conectar à API Mikrotik (tentativa ${this.connectionAttempts}/${this.maxConnectionAttempts}):`, error);
      }

      if (this.connectionAttempts >= this.maxConnectionAttempts) {
        this.connectionAttempts = 0;
        Logger.log('Número máximo de tentativas de conexão API Mikrotik excedido, usando SSH como fallback');
        return false;
      }

      return false;
    }
  }

  /**
   * Executa um comando em um dispositivo Mikrotik
   * @param command Comando a ser executado
   * @returns Resultado do comando
   */
  async executeCommand(command: string): Promise<CommandResult> {
    try {
      Logger.log('Executando comando Mikrotik:', command);

      // Verificar se o comando contém múltiplas linhas
      if (command.includes('\n')) {
        Logger.log('Detectado comando multilinhas para Mikrotik');
        return await this.executeMultilineCommand(command);
      }

      // Limpar o comando para remover caracteres invisíveis e espaços extras
      const cleanCommand = command.trim().replace(/\s+/g, ' ');

      // Adicionar / no início se o comando não começar com /
      const formattedCommand = cleanCommand.startsWith('/') ? cleanCommand : `/${cleanCommand}`;

      Logger.log(`Executando comando Mikrotik formatado: ${formattedCommand}`);

      // Tentar usar a API nativa do Mikrotik
      const apiConnected = await this.connectApi();
      if (apiConnected) {
        try {
          Logger.log('Usando API nativa do Mikrotik para executar comando');

          // Dividir o comando em partes para a API
          // Exemplo: /ip/address/print -> ['/ip/address/print']
          // Exemplo: /ip address print -> ['/ip/address/print']
          const commandParts = formattedCommand.split(' ');
          const apiCommand = commandParts[0].replace(/ /g, '/');

          // Extrair parâmetros se houver
          const params: string[] = [];
          if (commandParts.length > 1) {
            for (let i = 1; i < commandParts.length; i++) {
              params.push(commandParts[i]);
            }
          }

          // Executar o comando via API
          const result = await this.routerOsApi!.write(apiCommand, params);

          // Converter o resultado para o formato esperado
          const formattedResult = this.formatApiResult(result);

          return {
            stdout: formattedResult,
            stderr: '',
            code: 0
          };
        } catch (error) {
          Logger.error('Erro ao executar comando via API Mikrotik, usando SSH como fallback:', error);
          // Continuar para usar SSH como fallback
        }
      }

      // Fallback para SSH se a API falhar
      Logger.log('Usando SSH como fallback para executar comando Mikrotik');

      // Calcular o comando sem a barra inicial para SSH
      const cmd = formattedCommand.startsWith('/') ? formattedCommand.slice(1) : formattedCommand;

      // Configurar opções SSH com PTY
      const sshOptions = {
        execOptions: { pty: true },          // aloca PTY no Mikrotik
        onStdout: (chunk: any) => {
          // Limpar caracteres de controle ANSI e formatar a saída
          const cleanOutput = this.cleanAnsiOutput(chunk.toString());
          Logger.log(`stdout Mikrotik (${chunk.length} bytes): ${cleanOutput}`);
        },
        onStderr: (chunk: any) => {
          // Limpar caracteres de controle ANSI e formatar a saída
          const cleanOutput = this.cleanAnsiOutput(chunk.toString());
          Logger.error(`stderr Mikrotik (${chunk.length} bytes): ${cleanOutput}`);
        }
      };

      // Executar o comando via SSH
      const result = await this.ssh.execCommand(cmd, sshOptions);

      // Limpar a saída final também
      const cleanStdout = this.cleanAnsiOutput(result.stdout);
      const cleanStderr = this.cleanAnsiOutput(result.stderr);

      // Formatar a saída para melhor legibilidade
      const formattedOutput = this.formatMikrotikOutput(cleanStdout);

      return {
        stdout: formattedOutput,
        stderr: cleanStderr,
        code: result.code || 0
      };
    } catch (error) {
      Logger.error('Erro ao executar comando Mikrotik:', error);

      // Verificar se é um erro de keepalive timeout
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('keepalive') ||
          errorMessage.includes('timeout') ||
          errorMessage.includes('timed out')) {
        Logger.log('Detectado erro de keepalive ou timeout, sinalizando necessidade de reconexão');
        throw new Error('RECONNECT_NEEDED: Keepalive timeout detectado, reconexão necessária');
      }

      throw error;
    }
  }

  /**
   * Executa um comando com múltiplas linhas
   * @param command Comando com múltiplas linhas
   * @returns Resultado do comando
   */
  async executeMultilineCommand(command: string): Promise<CommandResult> {
    try {
      // Dividir o comando em linhas individuais
      const lines = command.split('\n').filter(line => line.trim() !== '');
      const commandCount = lines.length;

      // Calcular o timeout dinâmico com base na quantidade de comandos
      const dynamicTimeout = this.calculateDynamicTimeout(commandCount);

      Logger.log(`Executando ${commandCount} comandos Mikrotik separados com timeout dinâmico de ${dynamicTimeout}ms`);

      // Tentar usar a API nativa do Mikrotik
      if (await this.connectApi()) {
        try {
          Logger.log('Usando API nativa do Mikrotik para executar comandos múltiplos');

          let combinedOutput = '';

          // Executar cada linha separadamente via API
          for (let i = 0; i < commandCount; i++) {
            const line = lines[i].trim();
            Logger.log(`Executando linha ${i+1}/${commandCount} via API: ${line}`);

            // Formatar o comando para a API
            const cleanCommand = line.replace(/\s+/g, ' ');
            const formattedCommand = cleanCommand.startsWith('/') ? cleanCommand : `/${cleanCommand}`;

            // Dividir o comando em partes para a API
            const commandParts = formattedCommand.split(' ');
            const apiCommand = commandParts[0].replace(/ /g, '/');

            // Extrair parâmetros se houver
            const params: string[] = [];
            if (commandParts.length > 1) {
              for (let j = 1; j < commandParts.length; j++) {
                params.push(commandParts[j]);
              }
            }

            try {
              // Executar o comando via API
              const result = await this.routerOsApi!.write(apiCommand, params);

              // Adicionar o resultado formatado à saída combinada
              const formattedResult = this.formatApiResult(result);
              combinedOutput += `=== Comando: ${formattedCommand} ===\n${formattedResult}\n\n`;
            } catch (error) {
              Logger.error(`Erro ao executar linha ${i+1}/${commandCount} via API:`, error);
              combinedOutput += `=== Comando: ${formattedCommand} ===\nErro: ${error instanceof Error ? error.message : 'Erro desconhecido'}\n\n`;
            }
          }

          return {
            stdout: combinedOutput,
            stderr: '',
            code: 0
          };
        } catch (error) {
          Logger.error('Erro ao executar comandos múltiplos via API Mikrotik, usando SSH como fallback:', error);
          // Continuar para usar SSH como fallback
        }
      }

      // Fallback para SSH se a API falhar
      Logger.log('Usando SSH como fallback para executar comandos múltiplos Mikrotik');

      // Criar uma promise com timeout global para todos os comandos
      const timeoutPromise = new Promise<CommandResult>((resolve) => {
        setTimeout(() => {
          resolve({
            stdout: '',
            stderr: `[ERRO] Timeout global atingido após ${dynamicTimeout/1000} segundos para ${commandCount} comandos`,
            code: 124 // Código de timeout
          });
        }, dynamicTimeout);
      });

      // Promise para executar todos os comandos
      const executePromise = async (): Promise<CommandResult> => {
        let combinedOutput = '';
        let combinedError = '';
        let lastCode = 0;

        // Executar cada linha separadamente
        for (let i = 0; i < commandCount; i++) {
          const line = lines[i].trim();
          Logger.log(`Executando linha ${i+1}/${commandCount}: ${line}`);

          // Formatar o comando para SSH
          const cleanCommand = line.replace(/\s+/g, ' ');
          const formattedCommand = cleanCommand.startsWith('/') ? cleanCommand : `/${cleanCommand}`;

          try {
            // Retira a barra inicial
            const cmd = formattedCommand.startsWith('/')
              ? formattedCommand.slice(1)
              : formattedCommand;
            const result = await this.ssh.execCommand(cmd, {
              execOptions: { pty: true },
              onStdout: (chunk: any) => {
                // Limpar caracteres de controle ANSI e formatar a saída
                const cleanOutput = this.cleanAnsiOutput(chunk.toString());
                Logger.log(`stdout linha ${i+1}/${commandCount}: ${cleanOutput}`);
              },
              onStderr: (chunk: any) => {
                // Limpar caracteres de controle ANSI e formatar a saída
                const cleanOutput = this.cleanAnsiOutput(chunk.toString());
                Logger.error(`stderr linha ${i+1}/${commandCount}: ${cleanOutput}`);
              }
            });

            // Limpar e formatar a saída
            const cleanStdout = this.cleanAnsiOutput(result.stdout);
            const formattedStdout = this.formatMikrotikOutput(cleanStdout);
            combinedOutput += `=== Comando: ${formattedCommand} ===\n${formattedStdout}\n\n`;

            if (result.stderr) {
              const cleanStderr = this.cleanAnsiOutput(result.stderr);
              combinedError += `=== Erro em: ${formattedCommand} ===\n${cleanStderr}\n\n`;
            }

            lastCode = result.code || 0;
          } catch (error) {
            Logger.error(`Erro ao executar linha ${i+1}/${commandCount}:`, error);
            combinedError += `=== Erro em: ${formattedCommand} ===\n${error instanceof Error ? error.message : 'Erro desconhecido'}\n\n`;
            lastCode = 1;
          }
        }

        return {
          stdout: combinedOutput,
          stderr: combinedError,
          code: lastCode
        };
      };

      // Executar com timeout
      return Promise.race([executePromise(), timeoutPromise]);
    } catch (error) {
      Logger.error('Erro ao executar comandos múltiplos Mikrotik:', error);
      throw error;
    }
  }

  /**
   * Formata o resultado da API para um formato legível
   * @param result Resultado da API
   * @returns String formatada
   */
  private formatApiResult(result: any[]): string {
    if (!result || result.length === 0) {
      return 'Comando executado com sucesso. Sem saída.';
    }

    try {
      // Tentar formatar o resultado de forma legível
      let output = '';

      result.forEach((item, index) => {
        output += `--- Item ${index + 1} ---\n`;

        // Adicionar cada propriedade do item
        Object.entries(item).forEach(([key, value]) => {
          // Ignorar propriedades internas que começam com .
          if (!key.startsWith('.')) {
            output += `${key}: ${value}\n`;
          }
        });

        output += '\n';
      });

      return output;
    } catch (error) {
      Logger.error('Erro ao formatar resultado da API:', error);
      // Fallback para JSON stringificado
      return JSON.stringify(result, null, 2);
    }
  }

  /**
   * Limpa caracteres de controle ANSI da saída do terminal
   * @param output Saída do terminal
   * @returns Saída limpa
   */
  private cleanAnsiOutput(output: string): string {
    try {
      if (!output) return '';

      // Remover códigos de cores ANSI e outros caracteres de controle
      const ansiRegex = /\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])/g;
      let cleanOutput = output.replace(ansiRegex, '');

      // Remover caracteres de controle adicionais específicos do Mikrotik
      cleanOutput = cleanOutput.replace(/\[m/g, '');
      cleanOutput = cleanOutput.replace(/\[K/g, '');
      cleanOutput = cleanOutput.replace(/\[9999B/g, '');
      cleanOutput = cleanOutput.replace(/\[9999D/g, '');
      cleanOutput = cleanOutput.replace(/\[9999C/g, '');

      // Remover sequências específicas do Mikrotik
      cleanOutput = cleanOutput.replace(/\[m\s*\[m/g, '');
      cleanOutput = cleanOutput.replace(/\[1m\s*\[m/g, '');

      // Remover caracteres não imprimíveis
      cleanOutput = cleanOutput.replace(/[\x00-\x09\x0B-\x0C\x0E-\x1F\x7F-\x9F]/g, '');

      // Tratar caracteres específicos do Mikrotik
      cleanOutput = cleanOutput.replace(/\[[\d;]*[a-zA-Z]/g, '');

      // Processar linha por linha para melhor formatação
      const lines = cleanOutput.split('\n');
      const processedLines = lines.map(line => {
        // Remover espaços extras e caracteres de controle residuais
        let processedLine = line.trim()
          .replace(/\s+/g, ' ')
          .replace(/\[\d+[A-Z]/g, '')
          .replace(/\[\d+;\d+[A-Z]/g, '');

        return processedLine;
      }).filter(line => line.length > 0); // Remover linhas vazias

      // Juntar as linhas novamente
      cleanOutput = processedLines.join('\n');

      // Remover linhas vazias duplicadas
      cleanOutput = cleanOutput.replace(/\n\s*\n/g, '\n');

      return cleanOutput;
    } catch (error) {
      Logger.error('Erro ao limpar saída ANSI:', error);
      return output; // Retornar a saída original em caso de erro
    }
  }

  /**
   * Formata a saída do Mikrotik para melhor legibilidade
   * @param output Saída limpa do Mikrotik
   * @returns Saída formatada
   */
  private formatMikrotikOutput(output: string): string {
    try {
      if (!output) return '';

      // Dividir em linhas para processamento
      const lines = output.split('\n');

      // Detectar se é uma tabela
      const isTable = lines.some(line =>
        line.includes('NAME') &&
        line.includes('TYPE') &&
        (line.includes('MTU') || line.includes('ACTUAL-MTU'))
      );

      if (isTable) {
        // Formatar como tabela
        return this.formatMikrotikTable(lines);
      }

      // Formatar saída padrão
      const formattedLines = lines.map(line => {
        // Remover caracteres estranhos e espaços extras
        return line.trim();
      });

      return formattedLines.join('\n');
    } catch (error) {
      Logger.error('Erro ao formatar saída Mikrotik:', error);
      return output; // Retornar a saída original em caso de erro
    }
  }

  /**
   * Formata uma tabela do Mikrotik
   * @param lines Linhas da tabela
   * @returns Tabela formatada
   */
  private formatMikrotikTable(lines: string[]): string {
    try {
      // Encontrar a linha de cabeçalho
      const headerIndex = lines.findIndex(line =>
        line.includes('NAME') &&
        (line.includes('TYPE') || line.includes('MTU'))
      );

      if (headerIndex === -1) {
        // Não é uma tabela, retornar as linhas originais
        return lines.join('\n');
      }

      // Extrair cabeçalho
      const headerLine = lines[headerIndex];

      // Extrair nomes das colunas
      const columnNames: string[] = [];
      const headerMatches = headerLine.match(/[A-Z\-]+/g);

      if (headerMatches) {
        columnNames.push(...headerMatches);
      }

      // Se não encontrou colunas, retornar as linhas originais
      if (columnNames.length === 0) {
        return lines.join('\n');
      }

      // Formatar como tabela
      let formattedOutput = '';

      // Adicionar cabeçalho formatado
      formattedOutput += columnNames.join('\t') + '\n';
      formattedOutput += '-'.repeat(columnNames.join('\t').length) + '\n';

      // Adicionar linhas de dados
      for (let i = headerIndex + 1; i < lines.length; i++) {
        const line = lines[i].trim();

        // Pular linhas vazias
        if (!line) continue;

        // Extrair valores
        const values = line.split(/\s{2,}/);

        // Adicionar linha formatada
        if (values.length > 0) {
          formattedOutput += values.join('\t') + '\n';
        }
      }

      return formattedOutput;
    } catch (error) {
      Logger.error('Erro ao formatar tabela Mikrotik:', error);
      return lines.join('\n'); // Retornar as linhas originais em caso de erro
    }
  }
}

{"name": "remoteops-backend", "version": "2.0.0", "description": "RemoteOps - Backend da plataforma de gerenciamento de infraestrutura remota", "main": "src/server.ts", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "prisma:seed": "ts-node prisma/seed.ts"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "keywords": ["ssh", "remote-management", "devops", "infrastructure", "automation", "terminal", "server-management"], "author": "RemoteOps Team", "license": "MIT", "dependencies": {"@fastify/cors": "^8.0.0", "@fastify/jwt": "^7.0.0", "@prisma/client": "^6.8.2", "@types/react-beautiful-dnd": "^13.1.8", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "dotenv": "^16.0.0", "fastify": "^4.0.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.0", "node-fetch": "^3.3.2", "node-routeros-v2": "^1.6.12", "node-ssh": "^13.0.0", "pino-pretty": "^13.0.0", "react-beautiful-dnd": "^13.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.0.0", "zod": "^3.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jest": "^29.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^18.19.79", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.0.0", "prisma": "^6.8.2", "supertest": "^6.3.3", "ts-jest": "^29.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/server.ts", "!src/scripts/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "setupFilesAfterEnv": ["<rootDir>/src/__tests__/setup.ts"]}}
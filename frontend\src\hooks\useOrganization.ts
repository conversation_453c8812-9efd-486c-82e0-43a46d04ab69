import { useState, useEffect, useCallback, useContext, createContext } from 'react'
import { OrganizationService, Organization, OrganizationWithStats, OrganizationLimits } from '../services/organizationService'
import toast from 'react-hot-toast'

interface OrganizationContextType {
  // Estado
  currentOrganization: OrganizationWithStats | null
  userOrganizations: Organization[]
  limits: OrganizationLimits | null
  isLoading: boolean
  
  // Ações
  createOrganization: (data: any) => Promise<void>
  switchOrganization: (organizationId: string) => Promise<void>
  inviteUser: (data: any) => Promise<void>
  removeUser: (userId: string) => Promise<void>
  updateOrganization: (data: any) => Promise<void>
  refreshOrganization: () => Promise<void>
  checkLimits: () => Promise<void>
  
  // Utilitários
  canAddServer: boolean
  canAddUser: boolean
  isInTrial: boolean
  trialDaysRemaining: number
  usagePercentages: {
    servers: number
    users: number
  }
}

const OrganizationContext = createContext<OrganizationContextType | null>(null)

export const useOrganization = () => {
  const context = useContext(OrganizationContext)
  if (!context) {
    throw new Error('useOrganization deve ser usado dentro de OrganizationProvider')
  }
  return context
}

export const useOrganizationHook = () => {
  const [currentOrganization, setCurrentOrganization] = useState<OrganizationWithStats | null>(null)
  const [userOrganizations, setUserOrganizations] = useState<Organization[]>([])
  const [limits, setLimits] = useState<OrganizationLimits | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Carrega organização atual
  const loadCurrentOrganization = useCallback(async () => {
    try {
      setIsLoading(true)
      const org = await OrganizationService.getCurrentOrganization()
      setCurrentOrganization(org)
    } catch (error) {
      console.error('Erro ao carregar organização atual:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Carrega organizações do usuário
  const loadUserOrganizations = useCallback(async () => {
    try {
      const orgs = await OrganizationService.getUserOrganizations()
      setUserOrganizations(orgs)
    } catch (error) {
      console.error('Erro ao carregar organizações do usuário:', error)
    }
  }, [])

  // Carrega limites da organização
  const loadLimits = useCallback(async () => {
    try {
      if (currentOrganization) {
        const orgLimits = await OrganizationService.checkLimits()
        setLimits(orgLimits)
      }
    } catch (error) {
      console.error('Erro ao carregar limites:', error)
    }
  }, [currentOrganization])

  // Cria nova organização
  const createOrganization = useCallback(async (data: any) => {
    try {
      setIsLoading(true)
      await OrganizationService.createOrganization(data)
      toast.success('Organização criada com sucesso!')
      await loadCurrentOrganization()
      await loadUserOrganizations()
    } catch (error: any) {
      console.error('Erro ao criar organização:', error)
      toast.error(error.response?.data?.message || 'Erro ao criar organização')
    } finally {
      setIsLoading(false)
    }
  }, [loadCurrentOrganization, loadUserOrganizations])

  // Troca de organização
  const switchOrganization = useCallback(async (organizationId: string) => {
    try {
      setIsLoading(true)
      await OrganizationService.switchOrganization(organizationId)
      toast.success('Organização alterada com sucesso!')
      await loadCurrentOrganization()
      // Recarregar a página para atualizar todos os dados
      window.location.reload()
    } catch (error: any) {
      console.error('Erro ao trocar organização:', error)
      toast.error(error.response?.data?.message || 'Erro ao trocar organização')
    } finally {
      setIsLoading(false)
    }
  }, [loadCurrentOrganization])

  // Convida usuário
  const inviteUser = useCallback(async (data: any) => {
    try {
      await OrganizationService.inviteUser(data)
      toast.success('Convite enviado com sucesso!')
      await loadCurrentOrganization()
    } catch (error: any) {
      console.error('Erro ao convidar usuário:', error)
      toast.error(error.response?.data?.message || 'Erro ao enviar convite')
    }
  }, [loadCurrentOrganization])

  // Remove usuário
  const removeUser = useCallback(async (userId: string) => {
    try {
      await OrganizationService.removeUser(userId)
      toast.success('Usuário removido com sucesso!')
      await loadCurrentOrganization()
    } catch (error: any) {
      console.error('Erro ao remover usuário:', error)
      toast.error(error.response?.data?.message || 'Erro ao remover usuário')
    }
  }, [loadCurrentOrganization])

  // Atualiza organização
  const updateOrganization = useCallback(async (data: any) => {
    try {
      await OrganizationService.updateOrganization(data)
      toast.success('Organização atualizada com sucesso!')
      await loadCurrentOrganization()
    } catch (error: any) {
      console.error('Erro ao atualizar organização:', error)
      toast.error(error.response?.data?.message || 'Erro ao atualizar organização')
    }
  }, [loadCurrentOrganization])

  // Refresh organização
  const refreshOrganization = useCallback(async () => {
    await loadCurrentOrganization()
    await loadLimits()
  }, [loadCurrentOrganization, loadLimits])

  // Check limites
  const checkLimits = useCallback(async () => {
    await loadLimits()
  }, [loadLimits])

  // Carrega dados iniciais
  useEffect(() => {
    loadCurrentOrganization()
    loadUserOrganizations()
  }, [loadCurrentOrganization, loadUserOrganizations])

  // Carrega limites quando organização muda
  useEffect(() => {
    if (currentOrganization) {
      loadLimits()
    }
  }, [currentOrganization, loadLimits])

  // Computed values
  const canAddServer = limits?.canAddServer ?? true
  const canAddUser = limits?.canAddUser ?? true
  
  const isInTrial = currentOrganization ? 
    OrganizationService.isInTrial(currentOrganization) : false
  
  const trialDaysRemaining = currentOrganization ? 
    OrganizationService.getTrialDaysRemaining(currentOrganization.trialEndsAt) : 0

  const usagePercentages = {
    servers: currentOrganization ? 
      OrganizationService.getUsagePercentage(
        currentOrganization._count.servers, 
        currentOrganization.maxServers
      ) : 0,
    users: currentOrganization ? 
      OrganizationService.getUsagePercentage(
        currentOrganization._count.users, 
        currentOrganization.maxUsers
      ) : 0
  }

  return {
    // Estado
    currentOrganization,
    userOrganizations,
    limits,
    isLoading,
    
    // Ações
    createOrganization,
    switchOrganization,
    inviteUser,
    removeUser,
    updateOrganization,
    refreshOrganization,
    checkLimits,
    
    // Utilitários
    canAddServer,
    canAddUser,
    isInTrial,
    trialDaysRemaining,
    usagePercentages
  }
}

// Provider component
export const OrganizationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const organizationData = useOrganizationHook()
  
  return (
    <OrganizationContext.Provider value={organizationData}>
      {children}
    </OrganizationContext.Provider>
  )
}

export default useOrganization

import React, { useState } from 'react'
import {
  BarChart3,
  <PERSON><PERSON>ding<PERSON>p,
  Clock,
  AlertCircle,
  CheckCircle,
  Info,
  Server,
  Command,
  Eye,
  EyeOff
} from 'lucide-react'
import CacheStats from './CacheStats'
import CacheControls from './CacheControls'
import CacheEntriesTable from './CacheEntriesTable'
import { useCache } from '../../hooks/useCache'
import { PopularCommand } from '../../services/cacheService'

const CacheDashboard: React.FC = () => {
  const {
    stats,
    popularCommands,
    config,
    metrics,
    redisHealth,
    serverPerformance,
    recommendations,
    isLoading,
    isRefreshing,
    refreshStats,
    clearCache,
    invalidateServer,
    invalidatePattern,
    updateConfig,
    exportConfig,
    importConfig,
    applyOptimizations,
    autoRefresh,
    setAutoRefresh,
    refreshInterval,
    setRefreshInterval
  } = useCache()

  const [activeTab, setActiveTab] = useState<'overview' | 'commands' | 'servers' | 'recommendations' | 'entries'>('overview')
  const [showDetails, setShowDetails] = useState<{ [key: string]: boolean }>({})

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatTime = (timestamp: string): string => {
    return new Date(timestamp).toLocaleString('pt-BR')
  }

  const toggleDetails = (key: string) => {
    setShowDetails(prev => ({ ...prev, [key]: !prev[key] }))
  }

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'warning': return <AlertCircle className="h-5 w-5 text-yellow-500" />
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />
      default: return <Info className="h-5 w-5 text-blue-500" />
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-blue-100 text-blue-800'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Gerenciamento de Cache</h1>
          <p className="text-gray-600">
            Monitore e gerencie o cache distribuído do sistema
          </p>
        </div>

        <div className="flex items-center space-x-4">
          {/* Auto-refresh toggle */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`p-2 rounded-lg ${autoRefresh ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}`}
            >
              {autoRefresh ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
            </button>
            <select
              value={refreshInterval}
              onChange={(e) => setRefreshInterval(parseInt(e.target.value))}
              className="text-sm border border-gray-300 rounded-md px-2 py-1"
            >
              <option value={10000}>10s</option>
              <option value={30000}>30s</option>
              <option value={60000}>1m</option>
              <option value={300000}>5m</option>
            </select>
          </div>

          {isRefreshing && (
            <div className="flex items-center text-sm text-gray-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              Atualizando...
            </div>
          )}
        </div>
      </div>

      {/* Controles */}
      <CacheControls
        config={config}
        isLoading={isLoading}
        onClearCache={clearCache}
        onInvalidateServer={invalidateServer}
        onInvalidatePattern={invalidatePattern}
        onUpdateConfig={updateConfig}
        onRefreshStats={refreshStats}
        onExportConfig={exportConfig}
        onImportConfig={importConfig}
        onApplyOptimizations={applyOptimizations}
      />

      {/* Estatísticas */}
      <CacheStats
        stats={stats}
        metrics={metrics}
        redisHealth={redisHealth}
        isLoading={isLoading}
      />

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Visão Geral', icon: BarChart3 },
              { id: 'commands', label: 'Comandos Populares', icon: Command },
              { id: 'servers', label: 'Performance por Servidor', icon: Server },
              { id: 'entries', label: 'Entradas do Cache', icon: Eye },
              { id: 'recommendations', label: 'Recomendações', icon: TrendingUp }
            ].map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              )
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* Tab: Visão Geral */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Distribuição por Servidor */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Distribuição por Servidor
                  </h3>
                  <div className="space-y-3">
                    {metrics?.serverDistribution?.slice(0, 5).map((server, index) => (
                      <div key={server.serverId} className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className={`w-3 h-3 rounded-full mr-3 bg-blue-${(index + 1) * 100}`}></div>
                          <span className="text-sm font-medium text-gray-900">
                            {server.serverName || server.serverId}
                          </span>
                        </div>
                        <span className="text-sm text-gray-600">
                          {server.keyCount} chaves
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Métricas de Tempo */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    Métricas de Performance
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Hit Rate:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {((stats?.hitRate || 0) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Miss Rate:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {((metrics?.missRate || 0) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Tempo Médio:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {metrics?.averageResponseTime?.toFixed(0) || 0}ms
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Evictions:</span>
                      <span className="text-sm font-medium text-gray-900">
                        {stats?.evictions || 0}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Tab: Comandos Populares */}
          {activeTab === 'commands' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                Comandos Mais Utilizados
              </h3>
              <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table className="min-w-full divide-y divide-gray-300">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Comando
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Hits
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Servidor
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Último Uso
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {popularCommands.map((cmd, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                            {cmd.command.length > 50 ? `${cmd.command.substring(0, 50)}...` : cmd.command}
                          </code>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {cmd.hits}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {cmd.serverName || cmd.serverId}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatTime(cmd.lastUsed)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Tab: Performance por Servidor */}
          {activeTab === 'servers' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">
                Performance por Servidor
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {serverPerformance.map((server) => (
                  <div key={server.serverId} className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900">
                        {server.serverName || server.serverId}
                      </h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        server.cacheHitRate >= 0.8
                          ? 'bg-green-100 text-green-800'
                          : server.cacheHitRate >= 0.6
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {(server.cacheHitRate * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Comandos Total:</span>
                        <span className="font-medium">{server.totalCommands}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Comandos Cached:</span>
                        <span className="font-medium">{server.cachedCommands}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tempo Médio:</span>
                        <span className="font-medium">{server.averageResponseTime.toFixed(0)}ms</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Tab: Entradas do Cache */}
          {activeTab === 'entries' && (
            <div className="space-y-4">
              <CacheEntriesTable />
            </div>
          )}

          {/* Tab: Recomendações */}
          {activeTab === 'recommendations' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">
                  Recomendações de Otimização
                </h3>
                <button
                  onClick={applyOptimizations}
                  disabled={isLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 text-sm"
                >
                  Aplicar Todas
                </button>
              </div>

              <div className="space-y-3">
                {recommendations.map((rec, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      {getRecommendationIcon(rec.type)}
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium text-gray-900">{rec.title}</h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getImpactColor(rec.impact)}`}>
                            {rec.impact === 'high' ? 'Alto' : rec.impact === 'medium' ? 'Médio' : 'Baixo'} Impacto
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{rec.description}</p>
                        {rec.action && (
                          <button className="text-sm text-blue-600 hover:text-blue-800 mt-2">
                            {rec.action}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {recommendations.length === 0 && (
                  <div className="text-center py-8">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Tudo Otimizado!
                    </h3>
                    <p className="text-gray-600">
                      Não há recomendações de otimização no momento.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CacheDashboard

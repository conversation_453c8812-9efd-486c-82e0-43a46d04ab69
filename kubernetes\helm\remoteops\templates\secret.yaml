{{- if .Values.secrets.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "remoteops.fullname" . }}-secret
  labels:
    {{- include "remoteops.labels" . | nindent 4 }}
type: Opaque
data:
  # JWT Secret for authentication
  jwt-secret: {{ include "remoteops.jwtSecret" . | b64enc | quote }}
  
  # Database connection string
  database-url: {{ include "remoteops.postgresql.connectionString" . | b64enc | quote }}
  
  # Redis connection string
  redis-url: {{ include "remoteops.redis.connectionString" . | b64enc | quote }}
  
  # Additional secrets
  {{- with .Values.secrets.data }}
  {{- range $key, $value := . }}
  {{ $key }}: {{ $value | b64enc | quote }}
  {{- end }}
  {{- end }}
{{- end }}

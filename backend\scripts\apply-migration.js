const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function applyMigration() {
  try {
    console.log('Aplicando migração para tornar commandId nullable...');
    
    // Executar a migração diretamente
    await prisma.$executeRaw`ALTER TABLE "CommandHistory" ALTER COLUMN "commandId" DROP NOT NULL;`;
    
    console.log('Migração aplicada com sucesso!');
    
    // Verificar se existem registros com commandId nulo
    const nullCommandIdCount = await prisma.commandHistory.count({
      where: {
        commandId: null
      }
    });
    
    console.log(`Registros com commandId nulo: ${nullCommandIdCount}`);
    
  } catch (error) {
    console.error('Erro ao aplicar migração:', error);
  } finally {
    await prisma.$disconnect();
  }
}

applyMigration();

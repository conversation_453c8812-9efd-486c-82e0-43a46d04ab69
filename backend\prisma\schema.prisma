generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id               String              @id @default(uuid())
  email            String              @unique
  name             String
  password         String
  role             Role                @default(USER)
  createdAt        DateTime            @default(now())
  updatedAt        DateTime            @updatedAt
  active           Boolean             @default(true)

  // Multi-tenant fields
  organizationId   String?
  organizationRole OrganizationRole    @default(MEMBER)

  commandHistory   CommandHistory[]
  commandTemplates CommandTemplate[]
  servers          Server[]
  serverAccess     ServerUser[]
  serverGroups     ServerGroup[]
  auditLogs        AuditLog[]

  // Marketplace relations
  templateReviews  TemplateReview[]
  templateLikes    TemplateLike[]
  templateFavorites TemplateFavorite[]

  // Organization relations
  organization     Organization?       @relation(fields: [organizationId], references: [id], onDelete: SetNull)
  ownedOrganizations Organization[]    @relation("OrganizationOwner")
  organizationInvites OrganizationInvite[]
}

model Server {
  id             String              @id @default(uuid())
  name           String
  host           String
  port           Int                 @default(22)
  username       String
  password       String?
  privateKey     String?
  os             OS                  @default(LINUX)
  deviceType     DeviceType          @default(GENERIC)
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @updatedAt
  userId         String

  // Multi-tenant field
  organizationId String?

  commands       Command[]
  commandHistory CommandHistory[]
  user           User                @relation(fields: [userId], references: [id])
  userAccess     ServerUser[]
  groupMembers   ServerGroupMember[]
  organization   Organization?       @relation(fields: [organizationId], references: [id], onDelete: SetNull)
}

model ServerUser {
  id        String   @id @default(uuid())
  userId    String
  serverId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  server    Server   @relation(fields: [serverId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, serverId])
}

model Command {
  id                String           @id @default(uuid())
  name              String
  command           String
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  serverId          String
  description       String?
  order             Int              @default(0)
  version           Int              @default(1)
  isLatest          Boolean          @default(true)
  previousVersionId String?
  previousVersion   Command?         @relation("CommandVersions", fields: [previousVersionId], references: [id])
  nextVersions      Command[]        @relation("CommandVersions")
  server            Server           @relation(fields: [serverId], references: [id], onDelete: Cascade)
  commandHistory    CommandHistory[]

  @@index([serverId, isLatest])
}

model CommandHistory {
  id           String    @id @default(uuid())
  userId       String
  serverId     String
  commandId    String?
  commandName  String?
  commandText  String?
  result       String?
  status       Int       @default(0)
  executedAt   DateTime  @default(now())
  createdAt    DateTime  @default(now())
  command      Command?  @relation(fields: [commandId], references: [id], onDelete: SetNull)
  server       Server    @relation(fields: [serverId], references: [id], onDelete: Cascade)
  user         User      @relation(fields: [userId], references: [id])
}

model CommandTemplate {
  id              String                @id @default(uuid())
  name            String
  description     String?
  isPublic        Boolean               @default(false)
  userId          String
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt

  // Multi-tenant field
  organizationId  String?

  // Marketplace fields
  category        String?               // e.g., "Network", "Security", "Monitoring"
  tags            String[]              @default([])
  version         String                @default("1.0.0")
  downloads       Int                   @default(0)
  likes           Int                   @default(0)
  isMarketplace   Boolean               @default(false)
  isFeatured      Boolean               @default(false)
  deviceTypes     DeviceType[]          @default([])

  user            User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization    Organization?         @relation(fields: [organizationId], references: [id], onDelete: SetNull)
  commands        CommandTemplateItem[]
  reviews         TemplateReview[]
  likes_users     TemplateLike[]
  favorites       TemplateFavorite[]
}

model CommandTemplateItem {
  id          String          @id @default(uuid())
  name        String
  command     String
  description String?
  templateId  String
  order       Int             @default(0)
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  template    CommandTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
}

// Marketplace models
model TemplateReview {
  id         String          @id @default(uuid())
  templateId String
  userId     String
  rating     Int             // 1-5 stars
  comment    String?
  createdAt  DateTime        @default(now())
  updatedAt  DateTime        @updatedAt

  template   CommandTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  user       User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([templateId, userId]) // One review per user per template
}

model TemplateLike {
  id         String          @id @default(uuid())
  templateId String
  userId     String
  createdAt  DateTime        @default(now())

  template   CommandTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  user       User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([templateId, userId]) // One like per user per template
}

model TemplateFavorite {
  id         String          @id @default(uuid())
  templateId String
  userId     String
  createdAt  DateTime        @default(now())

  template   CommandTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  user       User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([templateId, userId]) // One favorite per user per template
}

model TemplateDownload {
  id         String          @id @default(uuid())
  templateId String
  userId     String
  createdAt  DateTime        @default(now())

  // Note: No direct relation to avoid cascade issues
  // We'll query these separately
}

enum Role {
  ADMIN
  USER
}

enum OS {
  LINUX
  WINDOWS
}

enum DeviceType {
  NOKIA
  HUAWEI
  MIKROTIK
  DMOS
  GENERIC
}

model ServerGroup {
  id          String              @id @default(uuid())
  name        String
  description String?
  color       String?             @default("#3B82F6")
  userId      String
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt

  // Multi-tenant field
  organizationId String?

  user        User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization Organization?      @relation(fields: [organizationId], references: [id], onDelete: SetNull)
  members     ServerGroupMember[]

  @@unique([userId, name])
}

model ServerGroupMember {
  id        String      @id @default(uuid())
  groupId   String
  serverId  String
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  group     ServerGroup @relation(fields: [groupId], references: [id], onDelete: Cascade)
  server    Server      @relation(fields: [serverId], references: [id], onDelete: Cascade)

  @@unique([groupId, serverId])
}

model AuditLog {
  id         String   @id @default(uuid())
  userId     String
  action     String
  resource   String
  resourceId String
  oldValues  String?
  newValues  String?
  ipAddress  String?
  userAgent  String?
  timestamp  DateTime @default(now())
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([resource, resourceId])
  @@index([timestamp])
}

// Multi-tenant models
model Organization {
  id              String                @id @default(uuid())
  name            String
  slug            String                @unique
  description     String?
  website         String?
  logo            String?
  settings        Json?                 @default("{}")
  createdAt       DateTime              @default(now())
  updatedAt       DateTime              @updatedAt

  // Subscription fields
  planType        PlanType              @default(STARTER)
  subscriptionId  String?               // Stripe subscription ID
  customerId      String?               // Stripe customer ID
  trialEndsAt     DateTime?
  subscriptionStatus SubscriptionStatus @default(TRIAL)

  // Limits based on plan
  maxServers      Int                   @default(5)
  maxUsers        Int                   @default(1)

  // Owner
  ownerId         String
  owner           User                  @relation("OrganizationOwner", fields: [ownerId], references: [id], onDelete: Cascade)

  // Relations
  users           User[]
  servers         Server[]
  serverGroups    ServerGroup[]
  commandTemplates CommandTemplate[]
  invites         OrganizationInvite[]
  usageMetrics    OrganizationUsage[]
}

model OrganizationInvite {
  id             String            @id @default(uuid())
  email          String
  role           OrganizationRole  @default(MEMBER)
  token          String            @unique
  expiresAt      DateTime
  acceptedAt     DateTime?
  createdAt      DateTime          @default(now())

  organizationId String
  invitedById    String

  organization   Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  invitedBy      User              @relation(fields: [invitedById], references: [id], onDelete: Cascade)

  @@unique([organizationId, email])
}

model OrganizationUsage {
  id             String       @id @default(uuid())
  organizationId String
  date           DateTime     @default(now())

  // Usage metrics
  serversCount   Int          @default(0)
  usersCount     Int          @default(0)
  commandsExecuted Int        @default(0)
  apiCalls       Int          @default(0)
  storageUsed    BigInt       @default(0) // in bytes

  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, date])
  @@index([organizationId])
  @@index([date])
}

enum OrganizationRole {
  OWNER
  ADMIN
  MEMBER
  VIEWER
}

enum PlanType {
  STARTER
  PROFESSIONAL
  BUSINESS
  ENTERPRISE
}

enum SubscriptionStatus {
  TRIAL
  ACTIVE
  PAST_DUE
  CANCELED
  INCOMPLETE
  INCOMPLETE_EXPIRED
  UNPAID
}

import { useState, useEffect, useCallback } from 'react';

export interface UserSettings {
  // Aparência
  theme: 'light' | 'dark' | 'system';
  fontSize: 'small' | 'medium' | 'large';
  compactMode: boolean;
  animationsEnabled: boolean;
  
  // Editor
  editorFontSize: number;
  editorTheme: 'default' | 'dark' | 'high-contrast';
  syntaxHighlighting: boolean;
  autoComplete: boolean;
  wordWrap: boolean;
  lineNumbers: boolean;
  
  // Comandos
  commandTimeout: number;
  maxHistorySize: number;
  autoSaveCommands: boolean;
  confirmBeforeExecute: boolean;
  showCommandPreview: boolean;
  
  // Notificações
  enableNotifications: boolean;
  notificationSound: boolean;
  emailNotifications: boolean;
  slackNotifications: boolean;
  notificationTypes: {
    commandSuccess: boolean;
    commandError: boolean;
    serverConnection: boolean;
    systemAlerts: boolean;
    backupStatus: boolean;
  };
  
  // Interface
  sidebarCollapsed: boolean;
  showStatusBar: boolean;
  showToolbar: boolean;
  gridDensity: 'comfortable' | 'compact' | 'spacious';
  tablePageSize: number;
  
  // Avançado
  debugMode: boolean;
  experimentalFeatures: boolean;
  telemetryEnabled: boolean;
  autoBackup: boolean;
  
  // Atalhos personalizados
  customShortcuts: Record<string, string>;
  
  // Preferências por dispositivo
  devicePreferences: Record<string, {
    defaultTimeout: number;
    preferredCommands: string[];
    customPrompts: string[];
  }>;
}

const DEFAULT_SETTINGS: UserSettings = {
  // Aparência
  theme: 'system',
  fontSize: 'medium',
  compactMode: false,
  animationsEnabled: true,
  
  // Editor
  editorFontSize: 14,
  editorTheme: 'default',
  syntaxHighlighting: true,
  autoComplete: true,
  wordWrap: true,
  lineNumbers: true,
  
  // Comandos
  commandTimeout: 30,
  maxHistorySize: 1000,
  autoSaveCommands: true,
  confirmBeforeExecute: false,
  showCommandPreview: true,
  
  // Notificações
  enableNotifications: true,
  notificationSound: true,
  emailNotifications: false,
  slackNotifications: false,
  notificationTypes: {
    commandSuccess: true,
    commandError: true,
    serverConnection: true,
    systemAlerts: true,
    backupStatus: true
  },
  
  // Interface
  sidebarCollapsed: false,
  showStatusBar: true,
  showToolbar: true,
  gridDensity: 'comfortable',
  tablePageSize: 25,
  
  // Avançado
  debugMode: false,
  experimentalFeatures: false,
  telemetryEnabled: true,
  autoBackup: true,
  
  // Atalhos personalizados
  customShortcuts: {},
  
  // Preferências por dispositivo
  devicePreferences: {}
};

const STORAGE_KEY = 'sem-fronteiras-user-settings';

interface UseUserSettingsReturn {
  settings: UserSettings;
  updateSetting: <K extends keyof UserSettings>(key: K, value: UserSettings[K]) => void;
  updateNestedSetting: (path: string, value: any) => void;
  resetSettings: () => void;
  exportSettings: () => string;
  importSettings: (data: string) => boolean;
  isDefault: (key: keyof UserSettings) => boolean;
  resetSetting: (key: keyof UserSettings) => void;
}

export const useUserSettings = (): UseUserSettingsReturn => {
  const [settings, setSettings] = useState<UserSettings>(DEFAULT_SETTINGS);

  // Carregar configurações do localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Mesclar com configurações padrão para garantir que novas configurações sejam incluídas
        setSettings(prev => ({ ...DEFAULT_SETTINGS, ...parsed }));
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
    }
  }, []);

  // Salvar configurações no localStorage
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
    }
  }, [settings]);

  const updateSetting = useCallback(<K extends keyof UserSettings>(
    key: K, 
    value: UserSettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  }, []);

  const updateNestedSetting = useCallback((path: string, value: any) => {
    setSettings(prev => {
      const newSettings = { ...prev };
      const keys = path.split('.');
      let current: any = newSettings;
      
      for (let i = 0; i < keys.length - 1; i++) {
        if (!(keys[i] in current)) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      return newSettings;
    });
  }, []);

  const resetSettings = useCallback(() => {
    setSettings(DEFAULT_SETTINGS);
  }, []);

  const exportSettings = useCallback(() => {
    return JSON.stringify(settings, null, 2);
  }, [settings]);

  const importSettings = useCallback((data: string) => {
    try {
      const imported = JSON.parse(data);
      if (typeof imported === 'object' && imported !== null) {
        setSettings({ ...DEFAULT_SETTINGS, ...imported });
        return true;
      }
    } catch (error) {
      console.error('Erro ao importar configurações:', error);
    }
    return false;
  }, []);

  const isDefault = useCallback((key: keyof UserSettings) => {
    return JSON.stringify(settings[key]) === JSON.stringify(DEFAULT_SETTINGS[key]);
  }, [settings]);

  const resetSetting = useCallback((key: keyof UserSettings) => {
    updateSetting(key, DEFAULT_SETTINGS[key]);
  }, [updateSetting]);

  return {
    settings,
    updateSetting,
    updateNestedSetting,
    resetSettings,
    exportSettings,
    importSettings,
    isDefault,
    resetSetting
  };
};

// Hook para configurações específicas do editor
export const useEditorSettings = () => {
  const { settings, updateSetting } = useUserSettings();
  
  return {
    fontSize: settings.editorFontSize,
    theme: settings.editorTheme,
    syntaxHighlighting: settings.syntaxHighlighting,
    autoComplete: settings.autoComplete,
    wordWrap: settings.wordWrap,
    lineNumbers: settings.lineNumbers,
    setFontSize: (size: number) => updateSetting('editorFontSize', size),
    setTheme: (theme: UserSettings['editorTheme']) => updateSetting('editorTheme', theme),
    toggleSyntaxHighlighting: () => updateSetting('syntaxHighlighting', !settings.syntaxHighlighting),
    toggleAutoComplete: () => updateSetting('autoComplete', !settings.autoComplete),
    toggleWordWrap: () => updateSetting('wordWrap', !settings.wordWrap),
    toggleLineNumbers: () => updateSetting('lineNumbers', !settings.lineNumbers)
  };
};

// Hook para configurações de notificações
export const useNotificationSettings = () => {
  const { settings, updateSetting, updateNestedSetting } = useUserSettings();
  
  return {
    enabled: settings.enableNotifications,
    sound: settings.notificationSound,
    email: settings.emailNotifications,
    slack: settings.slackNotifications,
    types: settings.notificationTypes,
    toggleEnabled: () => updateSetting('enableNotifications', !settings.enableNotifications),
    toggleSound: () => updateSetting('notificationSound', !settings.notificationSound),
    toggleEmail: () => updateSetting('emailNotifications', !settings.emailNotifications),
    toggleSlack: () => updateSetting('slackNotifications', !settings.slackNotifications),
    updateType: (type: keyof UserSettings['notificationTypes'], value: boolean) => 
      updateNestedSetting(`notificationTypes.${type}`, value)
  };
};

// Hook para configurações de interface
export const useInterfaceSettings = () => {
  const { settings, updateSetting } = useUserSettings();
  
  return {
    compactMode: settings.compactMode,
    animationsEnabled: settings.animationsEnabled,
    sidebarCollapsed: settings.sidebarCollapsed,
    showStatusBar: settings.showStatusBar,
    showToolbar: settings.showToolbar,
    gridDensity: settings.gridDensity,
    tablePageSize: settings.tablePageSize,
    toggleCompactMode: () => updateSetting('compactMode', !settings.compactMode),
    toggleAnimations: () => updateSetting('animationsEnabled', !settings.animationsEnabled),
    toggleSidebar: () => updateSetting('sidebarCollapsed', !settings.sidebarCollapsed),
    toggleStatusBar: () => updateSetting('showStatusBar', !settings.showStatusBar),
    toggleToolbar: () => updateSetting('showToolbar', !settings.showToolbar),
    setGridDensity: (density: UserSettings['gridDensity']) => updateSetting('gridDensity', density),
    setTablePageSize: (size: number) => updateSetting('tablePageSize', size)
  };
};

export default useUserSettings;

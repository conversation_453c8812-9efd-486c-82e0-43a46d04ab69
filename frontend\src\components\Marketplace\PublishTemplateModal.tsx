import React, { useState, useEffect } from 'react'
import { X, Plus, Tag, AlertCircle } from 'lucide-react'
import { api } from '../../lib/api'

interface PublishTemplateModalProps {
  categories: string[]
  onPublish: (templateId: string, data: {
    category: string
    tags: string[]
    deviceTypes: string[]
  }) => Promise<void>
  onClose: () => void
}

interface UserTemplate {
  id: string
  name: string
  description?: string
  isPublic: boolean
  isMarketplace: boolean
}

const deviceTypes = [
  'CISCO',
  'HUAWEI', 
  'NOKIA',
  'JUNIPER',
  'MIKROTIK',
  'UBIQUITI',
  'FORTINET',
  'PFSENSE',
  'LINUX',
  'WINDOWS',
  'OTHER'
]

const PublishTemplateModal: React.FC<PublishTemplateModalProps> = ({
  categories,
  onPublish,
  onClose
}) => {
  const [userTemplates, setUserTemplates] = useState<UserTemplate[]>([])
  const [selectedTemplate, setSelectedTemplate] = useState('')
  const [category, setCategory] = useState('')
  const [newCategory, setNewCategory] = useState('')
  const [tags, setTags] = useState<string[]>([])
  const [tagInput, setTagInput] = useState('')
  const [selectedDeviceTypes, setSelectedDeviceTypes] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [loadingTemplates, setLoadingTemplates] = useState(true)
  const [errors, setErrors] = useState<string[]>([])

  // Carregar templates do usuário
  useEffect(() => {
    const loadUserTemplates = async () => {
      try {
        const response = await api.get('/command-templates')
        const templates = response.data.data.filter((t: UserTemplate) => 
          t.isPublic && !t.isMarketplace
        )
        setUserTemplates(templates)
      } catch (error) {
        console.error('Erro ao carregar templates:', error)
      } finally {
        setLoadingTemplates(false)
      }
    }

    loadUserTemplates()
  }, [])

  const handleAddTag = () => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()])
      setTagInput('')
    }
  }

  const handleRemoveTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag))
  }

  const handleDeviceTypeChange = (deviceType: string) => {
    setSelectedDeviceTypes(prev => 
      prev.includes(deviceType)
        ? prev.filter(t => t !== deviceType)
        : [...prev, deviceType]
    )
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddTag()
    }
  }

  const validateForm = () => {
    const newErrors: string[] = []

    if (!selectedTemplate) {
      newErrors.push('Selecione um template para publicar')
    }

    const finalCategory = category === 'new' ? newCategory : category
    if (!finalCategory.trim()) {
      newErrors.push('Categoria é obrigatória')
    }

    if (tags.length === 0) {
      newErrors.push('Pelo menos uma tag é obrigatória')
    }

    if (selectedDeviceTypes.length === 0) {
      newErrors.push('Pelo menos um tipo de dispositivo é obrigatório')
    }

    setErrors(newErrors)
    return newErrors.length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    try {
      const finalCategory = category === 'new' ? newCategory : category
      await onPublish(selectedTemplate, {
        category: finalCategory,
        tags,
        deviceTypes: selectedDeviceTypes
      })
      onClose()
    } catch (error) {
      console.error('Erro ao publicar template:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            Publicar Template no Marketplace
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Errors */}
          {errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                <h3 className="text-sm font-medium text-red-800">
                  Corrija os seguintes erros:
                </h3>
              </div>
              <ul className="text-sm text-red-700 space-y-1">
                {errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Template Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Selecionar Template
            </label>
            {loadingTemplates ? (
              <div className="animate-pulse">
                <div className="h-10 bg-gray-200 rounded"></div>
              </div>
            ) : (
              <select
                value={selectedTemplate}
                onChange={(e) => setSelectedTemplate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Escolha um template...</option>
                {userTemplates.map((template) => (
                  <option key={template.id} value={template.id}>
                    {template.name}
                  </option>
                ))}
              </select>
            )}
            {userTemplates.length === 0 && !loadingTemplates && (
              <p className="text-sm text-gray-500 mt-1">
                Você não possui templates públicos disponíveis para publicação.
              </p>
            )}
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Categoria
            </label>
            <select
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 mb-2"
              required
            >
              <option value="">Selecione uma categoria...</option>
              {categories.map((cat) => (
                <option key={cat} value={cat}>
                  {cat}
                </option>
              ))}
              <option value="new">+ Nova categoria</option>
            </select>
            
            {category === 'new' && (
              <input
                type="text"
                placeholder="Nome da nova categoria"
                value={newCategory}
                onChange={(e) => setNewCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            )}
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tags
            </label>
            <div className="flex items-center space-x-2 mb-3">
              <input
                type="text"
                placeholder="Adicionar tag..."
                value={tagInput}
                onChange={(e) => setTagInput(e.target.value)}
                onKeyPress={handleKeyPress}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="button"
                onClick={handleAddTag}
                disabled={!tagInput.trim()}
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Plus className="h-4 w-4 mr-1" />
                Adicionar
              </button>
            </div>
            
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <span
                    key={tag}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                  >
                    <Tag className="h-3 w-3 mr-1" />
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>

          {/* Device Types */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tipos de Dispositivo Compatíveis
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {deviceTypes.map((deviceType) => (
                <label key={deviceType} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedDeviceTypes.includes(deviceType)}
                    onChange={() => handleDeviceTypeChange(deviceType)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{deviceType}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isLoading || userTemplates.length === 0}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Publicando...' : 'Publicar no Marketplace'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default PublishTemplateModal

/**
 * Configuração de marca para RemoteOps
 * Centraliza todas as informações de branding do sistema
 */

export const BRAND_CONFIG = {
  // Informações básicas da marca
  name: 'RemoteOps',
  tagline: 'Streamline Your Remote Operations',
  description: 'A plataforma completa para gerenciamento de infraestrutura remota que simplifica operações SSH/RDP em escala empresarial.',

  // URLs e contatos
  website: 'https://remoteops.io',
  support: '<EMAIL>',
  sales: '<EMAIL>',

  // Versão e copyright
  version: '2.0.0',
  copyright: `© ${new Date().getFullYear()} RemoteOps. Todos os direitos reservados.`,

  // Cores da marca
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb', // Cor principal
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
    },
    secondary: {
      50: '#ecfdf5',
      100: '#d1fae5',
      200: '#a7f3d0',
      300: '#6ee7b7',
      400: '#34d399',
      500: '#10b981', // Verde tecnológico
      600: '#059669',
      700: '#047857',
      800: '#065f46',
      900: '#064e3b',
    },
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    }
  },

  // Tipografia
  fonts: {
    primary: 'Inter, system-ui, -apple-system, sans-serif',
    mono: 'JetBrains Mono, Consolas, Monaco, monospace',
  },

  // Ícones e logos
  logo: {
    text: 'RemoteOps',
    icon: '🚀', // Temporário até ter logo real
    favicon: '/favicon.ico',
  },

  // Redes sociais
  social: {
    twitter: 'https://twitter.com/remoteops',
    linkedin: 'https://linkedin.com/company/remoteops',
    github: 'https://github.com/remoteops',
    discord: 'https://discord.gg/remoteops',
  },

  // Configurações de SEO
  seo: {
    title: 'RemoteOps - Gerenciamento de Infraestrutura Remota',
    description: 'Plataforma completa para gerenciamento de infraestrutura remota via SSH/RDP. Centralize, automatize e escale suas operações remotas.',
    keywords: [
      'SSH management',
      'remote infrastructure',
      'DevOps tools',
      'server management',
      'terminal management',
      'infrastructure automation',
      'remote operations',
      'system administration'
    ],
    ogImage: '/og-image.png',
  },

  // Configurações de produto
  product: {
    features: [
      'Gerenciamento Centralizado',
      'Automação Inteligente',
      'Auditoria Completa',
      'Cache Otimizado',
      'Segurança Avançada',
      'API Robusta'
    ],
    benefits: [
      'Reduz tempo de operação em 80%',
      'Centraliza gerenciamento de milhares de servidores',
      'Garante compliance e auditoria completa',
      'Otimiza performance automaticamente'
    ]
  },

  // Planos de preços (para futuro SaaS)
  pricing: {
    plans: [
      {
        id: 'starter',
        name: 'Starter',
        price: 0,
        period: 'month',
        description: 'Perfeito para começar',
        features: [
          'Até 5 servidores',
          '1 usuário',
          'Comandos básicos',
          'Suporte community'
        ],
        popular: false
      },
      {
        id: 'professional',
        name: 'Professional',
        price: 29,
        period: 'month',
        description: 'Para equipes pequenas',
        features: [
          'Até 50 servidores',
          '5 usuários',
          'Templates avançados',
          'Cache otimizado',
          'Suporte email'
        ],
        popular: true
      },
      {
        id: 'business',
        name: 'Business',
        price: 99,
        period: 'month',
        description: 'Para empresas em crescimento',
        features: [
          'Até 200 servidores',
          '20 usuários',
          'Auditoria completa',
          'API access',
          'Suporte prioritário'
        ],
        popular: false
      },
      {
        id: 'enterprise',
        name: 'Enterprise',
        price: null,
        period: 'custom',
        description: 'Soluções customizadas',
        features: [
          'Servidores ilimitados',
          'Usuários ilimitados',
          'SSO/SAML',
          'Suporte dedicado',
          'Features customizadas'
        ],
        popular: false
      }
    ]
  },

  // Configurações de ambiente
  environment: {
    development: {
      apiUrl: 'http://localhost:3001',
      wsUrl: 'ws://localhost:3001',
      debug: true
    },
    staging: {
      apiUrl: 'https://api-staging.remoteops.io',
      wsUrl: 'wss://api-staging.remoteops.io',
      debug: false
    },
    production: {
      apiUrl: 'https://api.remoteops.io',
      wsUrl: 'wss://api.remoteops.io',
      debug: false
    }
  },

  // Configurações de analytics
  analytics: {
    googleAnalytics: 'GA_MEASUREMENT_ID',
    mixpanel: 'MIXPANEL_TOKEN',
    hotjar: 'HOTJAR_ID',
    intercom: 'INTERCOM_APP_ID'
  },

  // Configurações de feature flags
  features: {
    multiTenant: true,  // ✅ IMPLEMENTADO - Sistema multi-tenant
    billing: false,     // Para ser habilitado na versão SaaS
    marketplace: true,  // ✅ IMPLEMENTADO - Marketplace de templates
    mobileApp: false,   // Para futuro desenvolvimento
    aiAssistant: false  // Para futuras funcionalidades IA
  },

  // Configurações de suporte
  support: {
    helpCenter: 'https://help.remoteops.io',
    documentation: 'https://docs.remoteops.io',
    statusPage: 'https://status.remoteops.io',
    community: 'https://community.remoteops.io'
  }
} as const

// Tipos TypeScript para type safety
export type BrandConfig = typeof BRAND_CONFIG
export type ColorPalette = typeof BRAND_CONFIG.colors.primary
export type PricingPlan = typeof BRAND_CONFIG.pricing.plans[0]

// Helper functions
export const getBrandColor = (color: keyof typeof BRAND_CONFIG.colors, shade: keyof ColorPalette = 600) => {
  return BRAND_CONFIG.colors[color][shade]
}

export const getEnvironmentConfig = () => {
  const env = process.env.NODE_ENV as keyof typeof BRAND_CONFIG.environment
  return BRAND_CONFIG.environment[env] || BRAND_CONFIG.environment.development
}

export const isFeatureEnabled = (feature: keyof typeof BRAND_CONFIG.features) => {
  return BRAND_CONFIG.features[feature]
}

export default BRAND_CONFIG

import { Logger } from '../../utils/logger';

interface AlertRule {
  id: string;
  name: string;
  description: string;
  condition: AlertCondition;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  cooldownMinutes: number; // Tempo mínimo entre alertas do mesmo tipo
  actions: AlertAction[];
}

interface AlertCondition {
  type: 'success_rate' | 'execution_time' | 'error_count' | 'consecutive_failures' | 'service_down';
  threshold: number;
  timeWindowMinutes: number;
  deviceType?: string;
  service?: 'nodejs' | 'python';
}

interface AlertAction {
  type: 'log' | 'email' | 'webhook' | 'slack';
  config: any;
}

interface ActiveAlert {
  id: string;
  ruleId: string;
  ruleName: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  metadata: any;
}

interface AlertHistory {
  ruleId: string;
  lastTriggered: Date;
  triggerCount: number;
}

/**
 * Serviço de alertas inteligentes
 * Monitora métricas e dispara alertas baseado em regras configuráveis
 */
export class AlertService {
  private rules: AlertRule[] = [];
  private activeAlerts: ActiveAlert[] = [];
  private alertHistory: Map<string, AlertHistory> = new Map();
  private checkInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeDefaultRules();
    this.startMonitoring();
    Logger.log('AlertService inicializado com regras padrão');
  }

  /**
   * Inicia o monitoramento automático
   */
  private startMonitoring(): void {
    // Verificar alertas a cada 2 minutos
    this.checkInterval = setInterval(() => {
      this.checkAllRules();
    }, 120000);
  }

  /**
   * Para o monitoramento automático
   */
  stopMonitoring(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Inicializa regras de alerta padrão
   */
  private initializeDefaultRules(): void {
    const defaultRules: AlertRule[] = [
      {
        id: 'low-success-rate-huawei',
        name: 'Taxa de Sucesso Baixa - Huawei',
        description: 'Taxa de sucesso abaixo de 80% para dispositivos Huawei',
        condition: {
          type: 'success_rate',
          threshold: 0.8,
          timeWindowMinutes: 30,
          deviceType: 'HUAWEI'
        },
        severity: 'high',
        enabled: true,
        cooldownMinutes: 15,
        actions: [
          { type: 'log', config: {} },
          { type: 'webhook', config: { url: process.env.ALERT_WEBHOOK_URL } }
        ]
      },
      {
        id: 'high-execution-time',
        name: 'Tempo de Execução Alto',
        description: 'Tempo médio de execução acima de 60 segundos',
        condition: {
          type: 'execution_time',
          threshold: 60000,
          timeWindowMinutes: 15
        },
        severity: 'medium',
        enabled: true,
        cooldownMinutes: 10,
        actions: [
          { type: 'log', config: {} }
        ]
      },
      {
        id: 'python-service-failures',
        name: 'Falhas no Serviço Python',
        description: 'Múltiplas falhas consecutivas no serviço Python',
        condition: {
          type: 'consecutive_failures',
          threshold: 5,
          timeWindowMinutes: 10,
          service: 'python'
        },
        severity: 'critical',
        enabled: true,
        cooldownMinutes: 5,
        actions: [
          { type: 'log', config: {} },
          { type: 'webhook', config: { url: process.env.ALERT_WEBHOOK_URL } }
        ]
      },
      {
        id: 'nodejs-service-failures',
        name: 'Falhas no Serviço Node.js',
        description: 'Múltiplas falhas consecutivas no serviço Node.js',
        condition: {
          type: 'consecutive_failures',
          threshold: 5,
          timeWindowMinutes: 10,
          service: 'nodejs'
        },
        severity: 'critical',
        enabled: true,
        cooldownMinutes: 5,
        actions: [
          { type: 'log', config: {} },
          { type: 'webhook', config: { url: process.env.ALERT_WEBHOOK_URL } }
        ]
      },
      {
        id: 'high-error-rate',
        name: 'Taxa de Erro Alta',
        description: 'Taxa de erro geral acima de 20%',
        condition: {
          type: 'error_count',
          threshold: 0.2,
          timeWindowMinutes: 20
        },
        severity: 'high',
        enabled: true,
        cooldownMinutes: 15,
        actions: [
          { type: 'log', config: {} }
        ]
      }
    ];

    this.rules = defaultRules;
  }

  /**
   * Adiciona uma nova regra de alerta
   */
  addRule(rule: Omit<AlertRule, 'id'>): string {
    const newRule: AlertRule = {
      ...rule,
      id: this.generateId()
    };

    this.rules.push(newRule);
    Logger.log(`Nova regra de alerta adicionada: ${newRule.name}`);

    return newRule.id;
  }

  /**
   * Remove uma regra de alerta
   */
  removeRule(ruleId: string): boolean {
    const initialLength = this.rules.length;
    this.rules = this.rules.filter(rule => rule.id !== ruleId);

    if (this.rules.length < initialLength) {
      Logger.log(`Regra de alerta removida: ${ruleId}`);
      return true;
    }

    return false;
  }

  /**
   * Atualiza uma regra de alerta
   */
  updateRule(ruleId: string, updates: Partial<AlertRule>): boolean {
    const ruleIndex = this.rules.findIndex(rule => rule.id === ruleId);

    if (ruleIndex !== -1) {
      this.rules[ruleIndex] = { ...this.rules[ruleIndex], ...updates };
      Logger.log(`Regra de alerta atualizada: ${ruleId}`);
      return true;
    }

    return false;
  }

  /**
   * Obtém todas as regras de alerta
   */
  getRules(): AlertRule[] {
    return [...this.rules];
  }

  /**
   * Obtém alertas ativos
   */
  getActiveAlerts(): ActiveAlert[] {
    return this.activeAlerts.filter(alert => !alert.resolved);
  }

  /**
   * Obtém histórico de alertas
   */
  getAlertHistory(limit: number = 100): ActiveAlert[] {
    return this.activeAlerts
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Resolve um alerta manualmente
   */
  resolveAlert(alertId: string): boolean {
    const alert = this.activeAlerts.find(a => a.id === alertId);

    if (alert && !alert.resolved) {
      alert.resolved = true;
      alert.resolvedAt = new Date();
      Logger.log(`Alerta resolvido manualmente: ${alertId}`);
      return true;
    }

    return false;
  }

  /**
   * Verifica todas as regras de alerta
   */
  private async checkAllRules(): Promise<void> {
    for (const rule of this.rules) {
      if (rule.enabled) {
        await this.checkRule(rule);
      }
    }
  }

  /**
   * Verifica uma regra específica
   */
  private async checkRule(rule: AlertRule): Promise<void> {
    try {
      // Verificar cooldown
      if (this.isInCooldown(rule)) {
        return;
      }

      // Avaliar condição
      const shouldTrigger = await this.evaluateCondition(rule.condition);

      if (shouldTrigger) {
        await this.triggerAlert(rule);
      }
    } catch (error) {
      Logger.error(`Erro ao verificar regra ${rule.name}:`, error);
    }
  }

  /**
   * Verifica se uma regra está em cooldown
   */
  private isInCooldown(rule: AlertRule): boolean {
    const history = this.alertHistory.get(rule.id);

    if (!history) {
      return false;
    }

    const cooldownMs = rule.cooldownMinutes * 60 * 1000;
    const timeSinceLastTrigger = Date.now() - history.lastTriggered.getTime();

    return timeSinceLastTrigger < cooldownMs;
  }

  /**
   * Avalia uma condição de alerta
   */
  private async evaluateCondition(condition: AlertCondition): Promise<boolean> {
    try {
      switch (condition.type) {
        case 'success_rate':
          return await this.checkSuccessRate(condition);
        case 'execution_time':
          return await this.checkExecutionTime(condition);
        case 'error_count':
          return await this.checkErrorCount(condition);
        case 'consecutive_failures':
          return await this.checkConsecutiveFailures(condition);
        case 'service_down':
          return await this.checkServiceDown(condition);
        default:
          return false;
      }
    } catch (error) {
      Logger.error(`Erro ao avaliar condição de alerta ${condition.type}:`, error);
      return false;
    }
  }

  /**
   * Dispara um alerta
   */
  private async triggerAlert(rule: AlertRule): Promise<void> {
    const alert: ActiveAlert = {
      id: this.generateId(),
      ruleId: rule.id,
      ruleName: rule.name,
      message: this.generateAlertMessage(rule),
      severity: rule.severity,
      timestamp: new Date(),
      resolved: false,
      metadata: {
        condition: rule.condition,
        description: rule.description
      }
    };

    this.activeAlerts.push(alert);

    // Atualizar histórico
    this.alertHistory.set(rule.id, {
      ruleId: rule.id,
      lastTriggered: new Date(),
      triggerCount: (this.alertHistory.get(rule.id)?.triggerCount || 0) + 1
    });

    // Executar ações
    for (const action of rule.actions) {
      await this.executeAction(action, alert);
    }

    Logger.log(`Alerta disparado: ${rule.name} [${rule.severity.toUpperCase()}]`);
  }

  /**
   * Executa uma ação de alerta
   */
  private async executeAction(action: AlertAction, alert: ActiveAlert): Promise<void> {
    try {
      switch (action.type) {
        case 'log':
          Logger.log(`ALERTA [${alert.severity.toUpperCase()}]: ${alert.message}`);
          break;

        case 'webhook':
          if (action.config.url) {
            await this.sendWebhook(action.config.url, alert);
          }
          break;

        case 'email':
          if (action.config.to) {
            await this.sendEmailAlert(action.config.to, alert);
          }
          break;

        case 'slack':
          if (action.config.webhook_url) {
            await this.sendSlackAlert(action.config.webhook_url, alert);
          }
          break;
      }
    } catch (error) {
      Logger.error(`Erro ao executar ação ${action.type}:`, error);
    }
  }

  /**
   * Envia webhook
   */
  private async sendWebhook(url: string, alert: ActiveAlert): Promise<void> {
    try {
      const fetch = (await import('node-fetch')).default;

      const payload = {
        alert: {
          id: alert.id,
          rule: alert.ruleName,
          message: alert.message,
          severity: alert.severity,
          timestamp: alert.timestamp.toISOString(),
          metadata: alert.metadata
        },
        system: 'REMOTEOPS SSH'
      };

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'SemFronteiras-AlertService/1.0'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`Webhook failed: ${response.status} ${response.statusText}`);
      }

      Logger.log(`Webhook enviado com sucesso para ${url}`);
    } catch (error) {
      Logger.error(`Erro ao enviar webhook:`, error);
    }
  }

  /**
   * Gera mensagem de alerta
   */
  private generateAlertMessage(rule: AlertRule): string {
    const condition = rule.condition;
    let message = rule.description;

    // Adicionar detalhes específicos baseado no tipo de condição
    switch (condition.type) {
      case 'success_rate':
        message += ` (Threshold: ${(condition.threshold * 100).toFixed(1)}%)`;
        break;
      case 'execution_time':
        message += ` (Threshold: ${(condition.threshold / 1000).toFixed(1)}s)`;
        break;
      case 'consecutive_failures':
        message += ` (Threshold: ${condition.threshold} failures)`;
        break;
    }

    if (condition.deviceType) {
      message += ` - Device: ${condition.deviceType}`;
    }

    if (condition.service) {
      message += ` - Service: ${condition.service}`;
    }

    return message;
  }

  // Métodos de verificação de condições
  private async checkSuccessRate(condition: AlertCondition): Promise<boolean> {
    try {
      const timeRange = condition.timeWindow || '1h';
      const threshold = condition.threshold;

      // Obter métricas do período especificado
      const metrics = this.metricsService.getMetrics(timeRange);

      if (metrics.length === 0) return false;

      // Calcular taxa de sucesso
      const successRate = metrics.filter(m => m.success).length / metrics.length;

      // Verificar se está abaixo do threshold
      return successRate < threshold;
    } catch (error) {
      Logger.error('Erro ao verificar taxa de sucesso:', error);
      return false;
    }
  }

  private async checkExecutionTime(condition: AlertCondition): Promise<boolean> {
    try {
      const timeRange = condition.timeWindow || '1h';
      const threshold = condition.threshold;

      // Obter métricas do período especificado
      const metrics = this.metricsService.getMetrics(timeRange);

      if (metrics.length === 0) return false;

      // Calcular tempo médio de execução
      const avgExecutionTime = metrics.reduce((sum, m) => sum + m.executionTime, 0) / metrics.length;

      // Verificar se está acima do threshold (em segundos)
      return avgExecutionTime > threshold * 1000; // threshold em segundos, executionTime em ms
    } catch (error) {
      Logger.error('Erro ao verificar tempo de execução:', error);
      return false;
    }
  }

  private async checkErrorCount(condition: AlertCondition): Promise<boolean> {
    try {
      const timeRange = condition.timeWindow || '1h';
      const threshold = condition.threshold;

      // Obter métricas do período especificado
      const metrics = this.metricsService.getMetrics(timeRange);

      // Contar erros
      const errorCount = metrics.filter(m => !m.success).length;

      // Verificar se está acima do threshold
      return errorCount > threshold;
    } catch (error) {
      Logger.error('Erro ao verificar contagem de erros:', error);
      return false;
    }
  }

  private async checkConsecutiveFailures(condition: AlertCondition): Promise<boolean> {
    try {
      const threshold = condition.threshold;

      // Obter métricas mais recentes
      const recentMetrics = this.metricsService.getMetrics('1h')
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, Math.max(threshold + 5, 20)); // Pegar um pouco mais para análise

      if (recentMetrics.length < threshold) return false;

      // Verificar falhas consecutivas
      let consecutiveFailures = 0;
      for (const metric of recentMetrics) {
        if (!metric.success) {
          consecutiveFailures++;
          if (consecutiveFailures >= threshold) {
            return true;
          }
        } else {
          break; // Quebra a sequência de falhas
        }
      }

      return false;
    } catch (error) {
      Logger.error('Erro ao verificar falhas consecutivas:', error);
      return false;
    }
  }

  private async checkServiceDown(condition: AlertCondition): Promise<boolean> {
    try {
      // Obter status de saúde do sistema
      const health = await this.metricsService.getSystemHealth();

      // Verificar se algum serviço está down
      const servicesDown = Object.entries(health.services)
        .filter(([_, status]) => status === 'down')
        .map(([service, _]) => service);

      return servicesDown.length > 0;
    } catch (error) {
      Logger.error('Erro ao verificar serviços down:', error);
      return false;
    }
  }

  /**
   * Envia alerta por email
   */
  private async sendEmailAlert(to: string, alert: ActiveAlert): Promise<void> {
    try {
      // Implementação básica usando nodemailer (se configurado)
      const nodemailer = require('nodemailer');

      // Verificar se as configurações de email estão disponíveis
      const emailConfig = {
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      };

      if (!emailConfig.host || !emailConfig.auth.user) {
        Logger.warn('Configurações de email não encontradas. Alerta não enviado por email.');
        return;
      }

      const transporter = nodemailer.createTransporter(emailConfig);

      const severityEmoji = {
        low: '🟡',
        medium: '🟠',
        high: '🔴',
        critical: '🚨'
      };

      const mailOptions = {
        from: process.env.SMTP_FROM || emailConfig.auth.user,
        to: to,
        subject: `${severityEmoji[alert.severity]} Alerta REMOTEOPS - ${alert.severity.toUpperCase()}`,
        html: `
          <h2>🚨 Alerta do Sistema REMOTEOPS</h2>
          <p><strong>Severidade:</strong> ${alert.severity.toUpperCase()}</p>
          <p><strong>Regra:</strong> ${alert.ruleName}</p>
          <p><strong>Mensagem:</strong> ${alert.message}</p>
          <p><strong>Timestamp:</strong> ${alert.timestamp.toLocaleString('pt-BR')}</p>
          <hr>
          <p><small>Este é um alerta automático do sistema REMOTEOPS SSH Management.</small></p>
        `
      };

      await transporter.sendMail(mailOptions);
      Logger.log(`Email de alerta enviado para ${to}`);
    } catch (error) {
      Logger.error('Erro ao enviar email de alerta:', error);
    }
  }

  /**
   * Envia alerta para Slack
   */
  private async sendSlackAlert(webhookUrl: string, alert: ActiveAlert): Promise<void> {
    try {
      const axios = require('axios');

      const severityColors = {
        low: '#ffeb3b',
        medium: '#ff9800',
        high: '#f44336',
        critical: '#9c27b0'
      };

      const severityEmoji = {
        low: ':warning:',
        medium: ':exclamation:',
        high: ':red_circle:',
        critical: ':rotating_light:'
      };

      const payload = {
        text: `${severityEmoji[alert.severity]} *Alerta REMOTEOPS*`,
        attachments: [
          {
            color: severityColors[alert.severity],
            fields: [
              {
                title: 'Severidade',
                value: alert.severity.toUpperCase(),
                short: true
              },
              {
                title: 'Regra',
                value: alert.ruleName,
                short: true
              },
              {
                title: 'Mensagem',
                value: alert.message,
                short: false
              },
              {
                title: 'Timestamp',
                value: alert.timestamp.toLocaleString('pt-BR'),
                short: true
              }
            ],
            footer: 'REMOTEOPS SSH Management',
            ts: Math.floor(alert.timestamp.getTime() / 1000)
          }
        ]
      };

      await axios.post(webhookUrl, payload);
      Logger.log('Alerta enviado para Slack');
    } catch (error) {
      Logger.error('Erro ao enviar alerta para Slack:', error);
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

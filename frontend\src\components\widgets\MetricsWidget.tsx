import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  LinearProgress,
  IconButton,
  Tooltip,
  Select,
  MenuItem,
  FormControl,
  InputLabel
} from '@mui/material';
import {
  Memory as MemoryIcon,
  Speed as CpuIcon,
  NetworkCheck as NetworkIcon,
  Storage as StorageIcon,
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon
} from '@mui/icons-material';
import { DashboardWidget } from '../../hooks/useDashboard';

interface Metric {
  name: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  threshold: number;
  icon: React.ReactNode;
  color: string;
}

interface MetricsWidgetProps {
  widget: DashboardWidget;
}

const MetricsWidget: React.FC<MetricsWidgetProps> = ({ widget }) => {
  const [metrics, setMetrics] = useState<Metric[]>([]);
  const [timeRange, setTimeRange] = useState(widget.config.timeRange || '1h');
  const [loading, setLoading] = useState(false);

  // Mock data - em produção viria da API
  const generateMockMetrics = (): Metric[] => [
    {
      name: 'CPU',
      value: Math.random() * 100,
      unit: '%',
      trend: Math.random() > 0.5 ? 'up' : 'down',
      threshold: 80,
      icon: <CpuIcon />,
      color: '#1976d2'
    },
    {
      name: 'Memória',
      value: Math.random() * 100,
      unit: '%',
      trend: Math.random() > 0.5 ? 'up' : 'stable',
      threshold: 85,
      icon: <MemoryIcon />,
      color: '#388e3c'
    },
    {
      name: 'Rede',
      value: Math.random() * 1000,
      unit: 'Mbps',
      trend: Math.random() > 0.5 ? 'down' : 'up',
      threshold: 800,
      icon: <NetworkIcon />,
      color: '#f57c00'
    },
    {
      name: 'Armazenamento',
      value: Math.random() * 100,
      unit: '%',
      trend: 'up',
      threshold: 90,
      icon: <StorageIcon />,
      color: '#d32f2f'
    }
  ];

  useEffect(() => {
    loadMetrics();
    
    const interval = setInterval(() => {
      loadMetrics();
    }, widget.refreshInterval || 60000);

    return () => clearInterval(interval);
  }, [widget.refreshInterval, timeRange]);

  const loadMetrics = async () => {
    setLoading(true);
    try {
      // Simular carregamento
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newMetrics = generateMockMetrics();
      
      // Filtrar métricas baseado na configuração
      const configMetrics = widget.config.metrics || ['cpu', 'memory', 'network', 'storage'];
      const filteredMetrics = newMetrics.filter(metric => 
        configMetrics.includes(metric.name.toLowerCase())
      );
      
      setMetrics(filteredMetrics);
    } catch (error) {
      console.error('Erro ao carregar métricas:', error);
    } finally {
      setLoading(false);
    }
  };

  const getMetricColor = (metric: Metric) => {
    if (metric.value > metric.threshold) {
      return 'error';
    }
    if (metric.value > metric.threshold * 0.8) {
      return 'warning';
    }
    return 'success';
  };

  const getTrendIcon = (trend: Metric['trend']) => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon color="error" fontSize="small" />;
      case 'down':
        return <TrendingDownIcon color="success" fontSize="small" />;
      default:
        return null;
    }
  };

  const formatValue = (value: number, unit: string) => {
    if (unit === 'Mbps' && value > 1000) {
      return `${(value / 1000).toFixed(1)} Gbps`;
    }
    return `${value.toFixed(1)} ${unit}`;
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Cabeçalho com controles */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <FormControl size="small" sx={{ minWidth: 100 }}>
          <InputLabel>Período</InputLabel>
          <Select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            label="Período"
          >
            <MenuItem value="15m">15 min</MenuItem>
            <MenuItem value="1h">1 hora</MenuItem>
            <MenuItem value="6h">6 horas</MenuItem>
            <MenuItem value="24h">24 horas</MenuItem>
          </Select>
        </FormControl>
        
        <Tooltip title="Atualizar">
          <IconButton size="small" onClick={loadMetrics} disabled={loading}>
            <RefreshIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Loading */}
      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {/* Métricas */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <Grid container spacing={2}>
          {metrics.map((metric, index) => (
            <Grid item xs={12} sm={6} key={index}>
              <Card variant="outlined" sx={{ height: '100%' }}>
                <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                  {/* Cabeçalho da métrica */}
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{ color: metric.color }}>
                        {metric.icon}
                      </Box>
                      <Typography variant="subtitle2">
                        {metric.name}
                      </Typography>
                    </Box>
                    {getTrendIcon(metric.trend)}
                  </Box>

                  {/* Valor principal */}
                  <Typography 
                    variant="h4" 
                    sx={{ 
                      color: `${getMetricColor(metric)}.main`,
                      fontWeight: 'bold',
                      mb: 1
                    }}
                  >
                    {formatValue(metric.value, metric.unit)}
                  </Typography>

                  {/* Barra de progresso */}
                  <LinearProgress
                    variant="determinate"
                    value={Math.min(metric.value, 100)}
                    color={getMetricColor(metric) as any}
                    sx={{ 
                      height: 8, 
                      borderRadius: 4,
                      mb: 1,
                      backgroundColor: 'action.hover'
                    }}
                  />

                  {/* Informações adicionais */}
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="caption" color="text.secondary">
                      Limite: {metric.threshold}{metric.unit}
                    </Typography>
                    <Typography 
                      variant="caption" 
                      color={metric.value > metric.threshold ? 'error.main' : 'text.secondary'}
                    >
                      {metric.value > metric.threshold ? 'Acima do limite' : 'Normal'}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Resumo geral */}
        <Card variant="outlined" sx={{ mt: 2 }}>
          <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
            <Typography variant="subtitle2" gutterBottom>
              Resumo do Sistema
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography 
                    variant="h6" 
                    color={metrics.filter(m => m.value > m.threshold).length > 0 ? 'error.main' : 'success.main'}
                  >
                    {metrics.filter(m => m.value <= m.threshold).length}/{metrics.length}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Métricas OK
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="primary.main">
                    {metrics.filter(m => m.trend === 'up').length}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Em alta
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={4}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" color="success.main">
                    {metrics.filter(m => m.trend === 'down').length}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Em baixa
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Box>

      {/* Rodapé */}
      <Box sx={{ mt: 'auto', pt: 1, borderTop: 1, borderColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          Atualizado a cada {(widget.refreshInterval || 60000) / 1000}s • Período: {timeRange}
        </Typography>
      </Box>
    </Box>
  );
};

export default MetricsWidget;

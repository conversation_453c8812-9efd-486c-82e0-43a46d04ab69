# Solução para Problemas com Servidores Huawei/HarmonyOS

Este documento descreve as melhorias implementadas para resolver os problemas de timeout e interação com servidores Huawei/HarmonyOS.

## Problema

Os servidores Huawei apresentavam os seguintes problemas:

1. **Respostas longas não eram processadas corretamente**, resultando em timeout
2. **Comandos que requerem interação** (Enter/Espaço) não eram tratados adequadamente
3. **Prompts específicos do Huawei** não eram detectados corretamente
4. **Erro de "Too many parameters"** aparecia ao executar comandos que funcionam normalmente quando executados diretamente no servidor
5. **Caracteres invisíveis ou formatação incorreta** eram enviados junto com os comandos
6. **Conexão SSH fechada pelo servidor** após tentativa de executar comandos

## Solução Implementada

Foram realizadas as seguintes melhorias no código para resolver esses problemas:

### Abordagem Completamente Nova

Criamos uma abordagem completamente nova e dedicada para dispositivos Huawei, com um método específico `executeHuaweiCommand` que utiliza configurações e estratégias otimizadas para esses dispositivos.

### 1. Detecção Automática de Dispositivos Huawei

```typescript
// Verificar se é um dispositivo Huawei/HarmonyOS
this.isHuaweiDevice = server.name.toLowerCase().includes('harmony') ||
                      server.name.toLowerCase().includes('huawei') ||
                      server.name.toLowerCase().includes('rtr-pe-rbo')
```

### 2. Limpeza de Comandos

```typescript
// Limpar o comando para remover caracteres invisíveis e espaços extras
const cleanCommand = command.trim().replace(/\s+/g, ' ')
```

### 3. Configurações de Conexão Otimizadas para Huawei

```typescript
// Configurações mais conservadoras para Huawei
config.readyTimeout = 30000 // 30 segundos para timeout de conexão
config.keepaliveInterval = 10000 // 10 segundos para keepalive
config.keepaliveCountMax = 5 // Mais pacotes keepalive sem resposta
```

### 4. Timeouts Estendidos para Dispositivos Huawei

```typescript
// Timeouts mais longos para Huawei
const MAX_EXECUTION_TIME = 90000 // 90 segundos
const INACTIVITY_TIMEOUT = 10000 // 10 segundos
```

### 5. Configurações de Terminal Simplificadas

```typescript
// Iniciar shell interativo com configurações básicas
this.ssh.requestShell({
  term: 'dumb', // Terminal mais simples
  rows: 80,
  cols: 120
})
```

### 6. Detecção de Prompts e Tratamento de Erros

```typescript
shell.on('data', (data: Buffer) => {
  const chunk = data.toString('utf8')
  output += chunk
  lastDataTime = Date.now()
  console.log(`stdout (${chunk.length} bytes): ${chunk.substring(0, 100)}${chunk.length > 100 ? '...' : ''}`)

  // Verificar se temos o prompt de conclusão
  if (chunk.includes('<RTR-PE-RBO-VLG-')) {
    // Se detectamos o prompt, podemos enviar o comando ou finalizar
    if (output.includes(command) && output.includes('Error: Too many parameters')) {
      // Se já enviamos o comando e recebemos erro, finalizar
      console.log('Detectado erro de parâmetros, finalizando comando')
      if (!commandCompleted) {
        commandCompleted = true
        cleanup()
        resolve({
          stdout: output,
          stderr: errorOutput,
          code: 0
        })
      }
    }
  }

  // Verificar se há prompt de paginação e enviar espaço
  if (chunk.includes('More') || chunk.includes('--More--')) {
    console.log('Prompt de paginação detectado, enviando espaço')
    shell.write(' ')
    lastDataTime = Date.now()
  }
})
```

### 7. Sequência de Envio de Comandos com Atrasos Maiores

```typescript
// Aguardar um momento para o shell inicializar
setTimeout(() => {
  // Enviar um Enter para limpar qualquer prompt
  shell.write('\n')

  // Aguardar mais tempo antes de enviar o comando
  setTimeout(() => {
    if (!commandCompleted && shell) {
      console.log(`Enviando comando: ${command}`)
      shell.write(command + '\n')

      // Iniciar verificação de conclusão após enviar o comando
      commandTimeout = setTimeout(checkCompletion, INACTIVITY_TIMEOUT)
    }
  }, 3000) // 3 segundos após o Enter inicial
}, 2000) // 2 segundos para inicialização
```

### 8. Tratamento Específico para Dispositivos Huawei

```typescript
// Para dispositivos Huawei, sempre usar shell interativo com abordagem simplificada
if (this.isHuaweiDevice) {
  console.log('Dispositivo Huawei detectado, usando abordagem simplificada')
  return await this.executeHuaweiCommand(cleanCommand)
}
```

## Como Funciona

1. O sistema identifica automaticamente se o servidor é um dispositivo Huawei/HarmonyOS
2. Limpa o comando para remover caracteres invisíveis e espaços extras
3. Para dispositivos Huawei, usa um método dedicado `executeHuaweiCommand` com:
   - Configurações de conexão otimizadas (keepalive mais frequente, timeouts mais longos)
   - Terminal simplificado (tipo 'dumb' em vez de 'vt100')
   - Timeouts muito mais longos (90 segundos para execução, 10 segundos para inatividade)
4. Durante a execução do comando:
   - Aguarda 2 segundos para o shell inicializar completamente
   - Envia um Enter inicial para limpar qualquer prompt existente
   - Aguarda 3 segundos antes de enviar o comando
   - Detecta prompts de paginação e envia espaço quando necessário
   - Detecta erros de parâmetros e finaliza o comando com a saída disponível
   - Detecta o prompt de conclusão do comando e finaliza a execução

## Benefícios

1. **Abordagem dedicada**: Usa um método específico para dispositivos Huawei
2. **Conexão mais estável**: Configurações otimizadas para evitar desconexões
3. **Respostas completas**: Captura toda a saída do comando, mesmo para respostas longas
4. **Interação automática**: Responde automaticamente a prompts que requerem interação
5. **Detecção de conclusão**: Identifica corretamente quando o comando foi concluído
6. **Timeouts otimizados**: Evita timeouts prematuros para comandos que demoram mais para executar
7. **Tratamento de erros**: Lida com erros de parâmetros e continua a execução
8. **Limpeza de comandos**: Remove caracteres invisíveis e espaços extras que podem causar problemas

## Melhorias Recentes (Dezembro 2024)

### Problemas Identificados com Comandos Maiores

1. **Timeout de inatividade muito alto** (30 segundos) causava espera desnecessária
2. **Detecção de conclusão prematura** finalizava antes de capturar toda a saída
3. **Padrões de paginação limitados** não detectavam todos os tipos de prompts
4. **Delay insuficiente** após detecção de prompt para capturar saída adicional

### Soluções Implementadas

#### 1. Timeout Dinâmico Baseado na Complexidade
```typescript
// Timeout ajustado baseado na complexidade do comando
const INACTIVITY_TIMEOUT = commandComplexity > 3 ? 15000 : 20000; // 15-20 segundos
const PROMPT_COMPLETION_DELAY = 3000; // 3 segundos para capturar saída adicional
```

#### 2. Detecção Aprimorada de Paginação
```typescript
const paginationPatterns = [
  'More', '--More--', '-- More --', 'Press any key to continue',
  'Press SPACE to continue', 'Continue? (y/n)', '(more)',
  'Press any key', 'Hit any key', 'Press <SPACE>'
];
```

#### 3. Verificação de Conclusão Mais Inteligente
```typescript
// Para comandos maiores, verificar se a saída parece completa
const outputLines = output.split('\n');
const hasCommandEcho = output.includes(command);
const hasPromptAtEnd = outputLines[outputLines.length - 1]?.trim().match(/[>\]#$]$/);
```

#### 4. Frequência de Verificação Adaptativa
```typescript
// Para comandos complexos, verificar com mais frequência
const checkInterval = commandComplexity > 3 ? 500 : 1000;
```

#### 5. Melhorias para Comandos Multilinhas
- Timeout de inatividade reduzido para 12-18 segundos baseado na quantidade de comandos
- Delay final de 5 segundos após o último comando para capturar saída completa
- Verificação mais frequente para scripts com muitos comandos

## Limitações

1. A solução atual é específica para dispositivos Huawei/HarmonyOS identificados pelo nome
2. Pode ser necessário ajustar as expressões regulares para detectar outros tipos de prompts específicos
3. Alguns comandos muito complexos ou com muita interação ainda podem falhar

## Mensagem de Commit

```
fix: otimizar captura de saída completa para comandos maiores no Huawei

- Reduzir timeout de inatividade baseado na complexidade do comando (15-20s)
- Melhorar detecção de paginação com mais padrões de prompt
- Aumentar delay após detecção de prompt para capturar saída adicional (3s)
- Implementar verificação de conclusão mais inteligente com análise de saída
- Adicionar frequência de verificação adaptativa para comandos complexos
- Otimizar timeouts para comandos multilinhas (12-18s baseado na quantidade)
- Melhorar logs para diagnóstico de problemas de saída incompleta
```

import React from 'react'
import { 
  Download, 
  Heart, 
  Star, 
  User, 
  Calendar,
  Tag,
  Eye,
  BookmarkPlus
} from 'lucide-react'
import { MarketplaceTemplate, MarketplaceService } from '../../services/marketplaceService'

interface MarketplaceGridProps {
  templates: MarketplaceTemplate[]
  onDownload: (templateId: string) => Promise<void>
  onLike: (templateId: string) => Promise<void>
  onFavorite: (templateId: string) => Promise<void>
  isLoading?: boolean
}

const MarketplaceGrid: React.FC<MarketplaceGridProps> = ({
  templates,
  onDownload,
  onLike,
  onFavorite,
  isLoading = false
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  const renderStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <Star key={i} className="h-3 w-3 fill-yellow-400 text-yellow-400" />
      )
    }

    if (hasHalfStar) {
      stars.push(
        <Star key="half" className="h-3 w-3 fill-yellow-200 text-yellow-400" />
      )
    }

    const emptyStars = 5 - Math.ceil(rating)
    for (let i = 0; i < emptyStars; i++) {
      stars.push(
        <Star key={`empty-${i}`} className="h-3 w-3 text-gray-300" />
      )
    }

    return stars
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
            <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-2/3 mb-4"></div>
            <div className="flex items-center justify-between">
              <div className="h-6 bg-gray-200 rounded w-16"></div>
              <div className="h-6 bg-gray-200 rounded w-16"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {templates.map((template) => (
        <div
          key={template.id}
          className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200"
        >
          {/* Header */}
          <div className="p-6 pb-4">
            <div className="flex items-start justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
                {template.name}
              </h3>
              {template.isFeatured && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  ⭐ Destaque
                </span>
              )}
            </div>

            {template.description && (
              <p className="text-sm text-gray-600 line-clamp-3 mb-4">
                {template.description}
              </p>
            )}

            {/* Category and Tags */}
            <div className="flex flex-wrap gap-2 mb-4">
              {template.category && (
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${MarketplaceService.getCategoryColor(template.category)}`}>
                  {MarketplaceService.getCategoryIcon(template.category)} {template.category}
                </span>
              )}
              {template.tags.slice(0, 2).map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700"
                >
                  <Tag className="h-3 w-3 mr-1" />
                  {tag}
                </span>
              ))}
              {template.tags.length > 2 && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-500">
                  +{template.tags.length - 2}
                </span>
              )}
            </div>

            {/* Stats */}
            <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <Download className="h-4 w-4 mr-1" />
                  {formatNumber(template.downloads)}
                </div>
                <div className="flex items-center">
                  <Heart className={`h-4 w-4 mr-1 ${template.userInteraction?.hasLiked ? 'fill-red-500 text-red-500' : ''}`} />
                  {formatNumber(template.likes)}
                </div>
              </div>
              
              {template.stats.averageRating > 0 && (
                <div className="flex items-center">
                  <div className="flex items-center mr-1">
                    {renderStars(template.stats.averageRating)}
                  </div>
                  <span className="text-xs">
                    ({template.stats.totalReviews})
                  </span>
                </div>
              )}
            </div>

            {/* Author and Date */}
            <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
              <div className="flex items-center">
                <User className="h-3 w-3 mr-1" />
                {template.user.name}
              </div>
              <div className="flex items-center">
                <Calendar className="h-3 w-3 mr-1" />
                {formatDate(template.createdAt)}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="px-6 py-4 bg-gray-50 rounded-b-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => onLike(template.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    template.userInteraction?.hasLiked
                      ? 'bg-red-100 text-red-600 hover:bg-red-200'
                      : 'bg-white text-gray-400 hover:text-red-500 hover:bg-red-50'
                  }`}
                  title="Curtir"
                >
                  <Heart className={`h-4 w-4 ${template.userInteraction?.hasLiked ? 'fill-current' : ''}`} />
                </button>
                
                <button
                  onClick={() => onFavorite(template.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    template.userInteraction?.hasFavorited
                      ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                      : 'bg-white text-gray-400 hover:text-blue-500 hover:bg-blue-50'
                  }`}
                  title="Favoritar"
                >
                  <BookmarkPlus className={`h-4 w-4 ${template.userInteraction?.hasFavorited ? 'fill-current' : ''}`} />
                </button>
                
                <button
                  className="p-2 rounded-lg bg-white text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
                  title="Ver detalhes"
                >
                  <Eye className="h-4 w-4" />
                </button>
              </div>
              
              <button
                onClick={() => onDownload(template.id)}
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
              >
                <Download className="h-4 w-4 mr-1" />
                Baixar
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default MarketplaceGrid

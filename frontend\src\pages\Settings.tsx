import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Switch,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Divider,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  Slider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Palette as ThemeIcon,
  Code as EditorIcon,
  Terminal as CommandIcon,
  Notifications as NotificationIcon,
  ViewModule as InterfaceIcon,
  Build as AdvancedIcon,
  Restore as ResetIcon,
  Download as ExportIcon,
  Upload as ImportIcon,
  Save as SaveIcon
} from '@mui/icons-material';
import { useUserSettings } from '../hooks/useUserSettings';
import { useResponsive } from '../hooks/useResponsive';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
};

const Settings: React.FC = () => {
  const { isMobile } = useResponsive();
  const {
    settings,
    updateSetting,
    updateNestedSetting,
    resetSettings,
    exportSettings,
    importSettings,
    isDefault,
    resetSetting
  } = useUserSettings();

  const [activeTab, setActiveTab] = useState(0);
  const [showResetConfirm, setShowResetConfirm] = useState(false);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleExport = () => {
    const data = exportSettings();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'sem-fronteiras-settings.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (importSettings(content)) {
          alert('Configurações importadas com sucesso!');
        } else {
          alert('Erro ao importar configurações. Verifique o formato do arquivo.');
        }
      };
      reader.readAsText(file);
    }
  };

  const tabs = [
    { label: 'Aparência', icon: <ThemeIcon /> },
    { label: 'Editor', icon: <EditorIcon /> },
    { label: 'Comandos', icon: <CommandIcon /> },
    { label: 'Notificações', icon: <NotificationIcon /> },
    { label: 'Interface', icon: <InterfaceIcon /> },
    { label: 'Avançado', icon: <AdvancedIcon /> }
  ];

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Configurações
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Personalize sua experiência no REMOTEOPS
        </Typography>
      </Box>

      {/* Ações rápidas */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
            <Button
              startIcon={<ExportIcon />}
              onClick={handleExport}
              variant="outlined"
            >
              Exportar Configurações
            </Button>
            
            <Button
              startIcon={<ImportIcon />}
              component="label"
              variant="outlined"
            >
              Importar Configurações
              <input
                type="file"
                accept=".json"
                hidden
                onChange={handleImport}
              />
            </Button>
            
            <Button
              startIcon={<ResetIcon />}
              onClick={() => setShowResetConfirm(true)}
              color="error"
              variant="outlined"
            >
              Restaurar Padrões
            </Button>
          </Box>
          
          {showResetConfirm && (
            <Alert 
              severity="warning" 
              sx={{ mt: 2 }}
              action={
                <Box>
                  <Button 
                    color="inherit" 
                    size="small"
                    onClick={() => setShowResetConfirm(false)}
                  >
                    Cancelar
                  </Button>
                  <Button 
                    color="inherit" 
                    size="small"
                    onClick={() => {
                      resetSettings();
                      setShowResetConfirm(false);
                    }}
                  >
                    Confirmar
                  </Button>
                </Box>
              }
            >
              Tem certeza que deseja restaurar todas as configurações para os valores padrão?
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant={isMobile ? 'scrollable' : 'standard'}
            scrollButtons="auto"
          >
            {tabs.map((tab, index) => (
              <Tab
                key={index}
                label={tab.label}
                icon={tab.icon}
                iconPosition="start"
              />
            ))}
          </Tabs>
        </Box>

        <CardContent>
          {/* Aba Aparência */}
          <TabPanel value={activeTab} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Tema</InputLabel>
                  <Select
                    value={settings.theme}
                    onChange={(e) => updateSetting('theme', e.target.value as any)}
                  >
                    <MenuItem value="light">Claro</MenuItem>
                    <MenuItem value="dark">Escuro</MenuItem>
                    <MenuItem value="system">Automático (Sistema)</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Tamanho da Fonte</InputLabel>
                  <Select
                    value={settings.fontSize}
                    onChange={(e) => updateSetting('fontSize', e.target.value as any)}
                  >
                    <MenuItem value="small">Pequena</MenuItem>
                    <MenuItem value="medium">Média</MenuItem>
                    <MenuItem value="large">Grande</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.compactMode}
                      onChange={(e) => updateSetting('compactMode', e.target.checked)}
                    />
                  }
                  label="Modo Compacto"
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.animationsEnabled}
                      onChange={(e) => updateSetting('animationsEnabled', e.target.checked)}
                    />
                  }
                  label="Animações Habilitadas"
                />
              </Grid>
            </Grid>
          </TabPanel>

          {/* Aba Editor */}
          <TabPanel value={activeTab} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography gutterBottom>Tamanho da Fonte do Editor</Typography>
                <Slider
                  value={settings.editorFontSize}
                  onChange={(_, value) => updateSetting('editorFontSize', value as number)}
                  min={10}
                  max={24}
                  step={1}
                  marks
                  valueLabelDisplay="auto"
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Tema do Editor</InputLabel>
                  <Select
                    value={settings.editorTheme}
                    onChange={(e) => updateSetting('editorTheme', e.target.value as any)}
                  >
                    <MenuItem value="default">Padrão</MenuItem>
                    <MenuItem value="dark">Escuro</MenuItem>
                    <MenuItem value="high-contrast">Alto Contraste</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <List>
                  <ListItem>
                    <ListItemText primary="Syntax Highlighting" />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.syntaxHighlighting}
                        onChange={(e) => updateSetting('syntaxHighlighting', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  <ListItem>
                    <ListItemText primary="Auto Complete" />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.autoComplete}
                        onChange={(e) => updateSetting('autoComplete', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  <ListItem>
                    <ListItemText primary="Quebra de Linha" />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.wordWrap}
                        onChange={(e) => updateSetting('wordWrap', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  <ListItem>
                    <ListItemText primary="Números de Linha" />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.lineNumbers}
                        onChange={(e) => updateSetting('lineNumbers', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Aba Comandos */}
          <TabPanel value={activeTab} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Timeout de Comando (segundos)"
                  type="number"
                  value={settings.commandTimeout}
                  onChange={(e) => updateSetting('commandTimeout', parseInt(e.target.value))}
                  inputProps={{ min: 5, max: 300 }}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Tamanho Máximo do Histórico"
                  type="number"
                  value={settings.maxHistorySize}
                  onChange={(e) => updateSetting('maxHistorySize', parseInt(e.target.value))}
                  inputProps={{ min: 100, max: 10000 }}
                />
              </Grid>
              
              <Grid item xs={12}>
                <List>
                  <ListItem>
                    <ListItemText primary="Salvar Comandos Automaticamente" />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.autoSaveCommands}
                        onChange={(e) => updateSetting('autoSaveCommands', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  <ListItem>
                    <ListItemText primary="Confirmar Antes de Executar" />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.confirmBeforeExecute}
                        onChange={(e) => updateSetting('confirmBeforeExecute', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  <ListItem>
                    <ListItemText primary="Mostrar Preview do Comando" />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.showCommandPreview}
                        onChange={(e) => updateSetting('showCommandPreview', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Aba Notificações */}
          <TabPanel value={activeTab} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.enableNotifications}
                      onChange={(e) => updateSetting('enableNotifications', e.target.checked)}
                    />
                  }
                  label="Habilitar Notificações"
                />
              </Grid>
              
              {settings.enableNotifications && (
                <>
                  <Grid item xs={12}>
                    <List>
                      <ListItem>
                        <ListItemText primary="Som de Notificação" />
                        <ListItemSecondaryAction>
                          <Switch
                            checked={settings.notificationSound}
                            onChange={(e) => updateSetting('notificationSound', e.target.checked)}
                          />
                        </ListItemSecondaryAction>
                      </ListItem>
                      
                      <ListItem>
                        <ListItemText primary="Notificações por Email" />
                        <ListItemSecondaryAction>
                          <Switch
                            checked={settings.emailNotifications}
                            onChange={(e) => updateSetting('emailNotifications', e.target.checked)}
                          />
                        </ListItemSecondaryAction>
                      </ListItem>
                      
                      <ListItem>
                        <ListItemText primary="Notificações no Slack" />
                        <ListItemSecondaryAction>
                          <Switch
                            checked={settings.slackNotifications}
                            onChange={(e) => updateSetting('slackNotifications', e.target.checked)}
                          />
                        </ListItemSecondaryAction>
                      </ListItem>
                    </List>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Tipos de Notificação
                    </Typography>
                    <List>
                      {Object.entries(settings.notificationTypes).map(([key, value]) => (
                        <ListItem key={key}>
                          <ListItemText 
                            primary={
                              key === 'commandSuccess' ? 'Comando Executado com Sucesso' :
                              key === 'commandError' ? 'Erro na Execução de Comando' :
                              key === 'serverConnection' ? 'Conexão com Servidor' :
                              key === 'systemAlerts' ? 'Alertas do Sistema' :
                              key === 'backupStatus' ? 'Status do Backup' : key
                            }
                          />
                          <ListItemSecondaryAction>
                            <Switch
                              checked={value}
                              onChange={(e) => updateNestedSetting(`notificationTypes.${key}`, e.target.checked)}
                            />
                          </ListItemSecondaryAction>
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                </>
              )}
            </Grid>
          </TabPanel>

          {/* Aba Interface */}
          <TabPanel value={activeTab} index={4}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Densidade da Grade</InputLabel>
                  <Select
                    value={settings.gridDensity}
                    onChange={(e) => updateSetting('gridDensity', e.target.value as any)}
                  >
                    <MenuItem value="compact">Compacta</MenuItem>
                    <MenuItem value="comfortable">Confortável</MenuItem>
                    <MenuItem value="spacious">Espaçosa</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Itens por Página na Tabela"
                  type="number"
                  value={settings.tablePageSize}
                  onChange={(e) => updateSetting('tablePageSize', parseInt(e.target.value))}
                  inputProps={{ min: 10, max: 100 }}
                />
              </Grid>
              
              <Grid item xs={12}>
                <List>
                  <ListItem>
                    <ListItemText primary="Barra Lateral Recolhida" />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.sidebarCollapsed}
                        onChange={(e) => updateSetting('sidebarCollapsed', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  <ListItem>
                    <ListItemText primary="Mostrar Barra de Status" />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.showStatusBar}
                        onChange={(e) => updateSetting('showStatusBar', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  <ListItem>
                    <ListItemText primary="Mostrar Barra de Ferramentas" />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.showToolbar}
                        onChange={(e) => updateSetting('showToolbar', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              </Grid>
            </Grid>
          </TabPanel>

          {/* Aba Avançado */}
          <TabPanel value={activeTab} index={5}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Alert severity="warning">
                  Estas configurações são para usuários avançados. Altere apenas se souber o que está fazendo.
                </Alert>
              </Grid>
              
              <Grid item xs={12}>
                <List>
                  <ListItem>
                    <ListItemText 
                      primary="Modo Debug" 
                      secondary="Habilita logs detalhados no console"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.debugMode}
                        onChange={(e) => updateSetting('debugMode', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  <ListItem>
                    <ListItemText 
                      primary="Funcionalidades Experimentais" 
                      secondary="Habilita recursos em desenvolvimento"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.experimentalFeatures}
                        onChange={(e) => updateSetting('experimentalFeatures', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  <ListItem>
                    <ListItemText 
                      primary="Telemetria" 
                      secondary="Enviar dados de uso para melhorar o sistema"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.telemetryEnabled}
                        onChange={(e) => updateSetting('telemetryEnabled', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                  
                  <ListItem>
                    <ListItemText 
                      primary="Backup Automático" 
                      secondary="Backup automático das configurações"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.autoBackup}
                        onChange={(e) => updateSetting('autoBackup', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              </Grid>
            </Grid>
          </TabPanel>
        </CardContent>
      </Card>
    </Container>
  );
};

export default Settings;

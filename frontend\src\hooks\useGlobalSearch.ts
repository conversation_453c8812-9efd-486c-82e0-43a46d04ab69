import { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';

export interface SearchResult {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  type: 'server' | 'command' | 'template' | 'user' | 'group' | 'page' | 'setting';
  category: string;
  url?: string;
  action?: () => void;
  icon?: React.ReactNode;
  metadata?: Record<string, any>;
  score: number;
}

export interface SearchCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  count: number;
}

interface UseGlobalSearchOptions {
  minQueryLength?: number;
  maxResults?: number;
  debounceMs?: number;
}

interface UseGlobalSearchReturn {
  query: string;
  setQuery: (query: string) => void;
  results: SearchResult[];
  categories: SearchCategory[];
  isSearching: boolean;
  selectedCategory: string | null;
  setSelectedCategory: (category: string | null) => void;
  executeResult: (result: SearchResult) => void;
  clearSearch: () => void;
  recentSearches: string[];
  addToRecent: (query: string) => void;
  clearRecent: () => void;
}

// Dados mockados para demonstração - em produção viriam da API
const mockData = {
  servers: [
    { id: '1', name: 'Router Principal', ip: '***********', type: 'MIKROTIK' },
    { id: '2', name: 'Switch Core', ip: '***********', type: 'HUAWEI' },
    { id: '3', name: 'OLT Fibra', ip: '***********', type: 'NOKIA' }
  ],
  commands: [
    { id: '1', name: 'Verificar Interface', command: '/interface print', serverId: '1' },
    { id: '2', name: 'Status Sistema', command: 'display version', serverId: '2' },
    { id: '3', name: 'Lista Usuários', command: 'show users', serverId: '3' }
  ],
  templates: [
    { id: '1', name: 'Backup Configuração', description: 'Template para backup' },
    { id: '2', name: 'Reset Interface', description: 'Reset de interface de rede' }
  ],
  users: [
    { id: '1', name: 'João Silva', email: '<EMAIL>', role: 'ADMIN' },
    { id: '2', name: 'Maria Santos', email: '<EMAIL>', role: 'USER' }
  ],
  pages: [
    { id: 'servers', name: 'Servidores', url: '/', description: 'Gerenciar servidores' },
    { id: 'history', name: 'Histórico', url: '/command-history', description: 'Histórico de comandos' },
    { id: 'templates', name: 'Templates', url: '/command-templates', description: 'Templates de comandos' },
    { id: 'users', name: 'Usuários', url: '/users', description: 'Gerenciar usuários' },
    { id: 'monitoring', name: 'Monitoramento', url: '/monitoring', description: 'Dashboard de monitoramento' },
    { id: 'audit', name: 'Auditoria', url: '/audit', description: 'Logs de auditoria' },
    { id: 'settings', name: 'Configurações', url: '/settings', description: 'Configurações do sistema' }
  ]
};

const RECENT_SEARCHES_KEY = 'sem-fronteiras-recent-searches';
const MAX_RECENT_SEARCHES = 10;

export const useGlobalSearch = (options: UseGlobalSearchOptions = {}): UseGlobalSearchReturn => {
  const {
    minQueryLength = 2,
    maxResults = 50,
    debounceMs = 300
  } = options;

  const navigate = useNavigate();
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  // Carregar buscas recentes
  useEffect(() => {
    try {
      const stored = localStorage.getItem(RECENT_SEARCHES_KEY);
      if (stored) {
        setRecentSearches(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Erro ao carregar buscas recentes:', error);
    }
  }, []);

  // Salvar buscas recentes
  useEffect(() => {
    try {
      localStorage.setItem(RECENT_SEARCHES_KEY, JSON.stringify(recentSearches));
    } catch (error) {
      console.error('Erro ao salvar buscas recentes:', error);
    }
  }, [recentSearches]);

  // Função de busca
  const performSearch = useCallback(async (searchQuery: string): Promise<SearchResult[]> => {
    if (searchQuery.length < minQueryLength) {
      return [];
    }

    const lowerQuery = searchQuery.toLowerCase();
    const searchResults: SearchResult[] = [];

    // Buscar servidores
    mockData.servers.forEach(server => {
      const score = calculateScore(lowerQuery, [server.name, server.ip, server.type]);
      if (score > 0) {
        searchResults.push({
          id: `server-${server.id}`,
          title: server.name,
          subtitle: `${server.ip} (${server.type})`,
          description: `Servidor ${server.type}`,
          type: 'server',
          category: 'Servidores',
          url: `/servers/${server.id}`,
          metadata: server,
          score
        });
      }
    });

    // Buscar comandos
    mockData.commands.forEach(command => {
      const score = calculateScore(lowerQuery, [command.name, command.command]);
      if (score > 0) {
        const server = mockData.servers.find(s => s.id === command.serverId);
        searchResults.push({
          id: `command-${command.id}`,
          title: command.name,
          subtitle: command.command,
          description: server ? `Comando para ${server.name}` : 'Comando',
          type: 'command',
          category: 'Comandos',
          metadata: { ...command, server },
          score
        });
      }
    });

    // Buscar templates
    mockData.templates.forEach(template => {
      const score = calculateScore(lowerQuery, [template.name, template.description]);
      if (score > 0) {
        searchResults.push({
          id: `template-${template.id}`,
          title: template.name,
          subtitle: template.description,
          description: 'Template de comando',
          type: 'template',
          category: 'Templates',
          url: `/command-templates/${template.id}`,
          metadata: template,
          score
        });
      }
    });

    // Buscar usuários
    mockData.users.forEach(user => {
      const score = calculateScore(lowerQuery, [user.name, user.email]);
      if (score > 0) {
        searchResults.push({
          id: `user-${user.id}`,
          title: user.name,
          subtitle: user.email,
          description: `Usuário ${user.role}`,
          type: 'user',
          category: 'Usuários',
          url: `/users/${user.id}`,
          metadata: user,
          score
        });
      }
    });

    // Buscar páginas
    mockData.pages.forEach(page => {
      const score = calculateScore(lowerQuery, [page.name, page.description]);
      if (score > 0) {
        searchResults.push({
          id: `page-${page.id}`,
          title: page.name,
          subtitle: page.description,
          description: 'Página do sistema',
          type: 'page',
          category: 'Páginas',
          url: page.url,
          metadata: page,
          score
        });
      }
    });

    // Ordenar por score e limitar resultados
    return searchResults
      .sort((a, b) => b.score - a.score)
      .slice(0, maxResults);
  }, [minQueryLength, maxResults]);

  // Debounced search
  useEffect(() => {
    if (query.length < minQueryLength) {
      setResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    const timeoutId = setTimeout(async () => {
      try {
        const searchResults = await performSearch(query);
        setResults(searchResults);
      } catch (error) {
        console.error('Erro na busca:', error);
        setResults([]);
      } finally {
        setIsSearching(false);
      }
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [query, minQueryLength, debounceMs, performSearch]);

  // Filtrar resultados por categoria
  const filteredResults = useMemo(() => {
    if (!selectedCategory) return results;
    return results.filter(result => result.category === selectedCategory);
  }, [results, selectedCategory]);

  // Calcular categorias com contadores
  const categories = useMemo(() => {
    const categoryMap = new Map<string, number>();

    results.forEach(result => {
      const count = categoryMap.get(result.category) || 0;
      categoryMap.set(result.category, count + 1);
    });

    return Array.from(categoryMap.entries()).map(([name, count]) => ({
      id: name.toLowerCase(),
      name,
      icon: getCategoryIcon(name),
      count
    }));
  }, [results]);

  const executeResult = useCallback((result: SearchResult) => {
    if (result.action) {
      result.action();
    } else if (result.url) {
      navigate(result.url);
    }

    // Adicionar à busca recente
    addToRecent(query);
  }, [navigate, query]);

  const clearSearch = useCallback(() => {
    setQuery('');
    setResults([]);
    setSelectedCategory(null);
  }, []);

  const addToRecent = useCallback((searchQuery: string) => {
    if (!searchQuery.trim()) return;

    setRecentSearches(prev => {
      const filtered = prev.filter(q => q !== searchQuery);
      return [searchQuery, ...filtered].slice(0, MAX_RECENT_SEARCHES);
    });
  }, []);

  const clearRecent = useCallback(() => {
    setRecentSearches([]);
  }, []);

  return {
    query,
    setQuery,
    results: filteredResults,
    categories,
    isSearching,
    selectedCategory,
    setSelectedCategory,
    executeResult,
    clearSearch,
    recentSearches,
    addToRecent,
    clearRecent
  };
};

// Função para calcular score de relevância
function calculateScore(query: string, fields: string[]): number {
  let score = 0;

  fields.forEach(field => {
    if (!field) return;

    const lowerField = field.toLowerCase();

    // Correspondência exata
    if (lowerField === query) {
      score += 100;
    }
    // Começa com a query
    else if (lowerField.startsWith(query)) {
      score += 80;
    }
    // Contém a query
    else if (lowerField.includes(query)) {
      score += 60;
    }
    // Correspondência de palavras
    else {
      const queryWords = query.split(' ');
      const fieldWords = lowerField.split(' ');

      queryWords.forEach(queryWord => {
        fieldWords.forEach(fieldWord => {
          if (fieldWord.includes(queryWord)) {
            score += 20;
          }
        });
      });
    }
  });

  return score;
}

// Função para obter ícone da categoria
function getCategoryIcon(category: string): React.ReactNode {
  // Os ícones serão definidos no componente que usa este hook
  return null;
}

export default useGlobalSearch;

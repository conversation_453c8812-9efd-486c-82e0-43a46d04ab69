#!/bin/bash

# RemoteOps Docker Development Script
# Simple Docker setup for development and testing

set -e

# Configuration
PROJECT_NAME="remoteops-dev"
COMPOSE_FILE="docker-compose.yml"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed and running
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        echo "Please install Docker from: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker is not running"
        echo "Please start Docker and try again"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        echo "Please install Docker Compose from: https://docs.docker.com/compose/install/"
        exit 1
    fi
    
    log_success "Docker and Docker Compose are available"
}

# Create development Docker Compose file
create_docker_compose() {
    log_info "Creating Docker Compose configuration..."
    
    cat > "$PROJECT_ROOT/docker-compose.yml" << 'EOF'
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: remoteops-postgres
    environment:
      POSTGRES_DB: remoteops
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: remoteops-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: remoteops-backend
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/remoteops
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - PORT=3000
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Python Microservice
  python-service:
    build:
      context: ./python-microservice
      dockerfile: Dockerfile
    container_name: remoteops-python
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=********************************************/remoteops
    ports:
      - "8000:8000"
    volumes:
      - ./python-microservice:/app
    depends_on:
      - redis
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: remoteops-frontend
    environment:
      - REACT_APP_API_URL=http://localhost:3000
      - REACT_APP_PYTHON_API_URL=http://localhost:8000
    ports:
      - "3001:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    stdin_open: true
    tty: true
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
EOF

    log_success "Docker Compose configuration created"
}

# Create Dockerfiles if they don't exist
create_dockerfiles() {
    log_info "Creating Dockerfiles..."
    
    # Backend Dockerfile
    if [ ! -f "$PROJECT_ROOT/backend/Dockerfile" ]; then
        cat > "$PROJECT_ROOT/backend/Dockerfile" << 'EOF'
FROM node:18-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start application
CMD ["npm", "start"]
EOF
    fi
    
    # Frontend Dockerfile for development
    if [ ! -f "$PROJECT_ROOT/frontend/Dockerfile.dev" ]; then
        cat > "$PROJECT_ROOT/frontend/Dockerfile.dev" << 'EOF'
FROM node:18-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm install

# Copy source code
COPY . .

# Expose port
EXPOSE 3000

# Start development server
CMD ["npm", "start"]
EOF
    fi
    
    # Python Dockerfile
    if [ ! -f "$PROJECT_ROOT/python-microservice/Dockerfile" ]; then
        cat > "$PROJECT_ROOT/python-microservice/Dockerfile" << 'EOF'
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY . .

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

# Start application
CMD ["python", "main.py"]
EOF
    fi
    
    log_success "Dockerfiles created"
}

# Setup environment
setup_environment() {
    log_info "Setting up environment..."
    
    cd "$PROJECT_ROOT"
    
    if [ ! -f ".env" ]; then
        cat > .env << EOF
# RemoteOps Docker Environment
NODE_ENV=development
JWT_SECRET=$(openssl rand -base64 32)
DATABASE_URL=********************************************/remoteops
REDIS_URL=redis://redis:6379
PORT=3000
FRONTEND_URL=http://localhost:3001
PYTHON_MICROSERVICE_URL=http://localhost:8000

# Docker specific
COMPOSE_PROJECT_NAME=$PROJECT_NAME
EOF
        log_success "Environment file created"
    fi
}

# Build and start services
start_services() {
    log_info "Building and starting services..."
    
    cd "$PROJECT_ROOT"
    
    # Build images
    log_info "Building Docker images..."
    docker-compose -p "$PROJECT_NAME" build
    
    # Start services
    log_info "Starting services..."
    docker-compose -p "$PROJECT_NAME" up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Run database migrations
    log_info "Running database migrations..."
    docker-compose -p "$PROJECT_NAME" exec backend npx prisma migrate dev --name init || true
    
    log_success "Services started successfully"
}

# Stop services
stop_services() {
    log_info "Stopping services..."
    
    cd "$PROJECT_ROOT"
    docker-compose -p "$PROJECT_NAME" down
    
    log_success "Services stopped"
}

# Show service status
show_status() {
    log_info "Checking service status..."
    
    cd "$PROJECT_ROOT"
    
    echo -e "\n${CYAN}Docker Services Status:${NC}"
    echo "======================="
    docker-compose -p "$PROJECT_NAME" ps
    
    echo -e "\n${CYAN}Service Health:${NC}"
    echo "==============="
    
    # Check each service
    services=("backend:3000/api/health" "frontend:3001" "python-service:8000/health")
    
    for service in "${services[@]}"; do
        name=$(echo $service | cut -d: -f1)
        url="http://localhost:$(echo $service | cut -d: -f2)"
        
        if curl -s "$url" >/dev/null 2>&1; then
            echo -e "$name: ${GREEN}HEALTHY${NC}"
        else
            echo -e "$name: ${RED}UNHEALTHY${NC}"
        fi
    done
    
    echo -e "\n${CYAN}Application URLs:${NC}"
    echo "=================="
    echo -e "Frontend:    ${GREEN}http://localhost:3001${NC}"
    echo -e "Backend API: ${GREEN}http://localhost:3000${NC}"
    echo -e "Python API:  ${GREEN}http://localhost:8000${NC}"
    echo -e "Database:    ${GREEN}localhost:5432${NC}"
    echo -e "Redis:       ${GREEN}localhost:6379${NC}"
    
    echo -e "\n${CYAN}Default Credentials:${NC}"
    echo "===================="
    echo -e "Email:    ${GREEN}<EMAIL>${NC}"
    echo -e "Password: ${GREEN}admin123${NC}"
}

# Show logs
show_logs() {
    cd "$PROJECT_ROOT"
    
    if [ -n "$1" ]; then
        docker-compose -p "$PROJECT_NAME" logs -f "$1"
    else
        docker-compose -p "$PROJECT_NAME" logs -f
    fi
}

# Clean up Docker resources
cleanup() {
    log_info "Cleaning up Docker resources..."
    
    cd "$PROJECT_ROOT"
    
    # Stop and remove containers
    docker-compose -p "$PROJECT_NAME" down -v
    
    # Remove images
    docker-compose -p "$PROJECT_NAME" down --rmi all
    
    # Clean up unused Docker resources
    docker system prune -f
    
    log_success "Cleanup completed"
}

# Show help
show_help() {
    echo "RemoteOps Docker Management Script"
    echo "=================================="
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start      Build and start all services"
    echo "  stop       Stop all services"
    echo "  restart    Restart all services"
    echo "  status     Show service status"
    echo "  logs       Show logs (optionally for specific service)"
    echo "  shell      Open shell in service container"
    echo "  cleanup    Stop services and clean up Docker resources"
    echo "  help       Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start                    # Start all services"
    echo "  $0 logs backend             # Show backend logs"
    echo "  $0 shell backend            # Open shell in backend container"
    echo "  $0 status                   # Check service status"
}

# Open shell in container
open_shell() {
    local service=${1:-backend}
    
    cd "$PROJECT_ROOT"
    
    log_info "Opening shell in $service container..."
    docker-compose -p "$PROJECT_NAME" exec "$service" /bin/sh
}

# Main execution
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    RemoteOps Docker Management${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    check_docker
    
    case "${1:-start}" in
        "start")
            create_docker_compose
            create_dockerfiles
            setup_environment
            start_services
            show_status
            
            echo ""
            log_success "🎉 RemoteOps is running with Docker!"
            echo ""
            log_info "Next steps:"
            echo "1. Access the application at http://localhost:3001"
            echo "2. <NAME_EMAIL> / admin123"
            echo "3. Use '$0 logs' to view application logs"
            echo "4. Use '$0 stop' to stop all services"
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 3
            start_services
            show_status
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$2"
            ;;
        "shell")
            open_shell "$2"
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

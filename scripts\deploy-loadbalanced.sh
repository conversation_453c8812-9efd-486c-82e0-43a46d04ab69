#!/bin/bash

# RemoteOps Docker Automation Script
# Universal deployment with Docker and load balancing

set -e

echo "🚀 Starting RemoteOps Docker Deployment..."

# Configuration
COMPOSE_FILE="docker-compose.loadbalancer.yml"
PROJECT_NAME="remoteops-lb"
BACKUP_DIR="./backups/$(date +%Y%m%d_%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed"
        exit 1
    fi

    if [ ! -f "$COMPOSE_FILE" ]; then
        log_error "Docker Compose file not found: $COMPOSE_FILE"
        exit 1
    fi

    log_success "Prerequisites check passed"
}

# Create SSL certificates if they don't exist
setup_ssl() {
    log_info "Setting up SSL certificates..."

    mkdir -p nginx/ssl

    if [ ! -f "nginx/ssl/remoteops.crt" ] || [ ! -f "nginx/ssl/remoteops.key" ]; then
        log_warning "SSL certificates not found, generating self-signed certificates..."

        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout nginx/ssl/remoteops.key \
            -out nginx/ssl/remoteops.crt \
            -subj "/C=US/ST=State/L=City/O=RemoteOps/CN=remoteops.com"

        log_success "Self-signed SSL certificates generated"
    else
        log_success "SSL certificates found"
    fi
}

# Create necessary directories
setup_directories() {
    log_info "Setting up directories..."

    mkdir -p nginx/logs
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    mkdir -p monitoring/logstash/pipeline
    mkdir -p postgres/init
    mkdir -p $BACKUP_DIR

    log_success "Directories created"
}

# Generate environment file
setup_environment() {
    log_info "Setting up environment variables..."

    if [ ! -f ".env" ]; then
        log_warning ".env file not found, creating from template..."

        cat > .env << EOF
# RemoteOps Load Balanced Environment
NODE_ENV=production
JWT_SECRET=$(openssl rand -base64 32)
DATABASE_URL=********************************************/remoteops
REDIS_URL=redis://redis:6379

# Build info
BUILD_NUMBER=$(date +%Y%m%d%H%M%S)
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Monitoring
PROMETHEUS_URL=http://prometheus:9090
GRAFANA_URL=http://grafana:3000
EOF

        log_success "Environment file created"
    else
        log_success "Environment file found"
    fi
}

# Backup current deployment
backup_current() {
    log_info "Creating backup of current deployment..."

    if docker-compose -p $PROJECT_NAME ps | grep -q "Up"; then
        log_info "Backing up database..."
        docker-compose -p $PROJECT_NAME exec -T postgres pg_dump -U postgres remoteops > "$BACKUP_DIR/database.sql"

        log_info "Backing up Redis data..."
        docker-compose -p $PROJECT_NAME exec -T redis redis-cli BGSAVE

        log_success "Backup completed: $BACKUP_DIR"
    else
        log_warning "No running deployment found to backup"
    fi
}

# Build images
build_images() {
    log_info "Building Docker images..."

    docker-compose -f $COMPOSE_FILE build --parallel

    log_success "Images built successfully"
}

# Deploy with rolling update
deploy_rolling() {
    log_info "Starting rolling deployment..."

    # Start infrastructure services first
    log_info "Starting infrastructure services..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d postgres redis prometheus grafana elasticsearch

    # Wait for infrastructure to be ready
    log_info "Waiting for infrastructure services..."
    sleep 30

    # Start backend instances one by one
    log_info "Starting backend instances..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d backend-1
    sleep 15

    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d backend-2
    sleep 15

    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d backend-3
    sleep 15

    # Start Python microservices
    log_info "Starting Python microservices..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d python-1 python-2
    sleep 10

    # Start frontend instances
    log_info "Starting frontend instances..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d frontend-1 frontend-2
    sleep 10

    # Start load balancer last
    log_info "Starting load balancer..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d nginx

    # Start monitoring services
    log_info "Starting monitoring services..."
    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d logstash kibana

    log_success "Rolling deployment completed"
}

# Health check
health_check() {
    log_info "Performing health checks..."

    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        log_info "Health check attempt $attempt/$max_attempts..."

        if curl -f -s http://localhost/api/health > /dev/null; then
            log_success "Application is healthy!"
            return 0
        fi

        sleep 10
        ((attempt++))
    done

    log_error "Health check failed after $max_attempts attempts"
    return 1
}

# Show deployment status
show_status() {
    log_info "Deployment Status:"
    echo ""

    docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME ps

    echo ""
    log_info "Service URLs:"
    echo "🌐 Application: https://localhost"
    echo "📊 Grafana: http://localhost:3001 (admin/admin)"
    echo "🔍 Prometheus: http://localhost:9090"
    echo "📋 Kibana: http://localhost:5601"
    echo "🏥 Health Check: http://localhost/api/health"
    echo "📈 Metrics: http://localhost/api/health/metrics"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    docker system prune -f
    log_success "Cleanup completed"
}

# Main deployment flow
main() {
    echo "🏢 RemoteOps Load Balanced Deployment"
    echo "======================================"

    check_prerequisites
    setup_ssl
    setup_directories
    setup_environment
    backup_current
    build_images
    deploy_rolling

    if health_check; then
        show_status
        log_success "🎉 Deployment completed successfully!"

        echo ""
        log_info "Next steps:"
        echo "1. Configure DNS to point to this server"
        echo "2. Replace self-signed certificates with real SSL certificates"
        echo "3. Configure monitoring alerts"
        echo "4. Set up backup schedules"
        echo "5. Configure log retention policies"

    else
        log_error "❌ Deployment failed health check"

        log_info "Checking logs for errors..."
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME logs --tail=50

        exit 1
    fi
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "status")
        show_status
        ;;
    "health")
        health_check
        ;;
    "cleanup")
        cleanup
        ;;
    "stop")
        log_info "Stopping all services..."
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME down
        log_success "All services stopped"
        ;;
    "restart")
        log_info "Restarting deployment..."
        docker-compose -f $COMPOSE_FILE -p $PROJECT_NAME restart
        health_check
        show_status
        ;;
    *)
        echo "Usage: $0 {deploy|status|health|cleanup|stop|restart}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Full deployment with load balancing"
        echo "  status   - Show current deployment status"
        echo "  health   - Run health check"
        echo "  cleanup  - Clean up Docker resources"
        echo "  stop     - Stop all services"
        echo "  restart  - Restart all services"
        exit 1
        ;;
esac

import React, { Fragment, useState, useEffect, useRef, useCallback } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { SSHServer, CommandResult, Command } from '../types/server'
import ConfirmModal from './ConfirmModal' // Import ConfirmModal
import { executeCommand, updateCommandsOrder } from '../services/api'
import { ChevronLeft, ChevronRight, Maximize2, Minimize2, ListChecks, Clock, SortAsc, GripVertical } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

interface CommandModalProps {
  isOpen: boolean
  onClose: () => void
  server: SSHServer & { commands: Command[] }
}



export default function CommandModal({ isOpen, onClose, server }: CommandModalProps) {
  const { user } = useAuth()
  const isAdmin = user?.role === 'ADMIN'

  const [commandResult, setCommandResult] = useState<CommandResult | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isMaximized, setIsMaximized] = useState(false)
  const [showCommands, setShowCommands] = useState(true)
  const [executionTime, setExecutionTime] = useState(0)
  const [commands, setCommands] = useState<Command[]>([])
  const [isSaving, setIsSaving] = useState(false)
  const executionTimerRef = useRef<any | null>(null)

  // State for confirmation modal
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false)
  const [selectedCommandToExecute, setSelectedCommandToExecute] = useState<Command | null>(null)

  const handleCommandExecution = async (command: Command) => {
    try {
      // Iniciar o timer
      setExecutionTime(0)
      setIsLoading(true)

      // Iniciar o timer que atualiza a cada segundo
      if (executionTimerRef.current) {
        clearInterval(executionTimerRef.current)
      }

      executionTimerRef.current = setInterval(() => {
        setExecutionTime(prev => prev + 1)
      }, 1000)

      const startTime = Date.now()
      const result = await executeCommand(server.id, command.id)
      const endTime = Date.now()

      // Calcular o tempo total de execução em segundos
      const totalExecutionTime = Math.round((endTime - startTime) / 1000)
      setExecutionTime(totalExecutionTime)

      setCommandResult(result)
    } catch (error) {
      console.error('Erro ao executar comando:', error)
    } finally {
      // Parar o timer
      if (executionTimerRef.current) {
        clearInterval(executionTimerRef.current)
        executionTimerRef.current = null
      }
      setIsLoading(false)
    }
  }

  const handleEscapeKey = (event: KeyboardEvent) => {
    if (event.key === 'Escape' && isMaximized) {
      setIsMaximized(false)
      event.preventDefault()
      event.stopPropagation()
    }
  }

  useEffect(() => {
    window.addEventListener('keydown', handleEscapeKey)
    return () => window.removeEventListener('keydown', handleEscapeKey)
  }, [isMaximized])

  // Inicializar os comandos quando o componente for montado ou o servidor mudar
  useEffect(() => {
    if (server && server.commands) {
      // Ordenar os comandos por ordem
      const sortedCommands = [...server.commands].sort((a, b) => {
        // Se ambos têm order, ordenar por order
        if (a.order !== undefined && b.order !== undefined) {
          return a.order - b.order;
        }
        // Se apenas um tem order, o que tem order vem primeiro
        if (a.order !== undefined) return -1;
        if (b.order !== undefined) return 1;
        // Se nenhum tem order, manter a ordem original
        return 0;
      });
      setCommands(sortedCommands);
    }
  }, [server]);

  // Adicionando estilos CSS para o drag and drop
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .command-item.dragging {
        opacity: 0.5;
        border: 1px dashed #666;
      }
      .command-item {
        transition: background-color 0.2s;
      }
      .command-item:hover {
        background-color: rgba(75, 85, 99, 0.3);
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Estado para controlar o item sendo arrastado
  const [draggedId, setDraggedId] = useState<string | null>(null);
  const [dragOverId, setDragOverId] = useState<string | null>(null);

  // Função para lidar com o início do arrasto
  const handleDragStart = useCallback((e: React.DragEvent<HTMLDivElement>, id: string) => {
    // Apenas administradores podem reordenar
    if (!isAdmin) return;

    setDraggedId(id);
    e.dataTransfer.setData('text/plain', id);
    e.currentTarget.classList.add('dragging');
  }, [isAdmin]);

  // Função para lidar com o arrasto sobre um item
  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>, id: string) => {
    // Apenas administradores podem reordenar
    if (!isAdmin) return;

    e.preventDefault();
    setDragOverId(id);
  }, [isAdmin]);

  // Função para lidar com o fim do arrasto
  const handleDrop = useCallback(async (e: React.DragEvent<HTMLDivElement>, id: string) => {
    // Apenas administradores podem reordenar
    if (!isAdmin) return;

    e.preventDefault();

    if (!draggedId || draggedId === id) {
      return;
    }

    const draggedIndex = commands.findIndex(cmd => cmd.id === draggedId);
    const dropIndex = commands.findIndex(cmd => cmd.id === id);

    if (draggedIndex === -1 || dropIndex === -1) {
      return;
    }

    const items = Array.from(commands);
    const [reorderedItem] = items.splice(draggedIndex, 1);
    items.splice(dropIndex, 0, reorderedItem);

    // Atualizar a ordem dos comandos
    const updatedCommands = items.map((item, idx) => ({
      ...item,
      order: idx
    }));

    setCommands(updatedCommands);

    // Salvar a nova ordem no backend
    try {
      setIsSaving(true);
      await updateCommandsOrder(server.id, updatedCommands.map(cmd => ({
        id: cmd.id,
        order: cmd.order || 0
      })));
    } catch (error) {
      console.error('Erro ao atualizar ordem dos comandos:', error);
    } finally {
      setIsSaving(false);
      setDraggedId(null);
      setDragOverId(null);
    }
  }, [commands, draggedId, server.id, isAdmin]);

  // Função para lidar com o fim do arrasto (quando solta fora de um alvo válido)
  const handleDragEnd = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    // Apenas administradores podem reordenar
    if (!isAdmin) return;

    e.currentTarget.classList.remove('dragging');
    setDraggedId(null);
    setDragOverId(null);
  }, [isAdmin]);

  // Função para ordenar os comandos alfabeticamente
  const handleAlphabeticalSort = useCallback(async () => {
    // Apenas administradores podem reordenar
    if (!isAdmin) return;

    const sortedCommands = [...commands].sort((a, b) =>
      a.name.localeCompare(b.name, 'pt-BR')
    );

    // Atualizar a ordem dos comandos
    const updatedCommands = sortedCommands.map((item, index) => ({
      ...item,
      order: index
    }));

    setCommands(updatedCommands);

    // Salvar a nova ordem no backend
    try {
      setIsSaving(true);
      await updateCommandsOrder(server.id, updatedCommands.map(cmd => ({
        id: cmd.id,
        order: cmd.order || 0
      })));
    } catch (error) {
      console.error('Erro ao atualizar ordem dos comandos:', error);
    } finally {
      setIsSaving(false);
    }
  }, [commands, server.id, isAdmin]);

  // Limpar o timer quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (executionTimerRef.current) {
        clearInterval(executionTimerRef.current)
        executionTimerRef.current = null
      }
    }
  }, [])

  // Function to initiate command execution confirmation
  const initiateCommandExecution = (command: Command) => {
    setSelectedCommandToExecute(command)
    setIsConfirmModalOpen(true)
  }

  // Function to handle confirmed command execution
  const handleConfirmExecuteCommand = async () => {
    if (selectedCommandToExecute) {
      await handleCommandExecution(selectedCommandToExecute)
    }
    setIsConfirmModalOpen(false)
    setSelectedCommandToExecute(null)
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-10"
        onClose={() => {
          if (isMaximized) {
            setIsMaximized(false)
          } else {
            onClose()
          }
        }}
      >
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className={`flex min-h-full items-${isMaximized ? 'start' : 'center'} justify-center p-0`}>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel
                className={`w-full transform overflow-hidden bg-gray-900 text-left align-middle shadow-xl transition-all
                  ${isMaximized ? 'h-screen rounded-none' : 'max-w-4xl min-h-[400px] rounded-2xl m-4'}`}
              >
                <div className="flex flex-col h-full">
                  {/* Cabeçalho */}
                  <div className="flex justify-between items-center p-4 border-b border-gray-700">
                    <Dialog.Title as="h3" className="text-lg font-medium text-gray-100">
                      Terminal - {server.name}
                    </Dialog.Title>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setIsMaximized(!isMaximized)}
                        className="text-gray-400 hover:text-gray-200 p-1 rounded-lg hover:bg-gray-700"
                        title={isMaximized ? "Restaurar (Esc)" : "Maximizar"}
                      >
                        {isMaximized ? <Minimize2 className="h-5 w-5" /> : <Maximize2 className="h-5 w-5" />}
                      </button>
                      <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-200 p-1 rounded-lg hover:bg-gray-700"
                        title="Fechar"
                      >
                        ×
                      </button>
                    </div>
                  </div>

                  <div className="flex flex-1 overflow-hidden">
                    {/* Área do Terminal */}
                    <div className="flex-1 overflow-hidden flex flex-col">
                      <div className="flex-1 bg-black p-4 font-mono text-sm text-gray-100 overflow-y-auto min-h-[300px]">
                        {isLoading ? (
                          <div className="flex flex-col items-center justify-center h-full gap-2">
                            <div className="animate-pulse text-gray-500">Executando comando...</div>
                            <div className="flex items-center text-gray-400 text-sm">
                              <Clock className="h-4 w-4 mr-1" />
                              <span>Tempo decorrido: {executionTime}s</span>
                            </div>
                          </div>
                        ) : commandResult ? (
                          <div className="space-y-2">
                            <div className="flex items-center text-gray-400 text-xs mb-2">
                              <Clock className="h-3 w-3 mr-1" />
                              <span>Tempo de execução: {executionTime}s</span>
                            </div>
                            {commandResult.stdout && (
                              <pre className="whitespace-pre-wrap text-green-400">
                                {commandResult.stdout}
                              </pre>
                            )}
                            {commandResult.stderr && (
                              <pre className="whitespace-pre-wrap text-red-400">
                                {commandResult.stderr}
                              </pre>
                            )}
                            {!commandResult.stdout && !commandResult.stderr && (
                              <div className="text-gray-500">
                                Comando executado sem saída
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-gray-500">
                            {server.commands?.length > 0
                              ? 'Selecione um comando para executar'
                              : 'Este servidor não possui comandos cadastrados'}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Lista de Comandos */}
                    {server.commands?.length > 0 && (
                      <div
                        className={`relative border-l border-gray-700 bg-gray-800 transition-all duration-300 ${
                          showCommands
                            ? 'w-72'
                            : 'w-8 hover:bg-gray-700 cursor-pointer'
                        }`}
                        onClick={() => !showCommands && setShowCommands(true)}
                        role={!showCommands ? "button" : undefined}
                        title={!showCommands ? "Mostrar comandos" : undefined}
                      >
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowCommands(!showCommands);
                          }}
                          className={`absolute ${
                            showCommands
                              ? '-left-3 border border-gray-600 shadow-lg'
                              : 'left-0'
                          } top-1/2 -translate-y-1/2 p-1.5 flex items-center justify-center text-gray-200 bg-gray-600 rounded-full hover:bg-gray-500 hover:text-white transition-all z-10 shadow-md`}
                          title={showCommands ? "Ocultar comandos" : "Mostrar comandos"}
                        >
                          {showCommands ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
                        </button>

                        {showCommands && (
                          <div className="h-full p-4 space-y-2 overflow-y-auto">
                            <div className="flex items-center justify-between gap-2 mb-3 bg-gray-700 p-2 rounded-md">
                              <div className="flex items-center gap-2">
                                <ListChecks className="h-4 w-4 text-gray-300" />
                                <h4 className="text-sm font-medium text-gray-300">Menu de Comandos</h4>
                              </div>
                              {isAdmin && (
                                <button
                                  onClick={handleAlphabeticalSort}
                                  disabled={isLoading || isSaving}
                                  className="p-1 rounded text-gray-300 hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                  title="Ordenar alfabeticamente"
                                >
                                  <SortAsc className="h-4 w-4" />
                                </button>
                              )}
                            </div>

                            {isSaving && (
                              <div className="text-xs text-gray-400 mb-2 animate-pulse">
                                Salvando ordem...
                              </div>
                            )}

                            <div className="space-y-2">
                              {commands.map((command) => (
                                <div
                                  key={command.id}
                                  className={`flex items-start gap-1 rounded command-item
                                    ${draggedId === command.id ? 'dragging' : ''}
                                    ${dragOverId === command.id ? 'bg-gray-700' : ''}`}
                                  draggable={isAdmin && !isLoading && !isSaving}
                                  onDragStart={(e) => handleDragStart(e, command.id)}
                                  onDragOver={(e) => handleDragOver(e, command.id)}
                                  onDrop={(e) => handleDrop(e, command.id)}
                                  onDragEnd={handleDragEnd}
                                >
                                  {isAdmin && (
                                    <div
                                      className="p-2 text-gray-400 cursor-grab"
                                      title="Arrastar para reordenar"
                                    >
                                      <GripVertical className="h-4 w-4" />
                                    </div>
                                  )}
                                  <button
                                    onClick={() => initiateCommandExecution(command)} // Modified onClick
                                    disabled={isLoading}
                                    className="flex-1 text-left p-2 rounded text-sm text-gray-300 hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    title={command.description || command.command}
                                  >
                                    <span className="block font-medium">{command.name}</span>
                                    {command.description && (
                                      <span className="block text-xs text-gray-400 mt-1">{command.description}</span>
                                    )}
                                  </button>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
                {/* Confirmation Modal */}
                <ConfirmModal
                  isOpen={isConfirmModalOpen}
                  onClose={() => {
                    setIsConfirmModalOpen(false)
                    setSelectedCommandToExecute(null)
                  }}
                  onConfirm={handleConfirmExecuteCommand}
                  title="Confirmar Execução"
                  message={`Tem certeza que deseja executar o comando "${selectedCommandToExecute?.name}"?`}
                />
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}
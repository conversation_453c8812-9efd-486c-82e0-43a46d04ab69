#!/bin/bash

# ===========================================
# SCRIPT DE GERENCIAMENTO PARA PRODUÇÃO
# REMOTEOPS SSH Management System
# ===========================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Funções auxiliares
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Mostrar status dos serviços
status() {
    log_info "Status dos serviços:"
    docker-compose -f docker-compose.prod.yml ps
    
    echo ""
    log_info "Uso de recursos:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# Mostrar logs
logs() {
    local service="${1:-}"
    local lines="${2:-100}"
    
    if [ -z "$service" ]; then
        log_info "Mostrando logs de todos os serviços (últimas $lines linhas):"
        docker-compose -f docker-compose.prod.yml logs --tail="$lines" -f
    else
        log_info "Mostrando logs do serviço $service (últimas $lines linhas):"
        docker-compose -f docker-compose.prod.yml logs --tail="$lines" -f "$service"
    fi
}

# Reiniciar serviços
restart() {
    local service="${1:-}"
    
    if [ -z "$service" ]; then
        log_info "Reiniciando todos os serviços..."
        docker-compose -f docker-compose.prod.yml restart
    else
        log_info "Reiniciando serviço $service..."
        docker-compose -f docker-compose.prod.yml restart "$service"
    fi
    
    log_success "Reinicialização concluída"
}

# Parar serviços
stop() {
    local service="${1:-}"
    
    if [ -z "$service" ]; then
        log_warning "Parando todos os serviços..."
        docker-compose -f docker-compose.prod.yml stop
    else
        log_info "Parando serviço $service..."
        docker-compose -f docker-compose.prod.yml stop "$service"
    fi
    
    log_success "Serviços parados"
}

# Iniciar serviços
start() {
    local service="${1:-}"
    
    if [ -z "$service" ]; then
        log_info "Iniciando todos os serviços..."
        docker-compose -f docker-compose.prod.yml start
    else
        log_info "Iniciando serviço $service..."
        docker-compose -f docker-compose.prod.yml start "$service"
    fi
    
    log_success "Serviços iniciados"
}

# Fazer backup
backup() {
    local description="${1:-Manual backup}"
    local backup_dir="./backups/manual-$(date +%Y%m%d_%H%M%S)"
    
    log_info "Criando backup: $description"
    mkdir -p "$backup_dir"
    
    # Backup do banco
    log_info "Fazendo backup do banco de dados..."
    docker-compose -f docker-compose.prod.yml exec -T postgres pg_dumpall -U postgres > "$backup_dir/database.sql"
    
    # Backup do Redis
    log_info "Fazendo backup do Redis..."
    docker-compose -f docker-compose.prod.yml exec -T redis redis-cli BGSAVE
    sleep 2
    docker cp $(docker-compose -f docker-compose.prod.yml ps -q redis):/data/dump.rdb "$backup_dir/redis.rdb" 2>/dev/null || true
    
    # Backup via API (se disponível)
    if curl -f -s http://localhost:3001/health > /dev/null 2>&1; then
        log_info "Fazendo backup via API..."
        curl -X POST http://localhost:3001/api/backup/create \
             -H "Content-Type: application/json" \
             -d "{\"description\": \"$description\"}" \
             -o "$backup_dir/api_backup_response.json" 2>/dev/null || true
    fi
    
    # Criar arquivo de informações
    cat > "$backup_dir/backup_info.txt" << EOF
Backup criado em: $(date)
Descrição: $description
Versão do sistema: $(git rev-parse HEAD 2>/dev/null || echo "unknown")
Serviços ativos: $(docker-compose -f docker-compose.prod.yml ps --services --filter status=running | tr '\n' ' ')
EOF
    
    log_success "Backup criado em: $backup_dir"
}

# Restaurar backup
restore() {
    local backup_path="$1"
    
    if [ -z "$backup_path" ]; then
        log_error "Caminho do backup é obrigatório"
        echo "Uso: $0 restore <caminho_do_backup>"
        exit 1
    fi
    
    if [ ! -d "$backup_path" ]; then
        log_error "Diretório de backup não encontrado: $backup_path"
        exit 1
    fi
    
    log_warning "ATENÇÃO: Esta operação irá substituir os dados atuais!"
    read -p "Tem certeza que deseja continuar? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Operação cancelada"
        exit 0
    fi
    
    # Fazer backup atual antes da restauração
    log_info "Fazendo backup dos dados atuais..."
    backup "Backup antes da restauração de $backup_path"
    
    # Restaurar banco de dados
    if [ -f "$backup_path/database.sql" ]; then
        log_info "Restaurando banco de dados..."
        docker-compose -f docker-compose.prod.yml exec -T postgres psql -U postgres < "$backup_path/database.sql"
    fi
    
    # Restaurar Redis
    if [ -f "$backup_path/redis.rdb" ]; then
        log_info "Restaurando Redis..."
        docker-compose -f docker-compose.prod.yml stop redis
        docker cp "$backup_path/redis.rdb" $(docker-compose -f docker-compose.prod.yml ps -q redis):/data/dump.rdb
        docker-compose -f docker-compose.prod.yml start redis
    fi
    
    log_success "Restauração concluída"
}

# Limpar recursos não utilizados
cleanup() {
    log_info "Limpando recursos não utilizados..."
    
    # Remover containers parados
    docker container prune -f
    
    # Remover imagens não utilizadas
    docker image prune -f
    
    # Remover volumes não utilizados (cuidado!)
    read -p "Remover volumes não utilizados? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker volume prune -f
    fi
    
    # Remover redes não utilizadas
    docker network prune -f
    
    log_success "Limpeza concluída"
}

# Atualizar sistema
update() {
    log_info "Atualizando sistema..."
    
    # Fazer backup antes da atualização
    backup "Backup antes da atualização"
    
    # Atualizar código
    if [ -d ".git" ]; then
        log_info "Atualizando código do repositório..."
        git pull
    fi
    
    # Rebuild das imagens
    log_info "Fazendo rebuild das imagens..."
    docker-compose -f docker-compose.prod.yml build --no-cache
    
    # Reiniciar serviços
    log_info "Reiniciando serviços..."
    docker-compose -f docker-compose.prod.yml down
    docker-compose -f docker-compose.prod.yml up -d
    
    log_success "Atualização concluída"
}

# Monitorar recursos
monitor() {
    log_info "Monitorando recursos do sistema (Ctrl+C para sair)..."
    
    while true; do
        clear
        echo "========================================"
        echo "  MONITORAMENTO - $(date)"
        echo "========================================"
        echo ""
        
        echo "STATUS DOS SERVIÇOS:"
        docker-compose -f docker-compose.prod.yml ps
        echo ""
        
        echo "USO DE RECURSOS:"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
        echo ""
        
        echo "ESPAÇO EM DISCO:"
        df -h | grep -E "(Filesystem|/dev/)"
        echo ""
        
        echo "MEMÓRIA DO SISTEMA:"
        free -h
        echo ""
        
        sleep 5
    done
}

# Verificar saúde dos serviços
health() {
    log_info "Verificando saúde dos serviços..."
    
    local services=("postgres" "redis" "backend" "python-service" "frontend")
    local healthy=0
    local total=${#services[@]}
    
    for service in "${services[@]}"; do
        if docker-compose -f docker-compose.prod.yml ps "$service" | grep -q "healthy\|Up"; then
            log_success "$service: OK"
            ((healthy++))
        else
            log_error "$service: PROBLEMA"
        fi
    done
    
    echo ""
    log_info "Resumo: $healthy/$total serviços saudáveis"
    
    if [ $healthy -eq $total ]; then
        log_success "Todos os serviços estão funcionando corretamente"
        return 0
    else
        log_warning "Alguns serviços apresentam problemas"
        return 1
    fi
}

# Mostrar ajuda
show_help() {
    echo "Uso: $0 <comando> [argumentos]"
    echo ""
    echo "Comandos disponíveis:"
    echo "  status                    Mostra status dos serviços"
    echo "  logs [serviço] [linhas]   Mostra logs (padrão: todos os serviços, 100 linhas)"
    echo "  restart [serviço]         Reinicia serviços (padrão: todos)"
    echo "  stop [serviço]            Para serviços (padrão: todos)"
    echo "  start [serviço]           Inicia serviços (padrão: todos)"
    echo "  backup [descrição]        Cria backup manual"
    echo "  restore <caminho>         Restaura backup"
    echo "  cleanup                   Remove recursos não utilizados"
    echo "  update                    Atualiza sistema"
    echo "  monitor                   Monitora recursos em tempo real"
    echo "  health                    Verifica saúde dos serviços"
    echo "  help                      Mostra esta ajuda"
    echo ""
    echo "Exemplos:"
    echo "  $0 logs backend 50        Mostra últimas 50 linhas do backend"
    echo "  $0 restart postgres       Reinicia apenas o PostgreSQL"
    echo "  $0 backup \"Backup diário\" Cria backup com descrição"
}

# Função principal
main() {
    local command="${1:-help}"
    
    case "$command" in
        status)
            status
            ;;
        logs)
            logs "$2" "$3"
            ;;
        restart)
            restart "$2"
            ;;
        stop)
            stop "$2"
            ;;
        start)
            start "$2"
            ;;
        backup)
            backup "$2"
            ;;
        restore)
            restore "$2"
            ;;
        cleanup)
            cleanup
            ;;
        update)
            update
            ;;
        monitor)
            monitor
            ;;
        health)
            health
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Comando desconhecido: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Executar comando
main "$@"

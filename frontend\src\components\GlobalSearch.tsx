import React, { useState, useRef, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  TextField,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Typography,
  Box,
  Chip,
  InputAdornment,
  CircularProgress,
  Divider,
  IconButton,
  Paper,
  Tabs,
  Tab,
  Badge
} from '@mui/material';
import {
  Search as SearchIcon,
  Close as CloseIcon,
  Computer as ServerIcon,
  Terminal as CommandIcon,
  Description as TemplateIcon,
  Person as UserIcon,
  Group as GroupIcon,
  Web as PageIcon,
  Settings as SettingIcon,
  History as HistoryIcon,
  Clear as ClearIcon,
  TrendingUp as TrendingIcon
} from '@mui/icons-material';
import { useGlobalSearch } from '../hooks/useGlobalSearch';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';

interface GlobalSearchProps {
  open: boolean;
  onClose: () => void;
}

export const GlobalSearch: React.FC<GlobalSearchProps> = ({ open, onClose }) => {
  const {
    query,
    setQuery,
    results,
    categories,
    isSearching,
    selectedCategory,
    setSelectedCategory,
    executeResult,
    clearSearch,
    recentSearches,
    addToRecent,
    clearRecent
  } = useGlobalSearch();

  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  // Focar no input quando abrir
  useEffect(() => {
    if (open && inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [open]);

  // Reset quando fechar
  useEffect(() => {
    if (!open) {
      clearSearch();
      setSelectedIndex(0);
    }
  }, [open, clearSearch]);

  // Atalhos de teclado para navegação
  useKeyboardShortcuts([
    {
      key: 'ArrowDown',
      action: () => {
        if (results.length > 0) {
          setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
        }
      },
      description: 'Próximo resultado'
    },
    {
      key: 'ArrowUp',
      action: () => {
        setSelectedIndex(prev => Math.max(prev - 1, 0));
      },
      description: 'Resultado anterior'
    },
    {
      key: 'Enter',
      action: () => {
        if (results[selectedIndex]) {
          executeResult(results[selectedIndex]);
          onClose();
        }
      },
      description: 'Executar resultado selecionado'
    },
    {
      key: 'Escape',
      action: onClose,
      description: 'Fechar busca'
    }
  ], { enabled: open });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'server': return <ServerIcon />;
      case 'command': return <CommandIcon />;
      case 'template': return <TemplateIcon />;
      case 'user': return <UserIcon />;
      case 'group': return <GroupIcon />;
      case 'page': return <PageIcon />;
      case 'setting': return <SettingIcon />;
      default: return <SearchIcon />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'server': return 'primary';
      case 'command': return 'secondary';
      case 'template': return 'success';
      case 'user': return 'info';
      case 'group': return 'warning';
      case 'page': return 'default';
      case 'setting': return 'error';
      default: return 'default';
    }
  };

  const handleResultClick = (result: any, index: number) => {
    setSelectedIndex(index);
    executeResult(result);
    onClose();
  };

  const handleRecentSearchClick = (recentQuery: string) => {
    setQuery(recentQuery);
    addToRecent(recentQuery);
  };

  const showRecentSearches = !query && recentSearches.length > 0;
  const showResults = query && results.length > 0;
  const showNoResults = query && !isSearching && results.length === 0;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          position: 'fixed',
          top: '10%',
          m: 0,
          maxHeight: '80vh'
        }
      }}
    >
      <DialogContent sx={{ p: 0 }}>
        {/* Campo de busca */}
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <TextField
            ref={inputRef}
            fullWidth
            placeholder="Buscar servidores, comandos, usuários..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  {isSearching ? (
                    <CircularProgress size={20} />
                  ) : (
                    <SearchIcon />
                  )}
                </InputAdornment>
              ),
              endAdornment: query && (
                <InputAdornment position="end">
                  <IconButton size="small" onClick={() => setQuery('')}>
                    <ClearIcon />
                  </IconButton>
                </InputAdornment>
              )
            }}
            variant="outlined"
            size="medium"
          />
        </Box>

        {/* Categorias */}
        {categories.length > 0 && (
          <Box sx={{ px: 2, py: 1, borderBottom: 1, borderColor: 'divider' }}>
            <Tabs
              value={selectedCategory || false}
              onChange={(_, value) => setSelectedCategory(value || null)}
              variant="scrollable"
              scrollButtons="auto"
              size="small"
            >
              <Tab
                label="Todos"
                value={false}
                icon={<Badge badgeContent={results.length} color="primary" />}
              />
              {categories.map(category => (
                <Tab
                  key={category.id}
                  label={category.name}
                  value={category.name}
                  icon={<Badge badgeContent={category.count} color="primary" />}
                />
              ))}
            </Tabs>
          </Box>
        )}

        {/* Resultados */}
        <Box sx={{ maxHeight: '50vh', overflow: 'auto' }} ref={listRef}>
          {showRecentSearches && (
            <Box sx={{ p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Buscas Recentes
                </Typography>
                <IconButton size="small" onClick={clearRecent}>
                  <ClearIcon fontSize="small" />
                </IconButton>
              </Box>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {recentSearches.map((recentQuery, index) => (
                  <Chip
                    key={index}
                    label={recentQuery}
                    size="small"
                    onClick={() => handleRecentSearchClick(recentQuery)}
                    icon={<HistoryIcon />}
                    variant="outlined"
                  />
                ))}
              </Box>
            </Box>
          )}

          {showResults && (
            <List>
              {results.map((result, index) => (
                <ListItem
                  key={result.id}
                  button
                  selected={index === selectedIndex}
                  onClick={() => handleResultClick(result, index)}
                  sx={{
                    '&.Mui-selected': {
                      backgroundColor: 'action.selected'
                    }
                  }}
                >
                  <ListItemIcon>
                    {getTypeIcon(result.type)}
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body1">
                          {result.title}
                        </Typography>
                        <Chip
                          label={result.type}
                          size="small"
                          color={getTypeColor(result.type) as any}
                          variant="outlined"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        {result.subtitle && (
                          <Typography variant="body2" color="text.secondary">
                            {result.subtitle}
                          </Typography>
                        )}
                        {result.description && (
                          <Typography variant="caption" color="text.secondary">
                            {result.description}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <Typography variant="caption" color="text.secondary">
                      {result.category}
                    </Typography>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}

          {showNoResults && (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <SearchIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Nenhum resultado encontrado
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tente usar termos diferentes ou verifique a ortografia
              </Typography>
            </Box>
          )}

          {!query && !showRecentSearches && (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <SearchIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                Busca Global
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Digite para buscar servidores, comandos, usuários e muito mais
              </Typography>
              
              <Box sx={{ mt: 3 }}>
                <Typography variant="caption" color="text.secondary">
                  Dicas: Use Ctrl+K para abrir a busca rapidamente
                </Typography>
              </Box>
            </Box>
          )}
        </Box>

        {/* Rodapé com atalhos */}
        {(showResults || showRecentSearches) && (
          <Box sx={{ p: 1, borderTop: 1, borderColor: 'divider', backgroundColor: 'background.default' }}>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Typography variant="caption" color="text.secondary">
                ↑↓ Navegar
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Enter Selecionar
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Esc Fechar
              </Typography>
            </Box>
          </Box>
        )}
      </DialogContent>
    </Dialog>
  );
};

// Hook para usar a busca global
export const useGlobalSearchDialog = () => {
  const [open, setOpen] = useState(false);

  const openSearch = () => setOpen(true);
  const closeSearch = () => setOpen(false);

  // Atalho global para abrir busca
  useKeyboardShortcuts([
    {
      key: 'k',
      ctrlKey: true,
      action: openSearch,
      description: 'Abrir busca global'
    }
  ]);

  return {
    open,
    openSearch,
    closeSearch,
    SearchDialog: () => <GlobalSearch open={open} onClose={closeSearch} />
  };
};

export default GlobalSearch;

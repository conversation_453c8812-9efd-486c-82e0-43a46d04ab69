import { useState, useEffect, useCallback } from 'react';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: Date;
  read: boolean;
  persistent?: boolean;
  actions?: NotificationAction[];
  data?: any;
}

export interface NotificationAction {
  label: string;
  action: () => void;
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
}

interface UseNotificationsReturn {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => string;
  removeNotification: (id: string) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearAll: () => void;
  clearRead: () => void;
}

const STORAGE_KEY = 'sem-fronteiras-notifications';
const MAX_NOTIFICATIONS = 100;

export const useNotifications = (): UseNotificationsReturn => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Carregar notificações do localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        const notificationsWithDates = parsed.map((notif: any) => ({
          ...notif,
          timestamp: new Date(notif.timestamp)
        }));
        setNotifications(notificationsWithDates);
      }
    } catch (error) {
      console.error('Erro ao carregar notificações:', error);
    }
  }, []);

  // Salvar notificações no localStorage
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(notifications));
    } catch (error) {
      console.error('Erro ao salvar notificações:', error);
    }
  }, [notifications]);

  const addNotification = useCallback((
    notificationData: Omit<Notification, 'id' | 'timestamp' | 'read'>
  ): string => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9);
    
    const newNotification: Notification = {
      ...notificationData,
      id,
      timestamp: new Date(),
      read: false
    };

    setNotifications(prev => {
      const newNotifications = [newNotification, ...prev];
      // Manter apenas as últimas MAX_NOTIFICATIONS
      return newNotifications.slice(0, MAX_NOTIFICATIONS);
    });

    // Auto-remover notificações não persistentes após 5 segundos
    if (!notificationData.persistent) {
      setTimeout(() => {
        removeNotification(id);
      }, 5000);
    }

    return id;
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  }, []);

  const markAsRead = useCallback((id: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  }, []);

  const markAllAsRead = useCallback(() => {
    setNotifications(prev =>
      prev.map(notif => ({ ...notif, read: true }))
    );
  }, []);

  const clearAll = useCallback(() => {
    setNotifications([]);
  }, []);

  const clearRead = useCallback(() => {
    setNotifications(prev => prev.filter(notif => !notif.read));
  }, []);

  const unreadCount = notifications.filter(notif => !notif.read).length;

  return {
    notifications,
    unreadCount,
    addNotification,
    removeNotification,
    markAsRead,
    markAllAsRead,
    clearAll,
    clearRead
  };
};

// Hook para notificações específicas do sistema
export const useSystemNotifications = () => {
  const { addNotification } = useNotifications();

  const notifyCommandExecuted = useCallback((serverName: string, command: string, success: boolean) => {
    addNotification({
      title: success ? 'Comando executado' : 'Erro na execução',
      message: `${command} em ${serverName}`,
      type: success ? 'success' : 'error',
      persistent: !success
    });
  }, [addNotification]);

  const notifyServerConnected = useCallback((serverName: string) => {
    addNotification({
      title: 'Servidor conectado',
      message: `Conexão estabelecida com ${serverName}`,
      type: 'success'
    });
  }, [addNotification]);

  const notifyServerDisconnected = useCallback((serverName: string) => {
    addNotification({
      title: 'Servidor desconectado',
      message: `Conexão perdida com ${serverName}`,
      type: 'warning',
      persistent: true
    });
  }, [addNotification]);

  const notifyAlert = useCallback((alertName: string, severity: 'low' | 'medium' | 'high' | 'critical') => {
    const typeMap = {
      low: 'info' as const,
      medium: 'warning' as const,
      high: 'error' as const,
      critical: 'error' as const
    };

    addNotification({
      title: 'Alerta do sistema',
      message: `${alertName} - Severidade: ${severity}`,
      type: typeMap[severity],
      persistent: severity === 'high' || severity === 'critical'
    });
  }, [addNotification]);

  const notifyBackupCompleted = useCallback((success: boolean) => {
    addNotification({
      title: success ? 'Backup concluído' : 'Erro no backup',
      message: success 
        ? 'Backup do sistema realizado com sucesso'
        : 'Falha ao realizar backup do sistema',
      type: success ? 'success' : 'error',
      persistent: !success
    });
  }, [addNotification]);

  const notifyUserAction = useCallback((action: string, target: string) => {
    addNotification({
      title: 'Ação realizada',
      message: `${action}: ${target}`,
      type: 'info'
    });
  }, [addNotification]);

  return {
    notifyCommandExecuted,
    notifyServerConnected,
    notifyServerDisconnected,
    notifyAlert,
    notifyBackupCompleted,
    notifyUserAction
  };
};

// Hook para notificações toast (temporárias)
export const useToastNotifications = () => {
  const { addNotification } = useNotifications();

  const showSuccess = useCallback((message: string, title?: string) => {
    return addNotification({
      title: title || 'Sucesso',
      message,
      type: 'success'
    });
  }, [addNotification]);

  const showError = useCallback((message: string, title?: string) => {
    return addNotification({
      title: title || 'Erro',
      message,
      type: 'error',
      persistent: true
    });
  }, [addNotification]);

  const showWarning = useCallback((message: string, title?: string) => {
    return addNotification({
      title: title || 'Atenção',
      message,
      type: 'warning'
    });
  }, [addNotification]);

  const showInfo = useCallback((message: string, title?: string) => {
    return addNotification({
      title: title || 'Informação',
      message,
      type: 'info'
    });
  }, [addNotification]);

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo
  };
};

export default useNotifications;

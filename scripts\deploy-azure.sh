#!/bin/bash

# RemoteOps Azure AKS Deployment Script
# Deploys RemoteOps to Azure Kubernetes Service with Azure-specific optimizations

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
RESOURCE_GROUP="remoteops-rg"
CLUSTER_NAME="remoteops-aks"
LOCATION="eastus"
NODE_COUNT=3
VM_SIZE="Standard_D2s_v3"
NAMESPACE="remoteops"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check Azure CLI and prerequisites
check_prerequisites() {
    log_info "Checking Azure prerequisites..."
    
    if ! command -v az &> /dev/null; then
        log_error "Azure CLI is not installed"
        echo "Please install Azure CLI: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
        exit 1
    fi
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    if ! command -v helm &> /dev/null; then
        log_error "Helm is not installed"
        exit 1
    fi
    
    # Check Azure login
    if ! az account show &> /dev/null; then
        log_error "Not logged in to Azure"
        echo "Please run: az login"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Create resource group
create_resource_group() {
    log_info "Creating resource group: $RESOURCE_GROUP"
    
    if az group show --name "$RESOURCE_GROUP" &> /dev/null; then
        log_warning "Resource group $RESOURCE_GROUP already exists"
    else
        az group create --name "$RESOURCE_GROUP" --location "$LOCATION"
        log_success "Resource group created"
    fi
}

# Create AKS cluster
create_cluster() {
    log_info "Creating AKS cluster: $CLUSTER_NAME"
    
    # Check if cluster already exists
    if az aks show --resource-group "$RESOURCE_GROUP" --name "$CLUSTER_NAME" &> /dev/null; then
        log_warning "Cluster $CLUSTER_NAME already exists"
        return
    fi
    
    # Create AKS cluster with advanced features
    az aks create \
        --resource-group "$RESOURCE_GROUP" \
        --name "$CLUSTER_NAME" \
        --location "$LOCATION" \
        --node-count "$NODE_COUNT" \
        --node-vm-size "$VM_SIZE" \
        --kubernetes-version "1.28" \
        --enable-addons monitoring,azure-policy,azure-keyvault-secrets-provider \
        --enable-managed-identity \
        --enable-cluster-autoscaler \
        --min-count 2 \
        --max-count 10 \
        --enable-azure-rbac \
        --enable-oidc-issuer \
        --enable-workload-identity \
        --network-plugin azure \
        --network-policy azure \
        --load-balancer-sku standard \
        --vm-set-type VirtualMachineScaleSets \
        --zones 1 2 3 \
        --node-osdisk-type Managed \
        --node-osdisk-size 50 \
        --max-pods 110 \
        --generate-ssh-keys
    
    log_success "AKS cluster created successfully"
}

# Get AKS credentials
get_credentials() {
    log_info "Getting AKS credentials..."
    
    az aks get-credentials \
        --resource-group "$RESOURCE_GROUP" \
        --name "$CLUSTER_NAME" \
        --overwrite-existing
    
    log_success "Credentials configured"
}

# Install NGINX Ingress Controller
install_nginx_ingress() {
    log_info "Installing NGINX Ingress Controller..."
    
    # Add NGINX Helm repository
    helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
    helm repo update
    
    # Install NGINX Ingress Controller with Azure-specific configuration
    helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx \
        --namespace ingress-nginx \
        --create-namespace \
        --set controller.service.type=LoadBalancer \
        --set controller.service.annotations."service\.beta\.kubernetes\.io/azure-load-balancer-health-probe-request-path"=/healthz \
        --set controller.service.externalTrafficPolicy=Local \
        --set controller.replicaCount=2 \
        --set controller.nodeSelector."kubernetes\.io/os"=linux \
        --set defaultBackend.nodeSelector."kubernetes\.io/os"=linux \
        --set controller.admissionWebhooks.patch.nodeSelector."kubernetes\.io/os"=linux
    
    log_success "NGINX Ingress Controller installed"
}

# Install cert-manager for SSL certificates
install_cert_manager() {
    log_info "Installing cert-manager..."
    
    # Add cert-manager Helm repository
    helm repo add jetstack https://charts.jetstack.io
    helm repo update
    
    # Install cert-manager
    helm upgrade --install cert-manager jetstack/cert-manager \
        --namespace cert-manager \
        --create-namespace \
        --version v1.13.0 \
        --set installCRDs=true \
        --set nodeSelector."kubernetes\.io/os"=linux
    
    # Wait for cert-manager to be ready
    kubectl wait --for=condition=ready pod -l app=cert-manager -n cert-manager --timeout=300s
    
    # Create ClusterIssuer for Let's Encrypt
    cat > /tmp/letsencrypt-issuer.yaml << EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
          podTemplate:
            spec:
              nodeSelector:
                "kubernetes.io/os": linux
EOF

    kubectl apply -f /tmp/letsencrypt-issuer.yaml
    
    log_success "cert-manager installed with Let's Encrypt"
}

# Install Azure Monitor for containers
install_azure_monitor() {
    log_info "Configuring Azure Monitor..."
    
    # Enable Azure Monitor for containers (already enabled during cluster creation)
    # Create Log Analytics workspace if needed
    local workspace_name="remoteops-logs"
    
    if ! az monitor log-analytics workspace show --resource-group "$RESOURCE_GROUP" --workspace-name "$workspace_name" &> /dev/null; then
        az monitor log-analytics workspace create \
            --resource-group "$RESOURCE_GROUP" \
            --workspace-name "$workspace_name" \
            --location "$LOCATION"
    fi
    
    log_success "Azure Monitor configured"
}

# Create Azure-specific values file
create_azure_values() {
    local values_file="$PROJECT_ROOT/kubernetes/helm/remoteops/values-azure.yaml"
    
    log_info "Creating Azure-specific values file..."
    
    cat > "$values_file" << EOF
# Azure AKS specific values
global:
  storageClass: "managed-csi"

app:
  environment: production

# Backend configuration with Azure optimizations
backend:
  replicaCount: 3
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 20
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  
  nodeSelector:
    "kubernetes.io/os": linux
  
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: app.kubernetes.io/component
              operator: In
              values:
              - backend
          topologyKey: topology.kubernetes.io/zone

# Frontend configuration
frontend:
  replicaCount: 2
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
  
  nodeSelector:
    "kubernetes.io/os": linux

# Python service configuration
pythonService:
  replicaCount: 2
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 15
  
  nodeSelector:
    "kubernetes.io/os": linux

# PostgreSQL with Azure Disk
postgresql:
  enabled: true
  primary:
    persistence:
      enabled: true
      storageClass: "managed-csi"
      size: 100Gi
    
    resources:
      limits:
        cpu: 2000m
        memory: 4Gi
      requests:
        cpu: 1000m
        memory: 2Gi
    
    nodeSelector:
      "kubernetes.io/os": linux

# Redis with Azure Disk
redis:
  enabled: true
  master:
    persistence:
      enabled: true
      storageClass: "managed-csi"
      size: 20Gi
    
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi
    
    nodeSelector:
      "kubernetes.io/os": linux

# Ingress with NGINX and cert-manager
ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
  
  hosts:
    - host: remoteops.example.com
      paths:
        - path: /
          pathType: Prefix
          service:
            name: frontend
            port: 80
        - path: /api
          pathType: Prefix
          service:
            name: backend
            port: 3000
        - path: /python-api
          pathType: Prefix
          service:
            name: python-service
            port: 8000
  
  tls:
    - secretName: remoteops-tls
      hosts:
        - remoteops.example.com

# Monitoring with Azure Monitor integration
monitoring:
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true
  
  grafana:
    enabled: true
    adminPassword: "admin"

# Security configurations
security:
  networkPolicy:
    enabled: true
  
  podSecurityPolicy:
    enabled: true

# Pod Disruption Budgets
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Node selectors for Linux nodes
nodeSelector:
  "kubernetes.io/os": linux

# Resource quotas
resourceQuota:
  enabled: true
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
EOF

    log_success "Azure values file created: $values_file"
}

# Deploy to AKS
deploy_to_aks() {
    log_info "Deploying RemoteOps to AKS..."
    
    # Deploy using Kubernetes script with Azure values
    "$SCRIPT_DIR/deploy-kubernetes.sh" deploy azure
    
    log_success "RemoteOps deployed to AKS successfully"
}

# Show Azure-specific information
show_azure_info() {
    log_info "Azure AKS Cluster Information:"
    
    echo -e "\n${BLUE}Cluster Details:${NC}"
    az aks show --resource-group "$RESOURCE_GROUP" --name "$CLUSTER_NAME" --query '{Name:name,Status:provisioningState,Version:kubernetesVersion,Location:location,NodeResourceGroup:nodeResourceGroup}' --output table
    
    echo -e "\n${BLUE}Node Pools:${NC}"
    az aks nodepool list --resource-group "$RESOURCE_GROUP" --cluster-name "$CLUSTER_NAME" --query '[].{Name:name,VmSize:vmSize,Count:count,MinCount:minCount,MaxCount:maxCount,OsType:osType}' --output table
    
    echo -e "\n${BLUE}Load Balancer:${NC}"
    kubectl get service -n ingress-nginx ingress-nginx-controller
    
    echo -e "\n${BLUE}Ingress:${NC}"
    kubectl get ingress -n "$NAMESPACE" -o wide
    
    echo -e "\n${BLUE}Estimated Monthly Cost:${NC}"
    echo "AKS Cluster: Free (managed control plane)"
    echo "3x Standard_D2s_v3 nodes: ~\$210/month"
    echo "Managed Disks (170GB): ~\$20/month"
    echo "Load Balancer: ~\$25/month"
    echo "Log Analytics: ~\$10/month"
    echo "Total estimated: ~\$265/month"
}

# Cleanup Azure resources
cleanup() {
    log_warning "This will delete the entire resource group and all resources!"
    read -p "Are you sure? Type 'DELETE' to confirm: " confirm
    
    if [ "$confirm" = "DELETE" ]; then
        log_info "Deleting resource group..."
        az group delete --name "$RESOURCE_GROUP" --yes --no-wait
        log_success "Resource group deletion initiated"
    else
        log_info "Cleanup cancelled"
    fi
}

# Show help
show_help() {
    echo "RemoteOps Azure AKS Deployment Script"
    echo "====================================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  create-cluster    Create AKS cluster"
    echo "  deploy           Deploy RemoteOps to existing cluster"
    echo "  full-deploy      Create cluster and deploy RemoteOps"
    echo "  status           Show cluster and deployment status"
    echo "  info             Show Azure-specific information"
    echo "  cleanup          Delete resource group"
    echo "  help             Show this help"
    echo ""
    echo "Environment Variables:"
    echo "  RESOURCE_GROUP   Azure resource group (default: remoteops-rg)"
    echo "  CLUSTER_NAME     AKS cluster name (default: remoteops-aks)"
    echo "  LOCATION         Azure region (default: eastus)"
    echo "  VM_SIZE          Node VM size (default: Standard_D2s_v3)"
}

# Main execution
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    RemoteOps Azure AKS Deployment${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    check_prerequisites
    
    case "${1:-full-deploy}" in
        "create-cluster")
            create_resource_group
            create_cluster
            get_credentials
            install_nginx_ingress
            install_cert_manager
            install_azure_monitor
            ;;
        "deploy")
            get_credentials
            create_azure_values
            deploy_to_aks
            ;;
        "full-deploy")
            create_resource_group
            create_cluster
            get_credentials
            install_nginx_ingress
            install_cert_manager
            install_azure_monitor
            create_azure_values
            deploy_to_aks
            show_azure_info
            
            echo ""
            log_success "🎉 RemoteOps deployed successfully to Azure AKS!"
            echo ""
            log_info "Next steps:"
            echo "1. Update DNS records to point to the load balancer IP"
            echo "2. Configure custom domain in the ingress"
            echo "3. Set up Azure Monitor alerts and dashboards"
            echo "4. Configure backup strategies for persistent volumes"
            ;;
        "status")
            "$SCRIPT_DIR/deploy-kubernetes.sh" status
            ;;
        "info")
            show_azure_info
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

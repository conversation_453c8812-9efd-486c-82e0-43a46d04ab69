import { FastifyRequest, FastifyReply } from 'fastify'
import { UserService } from '../services/UserService'
import { CreateUserDTO, UpdateUserDTO } from '../types/user'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()
const userService = new UserService(prisma)

export class UserController {
  async create(request: FastifyRequest<{ Body: CreateUserDTO }>, reply: FastifyReply) {
    try {
      const user = await userService.create(request.body)
      return reply.status(201).send({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        active: user.active,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      })
    } catch (error) {
      if (error instanceof Error) {
        return reply.status(400).send({ error: error.message })
      }
      return reply.status(500).send({ error: 'Erro interno do servidor' })
    }
  }

  async findAll(request: FastifyRequest<{ Querystring: { includeInactive?: string } }>, reply: FastifyReply) {
    try {
      const includeInactive = request.query.includeInactive === 'true'
      const users = await userService.findAll(includeInactive)
      return reply.send(users.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        active: user.active,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      })))
    } catch (error) {
      return reply.status(500).send({ error: 'Erro interno do servidor' })
    }
  }

  async findById(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.params
      const user = await userService.findById(id)

      if (!user) {
        return reply.status(404).send({ error: 'Usuário não encontrado' })
      }

      return reply.send({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        active: user.active,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      })
    } catch (error) {
      return reply.status(500).send({ error: 'Erro interno do servidor' })
    }
  }

  async update(request: FastifyRequest<{ Params: { id: string }, Body: UpdateUserDTO }>, reply: FastifyReply) {
    try {
      const { id } = request.params
      const data = request.body

      console.log('Controller - Atualizando usuário:', id, data)

      // Verifica se o usuário existe
      const existingUser = await userService.findById(id)
      if (!existingUser) {
        return reply.status(404).send({ error: 'Usuário não encontrado' })
      }

      try {
        const updatedUser = await userService.update(id, data)
        
        console.log('Controller - Usuário atualizado com sucesso')
        
        return reply.send({
          id: updatedUser.id,
          name: updatedUser.name,
          email: updatedUser.email,
          role: updatedUser.role,
          active: updatedUser.active,
          createdAt: updatedUser.createdAt,
          updatedAt: updatedUser.updatedAt
        })
      } catch (error) {
        if (error instanceof Error && error.message.includes('último administrador')) {
          return reply.status(400).send({ error: error.message })
        }
        throw error
      }
    } catch (error) {
      console.error('Controller - Erro ao atualizar usuário:', error)
      
      if (error instanceof Error) {
        return reply.status(400).send({ error: error.message })
      }
      
      return reply.status(500).send({ error: 'Erro interno do servidor' })
    }
  }

  async delete(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.params
      
      // Verifica se o usuário existe
      const existingUser = await userService.findById(id)
      if (!existingUser) {
        return reply.status(404).send({ error: 'Usuário não encontrado' })
      }
      
      await userService.delete(id)
      return reply.status(204).send()
    } catch (error) {
      return reply.status(500).send({ error: 'Erro interno do servidor' })
    }
  }

  async deactivate(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.params
      
      // Verifica se o usuário existe
      const existingUser = await userService.findById(id)
      if (!existingUser) {
        return reply.status(404).send({ error: 'Usuário não encontrado' })
      }
      
      try {
        const user = await userService.deactivateUser(id)
        return reply.send({
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          active: user.active,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        })
      } catch (error) {
        if (error instanceof Error && error.message.includes('último administrador')) {
          return reply.status(400).send({ error: error.message })
        }
        throw error
      }
    } catch (error) {
      return reply.status(500).send({ error: 'Erro interno do servidor' })
    }
  }

  async reactivate(request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) {
    try {
      const { id } = request.params
      
      // Verifica se o usuário existe
      const existingUser = await userService.findById(id)
      if (!existingUser) {
        return reply.status(404).send({ error: 'Usuário não encontrado' })
      }
      
      const user = await userService.reactivateUser(id)
      return reply.send({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        active: user.active,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      })
    } catch (error) {
      return reply.status(500).send({ error: 'Erro interno do servidor' })
    }
  }
} 
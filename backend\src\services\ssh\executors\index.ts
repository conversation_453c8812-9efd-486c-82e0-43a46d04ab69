import { NodeSSH } from 'node-ssh';
import { Device } from '../deviceDetector';
import { ICommandExecutor } from './baseExecutor';
import { DmosExecutor } from './dmosExecutor';
import { GenericExecutor } from './genericExecutor';
import { HuaweiExecutor } from './huaweiExecutor';
import { MikrotikExecutor } from './mikrotikExecutor';
import { NokiaExecutor } from './nokiaExecutor';

/**
 * Cria um executor de comandos com base no tipo de dispositivo
 * @param deviceType Tipo de dispositivo
 * @param ssh Instância SSH
 * @returns Executor de comandos
 */
export function makeExecutor(deviceType: Device, ssh: NodeSSH): ICommandExecutor {
  switch (deviceType) {
    case Device.NOKIA:
      return new NokiaExecutor(ssh);
    case Device.HUAWEI:
      return new HuaweiExecutor(ssh);
    case Device.MIKROTIK:
      return new MikrotikExecutor(ssh);
    case Device.DMOS:
      return new DmosExecutor(ssh);
    case Device.GENERIC:
    default:
      return new GenericExecutor(ssh);
  }
}

export * from './baseExecutor';
export * from './nokiaExecutor';
export * from './huaweiExecutor';
export * from './mikrotikExecutor';
export * from './dmosExecutor';
export * from './genericExecutor';

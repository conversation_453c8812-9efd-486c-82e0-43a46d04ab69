import React, { useState } from 'react'
import { 
  Trash2, 
  Refresh<PERSON><PERSON>, 
  Settings, 
  Download, 
  Upload, 
  Zap,
  AlertTriangle,
  Server,
  Search
} from 'lucide-react'
import { CacheConfig } from '../../services/cacheService'

interface CacheControlsProps {
  config: CacheConfig | null
  isLoading: boolean
  onClearCache: () => Promise<void>
  onInvalidateServer: (serverId: string) => Promise<void>
  onInvalidatePattern: (pattern: string) => Promise<void>
  onUpdateConfig: (config: Partial<CacheConfig>) => Promise<void>
  onRefreshStats: () => Promise<void>
  onExportConfig: () => Promise<void>
  onImportConfig: (file: File) => Promise<void>
  onApplyOptimizations: () => Promise<void>
}

const CacheControls: React.FC<CacheControlsProps> = ({
  config,
  isLoading,
  onClearCache,
  onInvalidateServer,
  onInvalidatePattern,
  onUpdateConfig,
  onRefreshStats,
  onExportConfig,
  onImportConfig,
  onApplyOptimizations
}) => {
  const [showConfigModal, setShowConfigModal] = useState(false)
  const [showInvalidateModal, setShowInvalidateModal] = useState(false)
  const [invalidateType, setInvalidateType] = useState<'server' | 'pattern'>('server')
  const [invalidateValue, setInvalidateValue] = useState('')
  const [showClearConfirm, setShowClearConfirm] = useState(false)
  
  // Estado do formulário de configuração
  const [configForm, setConfigForm] = useState<Partial<CacheConfig>>({})

  React.useEffect(() => {
    if (config) {
      setConfigForm(config)
    }
  }, [config])

  const handleClearCache = async () => {
    await onClearCache()
    setShowClearConfirm(false)
  }

  const handleInvalidate = async () => {
    if (!invalidateValue.trim()) return
    
    if (invalidateType === 'server') {
      await onInvalidateServer(invalidateValue)
    } else {
      await onInvalidatePattern(invalidateValue)
    }
    
    setShowInvalidateModal(false)
    setInvalidateValue('')
  }

  const handleConfigSave = async () => {
    await onUpdateConfig(configForm)
    setShowConfigModal(false)
  }

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      onImportConfig(file)
      event.target.value = '' // Reset input
    }
  }

  return (
    <>
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900">Controles do Cache</h3>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
              config?.enabled 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {config?.enabled ? 'Ativo' : 'Inativo'}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Refresh */}
          <button
            onClick={onRefreshStats}
            disabled={isLoading}
            className="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Atualizar
          </button>

          {/* Configurações */}
          <button
            onClick={() => setShowConfigModal(true)}
            className="flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Settings className="h-4 w-4 mr-2" />
            Configurar
          </button>

          {/* Invalidar */}
          <button
            onClick={() => setShowInvalidateModal(true)}
            className="flex items-center justify-center px-4 py-2 border border-yellow-300 rounded-md shadow-sm text-sm font-medium text-yellow-700 bg-yellow-50 hover:bg-yellow-100"
          >
            <Search className="h-4 w-4 mr-2" />
            Invalidar
          </button>

          {/* Limpar Cache */}
          <button
            onClick={() => setShowClearConfirm(true)}
            className="flex items-center justify-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Limpar Tudo
          </button>

          {/* Otimizar */}
          <button
            onClick={onApplyOptimizations}
            disabled={isLoading}
            className="flex items-center justify-center px-4 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 disabled:opacity-50"
          >
            <Zap className="h-4 w-4 mr-2" />
            Otimizar
          </button>

          {/* Exportar */}
          <button
            onClick={onExportConfig}
            className="flex items-center justify-center px-4 py-2 border border-green-300 rounded-md shadow-sm text-sm font-medium text-green-700 bg-green-50 hover:bg-green-100"
          >
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </button>

          {/* Importar */}
          <label className="flex items-center justify-center px-4 py-2 border border-purple-300 rounded-md shadow-sm text-sm font-medium text-purple-700 bg-purple-50 hover:bg-purple-100 cursor-pointer">
            <Upload className="h-4 w-4 mr-2" />
            Importar
            <input
              type="file"
              accept=".json"
              onChange={handleFileImport}
              className="hidden"
            />
          </label>
        </div>
      </div>

      {/* Modal de Configuração */}
      {showConfigModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Configurações do Cache
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  TTL Padrão (segundos)
                </label>
                <input
                  type="number"
                  value={configForm.defaultTTL || ''}
                  onChange={(e) => setConfigForm(prev => ({ 
                    ...prev, 
                    defaultTTL: parseInt(e.target.value) || 0 
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Memória Máxima
                </label>
                <input
                  type="text"
                  value={configForm.maxMemory || ''}
                  onChange={(e) => setConfigForm(prev => ({ 
                    ...prev, 
                    maxMemory: e.target.value 
                  }))}
                  placeholder="100mb"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Política de Eviction
                </label>
                <select
                  value={configForm.evictionPolicy || ''}
                  onChange={(e) => setConfigForm(prev => ({ 
                    ...prev, 
                    evictionPolicy: e.target.value 
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="allkeys-lru">All Keys LRU</option>
                  <option value="allkeys-lfu">All Keys LFU</option>
                  <option value="volatile-lru">Volatile LRU</option>
                  <option value="volatile-lfu">Volatile LFU</option>
                  <option value="allkeys-random">All Keys Random</option>
                  <option value="volatile-random">Volatile Random</option>
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enabled"
                  checked={configForm.enabled || false}
                  onChange={(e) => setConfigForm(prev => ({ 
                    ...prev, 
                    enabled: e.target.checked 
                  }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="enabled" className="ml-2 block text-sm text-gray-900">
                  Cache Ativo
                </label>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowConfigModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancelar
              </button>
              <button
                onClick={handleConfigSave}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                Salvar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Invalidação */}
      {showInvalidateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Invalidar Cache
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo de Invalidação
                </label>
                <div className="flex space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="server"
                      checked={invalidateType === 'server'}
                      onChange={(e) => setInvalidateType(e.target.value as 'server' | 'pattern')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Por Servidor</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="pattern"
                      checked={invalidateType === 'pattern'}
                      onChange={(e) => setInvalidateType(e.target.value as 'server' | 'pattern')}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Por Padrão</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {invalidateType === 'server' ? 'ID do Servidor' : 'Padrão de Comando'}
                </label>
                <input
                  type="text"
                  value={invalidateValue}
                  onChange={(e) => setInvalidateValue(e.target.value)}
                  placeholder={invalidateType === 'server' ? 'server-123' : 'show*'}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowInvalidateModal(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancelar
              </button>
              <button
                onClick={handleInvalidate}
                disabled={!invalidateValue.trim() || isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-yellow-600 rounded-md hover:bg-yellow-700 disabled:opacity-50"
              >
                Invalidar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Confirmação de Limpeza */}
      {showClearConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600 mr-3" />
              <h3 className="text-lg font-medium text-gray-900">
                Confirmar Limpeza
              </h3>
            </div>
            
            <p className="text-sm text-gray-600 mb-6">
              Esta ação irá remover todas as entradas do cache. Esta operação não pode ser desfeita.
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowClearConfirm(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancelar
              </button>
              <button
                onClick={handleClearCache}
                disabled={isLoading}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 disabled:opacity-50"
              >
                Limpar Cache
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default CacheControls

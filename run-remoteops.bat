@echo off
REM RemoteOps Windows Launcher
REM Calls the universal bash script or PowerShell script

setlocal enabledelayedexpansion

echo.
echo ========================================
echo    RemoteOps Windows Launcher
echo ========================================
echo.

REM Check if Git Bash is available (includes bash)
where bash >nul 2>&1
if %errorLevel% equ 0 (
    echo [INFO] Using Git Bash to run universal script...
    bash "%~dp0run-remoteops.sh" %*
    goto :end
)

REM Check if WSL is available
where wsl >nul 2>&1
if %errorLevel% equ 0 (
    echo [INFO] Using WSL to run universal script...
    wsl bash "%~dp0run-remoteops.sh" %*
    goto :end
)

REM Fallback to PowerShell script
echo [INFO] Falling back to PowerShell script...
if exist "%~dp0scripts\run-windows.ps1" (
    powershell -ExecutionPolicy Bypass -File "%~dp0scripts\run-windows.ps1" %*
) else if exist "%~dp0scripts\run-windows.bat" (
    call "%~dp0scripts\run-windows.bat" %*
) else (
    echo [ERROR] No suitable script found for Windows
    echo Please ensure the scripts directory exists with Windows scripts
    pause
    exit /b 1
)

:end
pause

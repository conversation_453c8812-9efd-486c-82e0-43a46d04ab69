import { useState, useEffect, useCallback } from 'react';

export interface TrashItem {
  id: string;
  originalId: string;
  type: 'server' | 'user' | 'template' | 'group' | 'command';
  name: string;
  data: any; // Dados originais do item
  deletedAt: Date;
  deletedBy: string;
  expiresAt: Date;
  reason?: string;
  metadata?: Record<string, any>;
}

export interface TrashFilter {
  types: TrashItem['type'][];
  dateRange: {
    start: Date;
    end: Date;
  };
  searchTerm: string;
  deletedBy?: string;
  showExpired: boolean;
}

export interface TrashStats {
  totalItems: number;
  itemsByType: Record<TrashItem['type'], number>;
  expiredItems: number;
  recentlyDeleted: number; // Últimas 24h
  spaceUsed: number; // Em bytes
}

interface UseTrashReturn {
  items: TrashItem[];
  filteredItems: TrashItem[];
  stats: TrashStats;
  filter: TrashFilter;
  isLoading: boolean;
  updateFilter: (newFilter: Partial<TrashFilter>) => void;
  restoreItem: (itemId: string) => Promise<boolean>;
  restoreMultiple: (itemIds: string[]) => Promise<{ success: string[]; failed: string[] }>;
  permanentlyDelete: (itemId: string) => Promise<boolean>;
  permanentlyDeleteMultiple: (itemIds: string[]) => Promise<{ success: string[]; failed: string[] }>;
  emptyTrash: () => Promise<boolean>;
  cleanupExpired: () => Promise<number>;
  getItemDetails: (itemId: string) => TrashItem | null;
  exportTrash: () => Promise<void>;
}

const DEFAULT_FILTER: TrashFilter = {
  types: ['server', 'user', 'template', 'group', 'command'],
  dateRange: {
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Últimos 30 dias
    end: new Date()
  },
  searchTerm: '',
  showExpired: false
};

const STORAGE_KEY = 'sem-fronteiras-trash';
const RETENTION_DAYS = 30; // Dias para manter itens na lixeira

export const useTrash = (): UseTrashReturn => {
  const [items, setItems] = useState<TrashItem[]>([]);
  const [filter, setFilter] = useState<TrashFilter>(DEFAULT_FILTER);
  const [isLoading, setIsLoading] = useState(false);

  // Carregar itens da lixeira
  useEffect(() => {
    loadTrashItems();
  }, []);

  // Gerar dados mock para demonstração
  const generateMockTrashItems = useCallback((): TrashItem[] => {
    const mockItems: TrashItem[] = [];
    const now = new Date();
    
    const types: TrashItem['type'][] = ['server', 'user', 'template', 'group', 'command'];
    const users = ['admin', 'joao.silva', 'maria.santos', 'carlos.oliveira'];
    
    for (let i = 0; i < 50; i++) {
      const type = types[Math.floor(Math.random() * types.length)];
      const deletedAt = new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000);
      const expiresAt = new Date(deletedAt.getTime() + RETENTION_DAYS * 24 * 60 * 60 * 1000);
      
      let name = '';
      let data = {};
      
      switch (type) {
        case 'server':
          name = `Servidor ${i + 1}`;
          data = {
            name,
            ip: `192.168.1.${Math.floor(Math.random() * 254) + 1}`,
            type: ['MIKROTIK', 'HUAWEI', 'NOKIA'][Math.floor(Math.random() * 3)],
            os: 'LINUX'
          };
          break;
        case 'user':
          name = `usuario${i + 1}@empresa.com`;
          data = {
            name: `Usuário ${i + 1}`,
            email: name,
            role: Math.random() > 0.8 ? 'ADMIN' : 'USER'
          };
          break;
        case 'template':
          name = `Template ${i + 1}`;
          data = {
            name,
            description: `Descrição do template ${i + 1}`,
            commands: [`comando1`, `comando2`, `comando3`]
          };
          break;
        case 'group':
          name = `Grupo ${i + 1}`;
          data = {
            name,
            color: `#${Math.floor(Math.random()*16777215).toString(16)}`,
            description: `Descrição do grupo ${i + 1}`
          };
          break;
        case 'command':
          name = `Comando ${i + 1}`;
          data = {
            command: `show interface ethernet${i + 1}`,
            description: `Comando para verificar interface ${i + 1}`
          };
          break;
      }
      
      mockItems.push({
        id: `trash-${i + 1}`,
        originalId: `original-${i + 1}`,
        type,
        name,
        data,
        deletedAt,
        deletedBy: users[Math.floor(Math.random() * users.length)],
        expiresAt,
        reason: Math.random() > 0.7 ? 'Limpeza de dados antigos' : undefined,
        metadata: {
          size: Math.floor(Math.random() * 10000) + 1000,
          dependencies: Math.floor(Math.random() * 5)
        }
      });
    }
    
    return mockItems.sort((a, b) => b.deletedAt.getTime() - a.deletedAt.getTime());
  }, []);

  const loadTrashItems = useCallback(async () => {
    setIsLoading(true);
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        const itemsWithDates = parsed.map((item: any) => ({
          ...item,
          deletedAt: new Date(item.deletedAt),
          expiresAt: new Date(item.expiresAt)
        }));
        setItems(itemsWithDates);
      } else {
        // Gerar dados mock para demonstração
        const mockItems = generateMockTrashItems();
        setItems(mockItems);
        localStorage.setItem(STORAGE_KEY, JSON.stringify(mockItems));
      }
    } catch (error) {
      console.error('Erro ao carregar lixeira:', error);
    } finally {
      setIsLoading(false);
    }
  }, [generateMockTrashItems]);

  // Salvar no localStorage
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(items));
    } catch (error) {
      console.error('Erro ao salvar lixeira:', error);
    }
  }, [items]);

  // Filtrar itens
  const filteredItems = items.filter(item => {
    // Filtro por tipo
    if (!filter.types.includes(item.type)) return false;
    
    // Filtro por data
    if (item.deletedAt < filter.dateRange.start || item.deletedAt > filter.dateRange.end) return false;
    
    // Filtro por termo de busca
    if (filter.searchTerm) {
      const searchTerm = filter.searchTerm.toLowerCase();
      const searchableText = `${item.name} ${item.deletedBy} ${item.reason || ''}`.toLowerCase();
      if (!searchableText.includes(searchTerm)) return false;
    }
    
    // Filtro por usuário que deletou
    if (filter.deletedBy && item.deletedBy !== filter.deletedBy) return false;
    
    // Filtro por itens expirados
    const isExpired = item.expiresAt < new Date();
    if (!filter.showExpired && isExpired) return false;
    
    return true;
  });

  // Calcular estatísticas
  const stats: TrashStats = {
    totalItems: items.length,
    itemsByType: {
      server: items.filter(i => i.type === 'server').length,
      user: items.filter(i => i.type === 'user').length,
      template: items.filter(i => i.type === 'template').length,
      group: items.filter(i => i.type === 'group').length,
      command: items.filter(i => i.type === 'command').length
    },
    expiredItems: items.filter(i => i.expiresAt < new Date()).length,
    recentlyDeleted: items.filter(i => 
      i.deletedAt > new Date(Date.now() - 24 * 60 * 60 * 1000)
    ).length,
    spaceUsed: items.reduce((total, item) => total + (item.metadata?.size || 0), 0)
  };

  const updateFilter = useCallback((newFilter: Partial<TrashFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter }));
  }, []);

  const restoreItem = useCallback(async (itemId: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // Simular API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const item = items.find(i => i.id === itemId);
      if (!item) return false;
      
      // Remover da lixeira
      setItems(prev => prev.filter(i => i.id !== itemId));
      
      // Em produção, aqui seria feita a restauração real no backend
      console.log('Restaurando item:', item);
      
      return true;
    } catch (error) {
      console.error('Erro ao restaurar item:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [items]);

  const restoreMultiple = useCallback(async (itemIds: string[]): Promise<{ success: string[]; failed: string[] }> => {
    const results = { success: [], failed: [] };
    
    for (const itemId of itemIds) {
      const success = await restoreItem(itemId);
      if (success) {
        results.success.push(itemId);
      } else {
        results.failed.push(itemId);
      }
    }
    
    return results;
  }, [restoreItem]);

  const permanentlyDelete = useCallback(async (itemId: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // Simular API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setItems(prev => prev.filter(i => i.id !== itemId));
      return true;
    } catch (error) {
      console.error('Erro ao deletar permanentemente:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const permanentlyDeleteMultiple = useCallback(async (itemIds: string[]): Promise<{ success: string[]; failed: string[] }> => {
    const results = { success: [], failed: [] };
    
    for (const itemId of itemIds) {
      const success = await permanentlyDelete(itemId);
      if (success) {
        results.success.push(itemId);
      } else {
        results.failed.push(itemId);
      }
    }
    
    return results;
  }, [permanentlyDelete]);

  const emptyTrash = useCallback(async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // Simular API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setItems([]);
      return true;
    } catch (error) {
      console.error('Erro ao esvaziar lixeira:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const cleanupExpired = useCallback(async (): Promise<number> => {
    try {
      setIsLoading(true);
      const now = new Date();
      const expiredItems = items.filter(item => item.expiresAt < now);
      
      // Simular API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setItems(prev => prev.filter(item => item.expiresAt >= now));
      return expiredItems.length;
    } catch (error) {
      console.error('Erro ao limpar itens expirados:', error);
      return 0;
    } finally {
      setIsLoading(false);
    }
  }, [items]);

  const getItemDetails = useCallback((itemId: string): TrashItem | null => {
    return items.find(item => item.id === itemId) || null;
  }, [items]);

  const exportTrash = useCallback(async (): Promise<void> => {
    try {
      const exportData = {
        exportedAt: new Date().toISOString(),
        totalItems: items.length,
        items: items.map(item => ({
          ...item,
          deletedAt: item.deletedAt.toISOString(),
          expiresAt: item.expiresAt.toISOString()
        }))
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `lixeira-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao exportar lixeira:', error);
      throw error;
    }
  }, [items]);

  return {
    items: filteredItems,
    filteredItems,
    stats,
    filter,
    isLoading,
    updateFilter,
    restoreItem,
    restoreMultiple,
    permanentlyDelete,
    permanentlyDeleteMultiple,
    emptyTrash,
    cleanupExpired,
    getItemDetails,
    exportTrash
  };
};

export default useTrash;

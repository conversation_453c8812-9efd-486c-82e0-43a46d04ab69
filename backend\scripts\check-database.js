// Script para verificar a integridade do banco de dados
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function checkDatabase() {
  try {
    console.log('Verificando integridade do banco de dados...')
    
    // Verificar se existem usuários
    const userCount = await prisma.user.count()
    console.log(`Usuários encontrados: ${userCount}`)
    
    if (userCount === 0) {
      console.error('ALERTA: Nenhum usuário encontrado no banco de dados!')
      console.error('Isso pode indicar que o banco de dados foi limpo ou corrompido.')
      console.error('Considere restaurar um backup recente.')
    }
    
    // Verificar se existem servidores
    const serverCount = await prisma.server.count()
    console.log(`Servidores encontrados: ${serverCount}`)
    
    if (serverCount === 0) {
      console.error('ALERTA: Nenhum servidor encontrado no banco de dados!')
      console.error('Isso pode indicar que o banco de dados foi limpo ou corrompido.')
      console.error('Considere restaurar um backup recente.')
    }
    
    // Verificar se existem comandos
    const commandCount = await prisma.command.count()
    console.log(`Comandos encontrados: ${commandCount}`)
    
    if (commandCount === 0) {
      console.error('ALERTA: Nenhum comando encontrado no banco de dados!')
      console.error('Isso pode indicar que os comandos foram apagados.')
      console.error('Considere executar o script de atualização de comandos.')
    }
    
    // Verificar se existem servidores sem comandos
    const serversWithoutCommands = await prisma.server.findMany({
      where: {
        commands: {
          none: {}
        }
      },
      select: {
        id: true,
        name: true
      }
    })
    
    if (serversWithoutCommands.length > 0) {
      console.error('ALERTA: Encontrados servidores sem comandos:')
      serversWithoutCommands.forEach(server => {
        console.error(`- ${server.name} (${server.id})`)
      })
      console.error('Considere executar o script de atualização de comandos para esses servidores.')
    }
    
    console.log('Verificação concluída!')
  } catch (error) {
    console.error('Erro ao verificar banco de dados:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkDatabase()

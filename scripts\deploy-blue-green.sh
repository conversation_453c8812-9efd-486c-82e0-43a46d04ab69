#!/bin/bash

# RemoteOps Blue-Green Deployment Script
# Zero-downtime deployments using blue-green strategy

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
NAMESPACE="remoteops"
RELEASE_NAME="remoteops"
HELM_CHART_PATH="$PROJECT_ROOT/kubernetes/helm/remoteops"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${PURPLE}[DEBUG]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites for blue-green deployment..."
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    if ! command -v helm &> /dev/null; then
        log_error "Helm is not installed"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_error "Namespace $NAMESPACE does not exist"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Get current deployment color
get_current_color() {
    local current_color=$(kubectl get service -n "$NAMESPACE" "$RELEASE_NAME-frontend" -o jsonpath='{.spec.selector.deployment-color}' 2>/dev/null || echo "")
    
    if [ -z "$current_color" ]; then
        # No color label found, check for existing deployment
        if kubectl get deployment -n "$NAMESPACE" "$RELEASE_NAME-backend" &> /dev/null; then
            echo "blue"  # Default to blue for existing deployments
        else
            echo "blue"  # Default to blue for new deployments
        fi
    else
        echo "$current_color"
    fi
}

# Get next deployment color
get_next_color() {
    local current_color=$1
    
    if [ "$current_color" = "blue" ]; then
        echo "green"
    else
        echo "blue"
    fi
}

# Create blue-green values file
create_blue_green_values() {
    local color=$1
    local base_values_file=${2:-"$HELM_CHART_PATH/values.yaml"}
    local bg_values_file="$HELM_CHART_PATH/values-$color.yaml"
    
    log_info "Creating blue-green values file for $color deployment..."
    
    # Copy base values and add blue-green specific configurations
    cp "$base_values_file" "$bg_values_file"
    
    # Add blue-green specific labels and configurations
    cat >> "$bg_values_file" << EOF

# Blue-Green deployment configuration
blueGreen:
  enabled: true
  color: $color

# Override deployment names and labels
backend:
  name: backend-$color
  labels:
    deployment-color: $color
  
frontend:
  name: frontend-$color
  labels:
    deployment-color: $color

pythonService:
  name: python-service-$color
  labels:
    deployment-color: $color

# Service selectors (will be updated during switch)
serviceSelector:
  deploymentColor: $color
EOF

    log_success "Blue-green values file created: $bg_values_file"
}

# Deploy new version (green/blue)
deploy_new_version() {
    local new_color=$1
    local values_file=${2:-"values.yaml"}
    
    log_info "Deploying new version ($new_color)..."
    
    # Create blue-green specific values
    create_blue_green_values "$new_color" "$HELM_CHART_PATH/$values_file"
    
    # Deploy new version with color suffix
    cd "$HELM_CHART_PATH"
    
    helm upgrade --install "$RELEASE_NAME-$new_color" . \
        --namespace "$NAMESPACE" \
        --values "values-$new_color.yaml" \
        --set nameOverride="$RELEASE_NAME-$new_color" \
        --set fullnameOverride="$RELEASE_NAME-$new_color" \
        --wait \
        --timeout 10m
    
    log_success "New version ($new_color) deployed successfully"
}

# Health check for new deployment
health_check() {
    local color=$1
    local max_attempts=30
    local attempt=1
    
    log_info "Performing health checks for $color deployment..."
    
    while [ $attempt -le $max_attempts ]; do
        log_debug "Health check attempt $attempt/$max_attempts..."
        
        # Check if all pods are ready
        local ready_pods=$(kubectl get pods -n "$NAMESPACE" -l "app.kubernetes.io/instance=$RELEASE_NAME-$color" -o jsonpath='{.items[*].status.conditions[?(@.type=="Ready")].status}' | grep -o "True" | wc -l)
        local total_pods=$(kubectl get pods -n "$NAMESPACE" -l "app.kubernetes.io/instance=$RELEASE_NAME-$color" --no-headers | wc -l)
        
        if [ "$ready_pods" -eq "$total_pods" ] && [ "$total_pods" -gt 0 ]; then
            # Check application health endpoints
            local backend_pod=$(kubectl get pods -n "$NAMESPACE" -l "app.kubernetes.io/instance=$RELEASE_NAME-$color,app.kubernetes.io/component=backend" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
            
            if [ -n "$backend_pod" ]; then
                if kubectl exec -n "$NAMESPACE" "$backend_pod" -- curl -f http://localhost:3000/api/health &> /dev/null; then
                    log_success "Health check passed for $color deployment"
                    return 0
                fi
            fi
        fi
        
        sleep 10
        ((attempt++))
    done
    
    log_error "Health check failed for $color deployment after $max_attempts attempts"
    return 1
}

# Run smoke tests
run_smoke_tests() {
    local color=$1
    
    log_info "Running smoke tests for $color deployment..."
    
    # Get service endpoints
    local backend_service="$RELEASE_NAME-$color-backend"
    local frontend_service="$RELEASE_NAME-$color-frontend"
    
    # Port forward to test endpoints
    kubectl port-forward -n "$NAMESPACE" "service/$backend_service" 8080:3000 &
    local backend_pf_pid=$!
    
    kubectl port-forward -n "$NAMESPACE" "service/$frontend_service" 8081:80 &
    local frontend_pf_pid=$!
    
    sleep 5
    
    local tests_passed=true
    
    # Test backend health endpoint
    if ! curl -f http://localhost:8080/api/health &> /dev/null; then
        log_error "Backend health check failed"
        tests_passed=false
    fi
    
    # Test backend version endpoint
    if ! curl -f http://localhost:8080/api/health/version &> /dev/null; then
        log_error "Backend version endpoint failed"
        tests_passed=false
    fi
    
    # Test frontend
    if ! curl -f http://localhost:8081 &> /dev/null; then
        log_error "Frontend health check failed"
        tests_passed=false
    fi
    
    # Cleanup port forwards
    kill $backend_pf_pid $frontend_pf_pid 2>/dev/null || true
    
    if [ "$tests_passed" = true ]; then
        log_success "Smoke tests passed for $color deployment"
        return 0
    else
        log_error "Smoke tests failed for $color deployment"
        return 1
    fi
}

# Switch traffic to new deployment
switch_traffic() {
    local new_color=$1
    local old_color=$2
    
    log_info "Switching traffic from $old_color to $new_color..."
    
    # Update service selectors to point to new deployment
    local services=("$RELEASE_NAME-frontend" "$RELEASE_NAME-backend" "$RELEASE_NAME-python-service")
    
    for service in "${services[@]}"; do
        if kubectl get service -n "$NAMESPACE" "$service" &> /dev/null; then
            log_debug "Updating service $service to point to $new_color deployment..."
            
            kubectl patch service -n "$NAMESPACE" "$service" -p "{\"spec\":{\"selector\":{\"deployment-color\":\"$new_color\"}}}"
        fi
    done
    
    # Update ingress if it exists
    if kubectl get ingress -n "$NAMESPACE" "$RELEASE_NAME" &> /dev/null; then
        log_debug "Updating ingress to point to $new_color services..."
        
        # This would require updating the ingress backend services
        # Implementation depends on specific ingress configuration
    fi
    
    log_success "Traffic switched to $new_color deployment"
}

# Cleanup old deployment
cleanup_old_deployment() {
    local old_color=$1
    
    log_info "Cleaning up old deployment ($old_color)..."
    
    # Wait a bit to ensure traffic has switched
    log_info "Waiting 30 seconds before cleanup..."
    sleep 30
    
    # Remove old Helm release
    if helm list -n "$NAMESPACE" | grep -q "$RELEASE_NAME-$old_color"; then
        helm uninstall "$RELEASE_NAME-$old_color" -n "$NAMESPACE"
        log_success "Old deployment ($old_color) cleaned up"
    else
        log_warning "Old deployment ($old_color) not found"
    fi
}

# Rollback to previous deployment
rollback() {
    local current_color=$(get_current_color)
    local previous_color=$(get_next_color "$current_color")
    
    log_warning "Rolling back from $current_color to $previous_color..."
    
    # Check if previous deployment exists
    if ! helm list -n "$NAMESPACE" | grep -q "$RELEASE_NAME-$previous_color"; then
        log_error "Previous deployment ($previous_color) not found. Cannot rollback."
        exit 1
    fi
    
    # Switch traffic back
    switch_traffic "$previous_color" "$current_color"
    
    # Cleanup current (failed) deployment
    cleanup_old_deployment "$current_color"
    
    log_success "Rollback completed successfully"
}

# Show deployment status
show_status() {
    log_info "Blue-Green Deployment Status:"
    
    echo -e "\n${CYAN}Current Active Color:${NC}"
    local current_color=$(get_current_color)
    echo "Active: $current_color"
    
    echo -e "\n${CYAN}Helm Releases:${NC}"
    helm list -n "$NAMESPACE" | grep "$RELEASE_NAME"
    
    echo -e "\n${CYAN}Deployments:${NC}"
    kubectl get deployments -n "$NAMESPACE" -l "app.kubernetes.io/name=$RELEASE_NAME" -o wide
    
    echo -e "\n${CYAN}Services:${NC}"
    kubectl get services -n "$NAMESPACE" -l "app.kubernetes.io/name=$RELEASE_NAME" -o wide
    
    echo -e "\n${CYAN}Pods by Color:${NC}"
    for color in blue green; do
        local pod_count=$(kubectl get pods -n "$NAMESPACE" -l "deployment-color=$color" --no-headers 2>/dev/null | wc -l)
        if [ "$pod_count" -gt 0 ]; then
            echo -e "$color: ${GREEN}$pod_count pods${NC}"
            kubectl get pods -n "$NAMESPACE" -l "deployment-color=$color" -o wide
        else
            echo -e "$color: ${YELLOW}No pods${NC}"
        fi
    done
}

# Full blue-green deployment
full_deployment() {
    local values_file=${1:-"values.yaml"}
    
    log_info "Starting blue-green deployment..."
    
    # Get current and next colors
    local current_color=$(get_current_color)
    local new_color=$(get_next_color "$current_color")
    
    log_info "Current deployment: $current_color"
    log_info "New deployment: $new_color"
    
    # Deploy new version
    deploy_new_version "$new_color" "$values_file"
    
    # Health checks
    if ! health_check "$new_color"; then
        log_error "Health checks failed. Cleaning up failed deployment..."
        cleanup_old_deployment "$new_color"
        exit 1
    fi
    
    # Smoke tests
    if ! run_smoke_tests "$new_color"; then
        log_error "Smoke tests failed. Cleaning up failed deployment..."
        cleanup_old_deployment "$new_color"
        exit 1
    fi
    
    # Switch traffic
    switch_traffic "$new_color" "$current_color"
    
    # Final verification
    log_info "Performing final verification..."
    sleep 10
    
    if health_check "$new_color"; then
        log_success "Deployment successful! Traffic switched to $new_color"
        
        # Cleanup old deployment
        cleanup_old_deployment "$current_color"
        
        log_success "🎉 Blue-green deployment completed successfully!"
    else
        log_error "Final verification failed. Rolling back..."
        rollback
        exit 1
    fi
}

# Show help
show_help() {
    echo "RemoteOps Blue-Green Deployment Script"
    echo "======================================"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  deploy [values-file]    Perform full blue-green deployment"
    echo "  status                  Show current deployment status"
    echo "  switch [color]          Manually switch traffic to color"
    echo "  rollback               Rollback to previous deployment"
    echo "  cleanup [color]        Cleanup specific color deployment"
    echo "  help                   Show this help"
    echo ""
    echo "Examples:"
    echo "  $0 deploy                    # Deploy with default values"
    echo "  $0 deploy values-prod.yaml   # Deploy with specific values file"
    echo "  $0 status                    # Show current status"
    echo "  $0 rollback                  # Rollback to previous version"
    echo ""
    echo "Blue-Green Strategy:"
    echo "  - Deploys new version alongside current version"
    echo "  - Performs health checks and smoke tests"
    echo "  - Switches traffic atomically"
    echo "  - Cleans up old version after successful switch"
    echo "  - Supports instant rollback if issues occur"
}

# Main execution
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    RemoteOps Blue-Green Deployment${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    check_prerequisites
    
    case "${1:-deploy}" in
        "deploy")
            full_deployment "$2"
            ;;
        "status")
            show_status
            ;;
        "switch")
            local target_color=$2
            if [ -z "$target_color" ]; then
                log_error "Please specify target color (blue or green)"
                exit 1
            fi
            local current_color=$(get_current_color)
            switch_traffic "$target_color" "$current_color"
            ;;
        "rollback")
            rollback
            ;;
        "cleanup")
            local color=$2
            if [ -z "$color" ]; then
                log_error "Please specify color to cleanup (blue or green)"
                exit 1
            fi
            cleanup_old_deployment "$color"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

import { useState, useEffect, useCallback } from 'react';

export interface DashboardWidget {
  id: string;
  type: 'servers' | 'commands' | 'alerts' | 'metrics' | 'activity' | 'status' | 'chart' | 'custom';
  title: string;
  position: { x: number; y: number };
  size: { width: number; height: number };
  config: Record<string, any>;
  visible: boolean;
  refreshInterval?: number;
  lastUpdated?: Date;
}

export interface DashboardLayout {
  id: string;
  name: string;
  description?: string;
  widgets: DashboardWidget[];
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface UseDashboardReturn {
  layouts: DashboardLayout[];
  currentLayout: DashboardLayout | null;
  widgets: DashboardWidget[];
  isEditing: boolean;
  setEditing: (editing: boolean) => void;
  createLayout: (name: string, description?: string) => string;
  deleteLayout: (layoutId: string) => void;
  switchLayout: (layoutId: string) => void;
  updateLayout: (layoutId: string, updates: Partial<DashboardLayout>) => void;
  addWidget: (widget: Omit<DashboardWidget, 'id'>) => string;
  updateWidget: (widgetId: string, updates: Partial<DashboardWidget>) => void;
  removeWidget: (widgetId: string) => void;
  moveWidget: (widgetId: string, position: { x: number; y: number }) => void;
  resizeWidget: (widgetId: string, size: { width: number; height: number }) => void;
  resetLayout: () => void;
  exportLayout: (layoutId: string) => string;
  importLayout: (data: string) => boolean;
}

const STORAGE_KEY = 'sem-fronteiras-dashboard-layouts';
const CURRENT_LAYOUT_KEY = 'sem-fronteiras-current-layout';

const DEFAULT_WIDGETS: Omit<DashboardWidget, 'id'>[] = [
  {
    type: 'status',
    title: 'Status do Sistema',
    position: { x: 0, y: 0 },
    size: { width: 4, height: 2 },
    config: { showDetails: true },
    visible: true,
    refreshInterval: 30000
  },
  {
    type: 'servers',
    title: 'Servidores Online',
    position: { x: 4, y: 0 },
    size: { width: 4, height: 2 },
    config: { showOffline: false, maxItems: 5 },
    visible: true,
    refreshInterval: 60000
  },
  {
    type: 'alerts',
    title: 'Alertas Ativos',
    position: { x: 8, y: 0 },
    size: { width: 4, height: 2 },
    config: { severity: 'all', maxItems: 5 },
    visible: true,
    refreshInterval: 15000
  },
  {
    type: 'activity',
    title: 'Atividade Recente',
    position: { x: 0, y: 2 },
    size: { width: 6, height: 3 },
    config: { maxItems: 10, showDetails: true },
    visible: true,
    refreshInterval: 30000
  },
  {
    type: 'metrics',
    title: 'Métricas de Performance',
    position: { x: 6, y: 2 },
    size: { width: 6, height: 3 },
    config: { timeRange: '1h', metrics: ['cpu', 'memory', 'network'] },
    visible: true,
    refreshInterval: 60000
  }
];

export const useDashboard = (): UseDashboardReturn => {
  const [layouts, setLayouts] = useState<DashboardLayout[]>([]);
  const [currentLayoutId, setCurrentLayoutId] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Carregar layouts do localStorage
  useEffect(() => {
    try {
      const storedLayouts = localStorage.getItem(STORAGE_KEY);
      const storedCurrentId = localStorage.getItem(CURRENT_LAYOUT_KEY);
      
      if (storedLayouts) {
        const parsed = JSON.parse(storedLayouts);
        const layoutsWithDates = parsed.map((layout: any) => ({
          ...layout,
          createdAt: new Date(layout.createdAt),
          updatedAt: new Date(layout.updatedAt),
          widgets: layout.widgets.map((widget: any) => ({
            ...widget,
            lastUpdated: widget.lastUpdated ? new Date(widget.lastUpdated) : undefined
          }))
        }));
        setLayouts(layoutsWithDates);
        
        if (storedCurrentId && layoutsWithDates.find((l: any) => l.id === storedCurrentId)) {
          setCurrentLayoutId(storedCurrentId);
        } else if (layoutsWithDates.length > 0) {
          setCurrentLayoutId(layoutsWithDates[0].id);
        }
      } else {
        // Criar layout padrão
        const defaultLayout = createDefaultLayout();
        setLayouts([defaultLayout]);
        setCurrentLayoutId(defaultLayout.id);
      }
    } catch (error) {
      console.error('Erro ao carregar layouts do dashboard:', error);
      const defaultLayout = createDefaultLayout();
      setLayouts([defaultLayout]);
      setCurrentLayoutId(defaultLayout.id);
    }
  }, []);

  // Salvar layouts no localStorage
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(layouts));
    } catch (error) {
      console.error('Erro ao salvar layouts:', error);
    }
  }, [layouts]);

  // Salvar layout atual
  useEffect(() => {
    if (currentLayoutId) {
      localStorage.setItem(CURRENT_LAYOUT_KEY, currentLayoutId);
    }
  }, [currentLayoutId]);

  const createDefaultLayout = (): DashboardLayout => {
    const now = new Date();
    return {
      id: 'default',
      name: 'Dashboard Principal',
      description: 'Layout padrão do dashboard',
      widgets: DEFAULT_WIDGETS.map((widget, index) => ({
        ...widget,
        id: `widget-${index}`,
        lastUpdated: now
      })),
      isDefault: true,
      createdAt: now,
      updatedAt: now
    };
  };

  const currentLayout = layouts.find(layout => layout.id === currentLayoutId) || null;
  const widgets = currentLayout?.widgets || [];

  const createLayout = useCallback((name: string, description?: string): string => {
    const id = Date.now().toString();
    const now = new Date();
    
    const newLayout: DashboardLayout = {
      id,
      name,
      description,
      widgets: [],
      isDefault: false,
      createdAt: now,
      updatedAt: now
    };
    
    setLayouts(prev => [...prev, newLayout]);
    return id;
  }, []);

  const deleteLayout = useCallback((layoutId: string) => {
    setLayouts(prev => {
      const filtered = prev.filter(layout => layout.id !== layoutId);
      
      // Se deletou o layout atual, mudar para o primeiro disponível
      if (layoutId === currentLayoutId) {
        const newCurrent = filtered.length > 0 ? filtered[0].id : null;
        setCurrentLayoutId(newCurrent);
      }
      
      return filtered;
    });
  }, [currentLayoutId]);

  const switchLayout = useCallback((layoutId: string) => {
    setCurrentLayoutId(layoutId);
    setIsEditing(false);
  }, []);

  const updateLayout = useCallback((layoutId: string, updates: Partial<DashboardLayout>) => {
    setLayouts(prev => prev.map(layout => 
      layout.id === layoutId 
        ? { ...layout, ...updates, updatedAt: new Date() }
        : layout
    ));
  }, []);

  const addWidget = useCallback((widget: Omit<DashboardWidget, 'id'>): string => {
    if (!currentLayoutId) return '';
    
    const id = `widget-${Date.now()}`;
    const newWidget: DashboardWidget = {
      ...widget,
      id,
      lastUpdated: new Date()
    };
    
    setLayouts(prev => prev.map(layout => 
      layout.id === currentLayoutId
        ? {
            ...layout,
            widgets: [...layout.widgets, newWidget],
            updatedAt: new Date()
          }
        : layout
    ));
    
    return id;
  }, [currentLayoutId]);

  const updateWidget = useCallback((widgetId: string, updates: Partial<DashboardWidget>) => {
    if (!currentLayoutId) return;
    
    setLayouts(prev => prev.map(layout => 
      layout.id === currentLayoutId
        ? {
            ...layout,
            widgets: layout.widgets.map(widget =>
              widget.id === widgetId
                ? { ...widget, ...updates, lastUpdated: new Date() }
                : widget
            ),
            updatedAt: new Date()
          }
        : layout
    ));
  }, [currentLayoutId]);

  const removeWidget = useCallback((widgetId: string) => {
    if (!currentLayoutId) return;
    
    setLayouts(prev => prev.map(layout => 
      layout.id === currentLayoutId
        ? {
            ...layout,
            widgets: layout.widgets.filter(widget => widget.id !== widgetId),
            updatedAt: new Date()
          }
        : layout
    ));
  }, [currentLayoutId]);

  const moveWidget = useCallback((widgetId: string, position: { x: number; y: number }) => {
    updateWidget(widgetId, { position });
  }, [updateWidget]);

  const resizeWidget = useCallback((widgetId: string, size: { width: number; height: number }) => {
    updateWidget(widgetId, { size });
  }, [updateWidget]);

  const resetLayout = useCallback(() => {
    if (!currentLayoutId) return;
    
    const defaultWidgets = DEFAULT_WIDGETS.map((widget, index) => ({
      ...widget,
      id: `widget-${Date.now()}-${index}`,
      lastUpdated: new Date()
    }));
    
    updateLayout(currentLayoutId, { widgets: defaultWidgets });
  }, [currentLayoutId, updateLayout]);

  const exportLayout = useCallback((layoutId: string): string => {
    const layout = layouts.find(l => l.id === layoutId);
    if (!layout) return '';
    
    return JSON.stringify(layout, null, 2);
  }, [layouts]);

  const importLayout = useCallback((data: string): boolean => {
    try {
      const imported = JSON.parse(data);
      if (imported && imported.widgets && Array.isArray(imported.widgets)) {
        const newLayout: DashboardLayout = {
          ...imported,
          id: Date.now().toString(),
          createdAt: new Date(),
          updatedAt: new Date(),
          isDefault: false
        };
        
        setLayouts(prev => [...prev, newLayout]);
        return true;
      }
    } catch (error) {
      console.error('Erro ao importar layout:', error);
    }
    return false;
  }, []);

  const setEditing = useCallback((editing: boolean) => {
    setIsEditing(editing);
  }, []);

  return {
    layouts,
    currentLayout,
    widgets,
    isEditing,
    setEditing,
    createLayout,
    deleteLayout,
    switchLayout,
    updateLayout,
    addWidget,
    updateWidget,
    removeWidget,
    moveWidget,
    resizeWidget,
    resetLayout,
    exportLayout,
    importLayout
  };
};

export default useDashboard;

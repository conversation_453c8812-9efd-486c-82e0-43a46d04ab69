import { FastifyInstance } from 'fastify'
import { verifyJWT } from '../middlewares/auth'
import {
  listServerGroups,
  getServerGroup,
  createServerGroup,
  updateServerGroup,
  deleteServerGroup,
  addServerToGroup,
  removeServerFromGroup,
} from '../controllers/serverGroups'

export async function serverGroupRoutes(app: FastifyInstance) {
  // Middleware de autenticação para todas as rotas
  app.addHook('onRequest', verifyJWT)

  // Listar grupos do usuário
  app.get('/', listServerGroups)

  // Obter grupo específico
  app.get('/:id', getServerGroup)

  // Criar grupo
  app.post('/', createServerGroup)

  // Atualizar grupo
  app.put('/:id', updateServerGroup)

  // Excluir grupo
  app.delete('/:id', deleteServerGroup)

  // Adicionar servidor ao grupo
  app.post('/:id/servers', addServerToGroup)

  // Remover servidor do grupo
  app.delete('/:id/servers/:serverId', removeServerFromGroup)
}

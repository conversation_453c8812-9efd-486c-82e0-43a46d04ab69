import { useState, useEffect } from 'react';

interface FavoriteCommand {
  id: string;
  command: string;
  description?: string;
  deviceType?: string;
  createdAt: Date;
  usageCount: number;
}

interface UseFavoriteCommandsReturn {
  favorites: FavoriteCommand[];
  addFavorite: (command: string, description?: string, deviceType?: string) => void;
  removeFavorite: (commandOrId: string) => void;
  updateFavorite: (id: string, updates: Partial<FavoriteCommand>) => void;
  getFavoritesByDevice: (deviceType: string) => FavoriteCommand[];
  incrementUsage: (commandOrId: string) => void;
  searchFavorites: (query: string) => FavoriteCommand[];
  exportFavorites: () => string;
  importFavorites: (data: string) => boolean;
  clearFavorites: () => void;
}

const STORAGE_KEY = 'sem-fronteiras-favorite-commands';

export const useFavoriteCommands = (): UseFavoriteCommandsReturn => {
  const [favorites, setFavorites] = useState<FavoriteCommand[]>([]);

  // Carregar favoritos do localStorage na inicialização
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Converter strings de data de volta para objetos Date
        const favoritesWithDates = parsed.map((fav: any) => ({
          ...fav,
          createdAt: new Date(fav.createdAt)
        }));
        setFavorites(favoritesWithDates);
      }
    } catch (error) {
      console.error('Erro ao carregar favoritos:', error);
    }
  }, []);

  // Salvar favoritos no localStorage sempre que mudarem
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(favorites));
    } catch (error) {
      console.error('Erro ao salvar favoritos:', error);
    }
  }, [favorites]);

  const addFavorite = (command: string, description?: string, deviceType?: string) => {
    const trimmedCommand = command.trim();
    if (!trimmedCommand) return;

    // Verificar se já existe
    const exists = favorites.some(fav => fav.command === trimmedCommand);
    if (exists) return;

    const newFavorite: FavoriteCommand = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      command: trimmedCommand,
      description,
      deviceType,
      createdAt: new Date(),
      usageCount: 0
    };

    setFavorites(prev => [...prev, newFavorite]);
  };

  const removeFavorite = (commandOrId: string) => {
    setFavorites(prev => 
      prev.filter(fav => fav.id !== commandOrId && fav.command !== commandOrId)
    );
  };

  const updateFavorite = (id: string, updates: Partial<FavoriteCommand>) => {
    setFavorites(prev =>
      prev.map(fav =>
        fav.id === id ? { ...fav, ...updates } : fav
      )
    );
  };

  const getFavoritesByDevice = (deviceType: string) => {
    return favorites.filter(fav => 
      !fav.deviceType || fav.deviceType === deviceType
    ).sort((a, b) => b.usageCount - a.usageCount);
  };

  const incrementUsage = (commandOrId: string) => {
    setFavorites(prev =>
      prev.map(fav => {
        if (fav.id === commandOrId || fav.command === commandOrId) {
          return { ...fav, usageCount: fav.usageCount + 1 };
        }
        return fav;
      })
    );
  };

  const searchFavorites = (query: string) => {
    const lowerQuery = query.toLowerCase();
    return favorites.filter(fav =>
      fav.command.toLowerCase().includes(lowerQuery) ||
      (fav.description && fav.description.toLowerCase().includes(lowerQuery))
    ).sort((a, b) => b.usageCount - a.usageCount);
  };

  const exportFavorites = () => {
    return JSON.stringify(favorites, null, 2);
  };

  const importFavorites = (data: string) => {
    try {
      const imported = JSON.parse(data);
      if (Array.isArray(imported)) {
        const validFavorites = imported.filter(item =>
          item.id && item.command && item.createdAt
        ).map(item => ({
          ...item,
          createdAt: new Date(item.createdAt),
          usageCount: item.usageCount || 0
        }));
        
        setFavorites(validFavorites);
        return true;
      }
    } catch (error) {
      console.error('Erro ao importar favoritos:', error);
    }
    return false;
  };

  const clearFavorites = () => {
    setFavorites([]);
  };

  return {
    favorites,
    addFavorite,
    removeFavorite,
    updateFavorite,
    getFavoritesByDevice,
    incrementUsage,
    searchFavorites,
    exportFavorites,
    importFavorites,
    clearFavorites
  };
};

// Hook para obter comandos mais usados
export const usePopularCommands = (limit: number = 10) => {
  const { favorites } = useFavoriteCommands();
  
  const popularCommands = favorites
    .filter(fav => fav.usageCount > 0)
    .sort((a, b) => b.usageCount - a.usageCount)
    .slice(0, limit);

  return popularCommands;
};

// Hook para obter comandos recentes
export const useRecentCommands = (limit: number = 10) => {
  const { favorites } = useFavoriteCommands();
  
  const recentCommands = favorites
    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
    .slice(0, limit);

  return recentCommands;
};

export default useFavoriteCommands;

import { api } from '../lib/api'

export interface MarketplaceTemplate {
  id: string
  name: string
  description?: string
  category?: string
  tags: string[]
  version: string
  downloads: number
  likes: number
  isMarketplace: boolean
  isFeatured: boolean
  deviceTypes: string[]
  createdAt: string
  updatedAt: string
  user: {
    id: string
    name: string
    email: string
  }
  commands: Array<{
    id: string
    name: string
    command: string
    description?: string
    order: number
  }>
  reviews?: Array<{
    id: string
    rating: number
    comment?: string
    createdAt: string
    user: {
      id: string
      name: string
    }
  }>
  stats: {
    totalDownloads: number
    totalLikes: number
    totalReviews: number
    averageRating: number
    recentDownloads: number
  }
  userInteraction?: {
    hasLiked: boolean
    hasFavorited: boolean
    hasReviewed: boolean
  }
}

export interface MarketplaceFilters {
  category?: string
  tags?: string[]
  deviceTypes?: string[]
  search?: string
  sortBy?: 'popular' | 'recent' | 'rating' | 'downloads'
  page?: number
  limit?: number
}

export interface MarketplaceSearchResult {
  templates: MarketplaceTemplate[]
  total: number
  page: number
  totalPages: number
}

/**
 * Serviço para interação com o marketplace de templates
 */
export class MarketplaceService {
  
  /**
   * Busca templates no marketplace
   */
  static async searchTemplates(filters: MarketplaceFilters): Promise<MarketplaceSearchResult> {
    const params = new URLSearchParams()
    
    if (filters.category) params.append('category', filters.category)
    if (filters.tags?.length) params.append('tags', filters.tags.join(','))
    if (filters.deviceTypes?.length) params.append('deviceTypes', filters.deviceTypes.join(','))
    if (filters.search) params.append('search', filters.search)
    if (filters.sortBy) params.append('sortBy', filters.sortBy)
    if (filters.page) params.append('page', filters.page.toString())
    if (filters.limit) params.append('limit', filters.limit.toString())

    const response = await api.get(`/marketplace/templates?${params.toString()}`)
    return response.data.data
  }

  /**
   * Obtém detalhes de um template específico
   */
  static async getTemplate(templateId: string): Promise<MarketplaceTemplate> {
    const response = await api.get(`/marketplace/templates/${templateId}`)
    return response.data.data
  }

  /**
   * Publica um template no marketplace
   */
  static async publishTemplate(templateId: string, marketplaceData: {
    category: string
    tags: string[]
    deviceTypes: string[]
  }): Promise<void> {
    await api.post(`/marketplace/templates/${templateId}/publish`, marketplaceData)
  }

  /**
   * Remove template do marketplace
   */
  static async unpublishTemplate(templateId: string): Promise<void> {
    await api.post(`/marketplace/templates/${templateId}/unpublish`)
  }

  /**
   * Faz download de um template
   */
  static async downloadTemplate(templateId: string): Promise<any> {
    const response = await api.post(`/marketplace/templates/${templateId}/download`)
    return response.data.data
  }

  /**
   * Curte/descurte um template
   */
  static async toggleLike(templateId: string): Promise<{ liked: boolean }> {
    const response = await api.post(`/marketplace/templates/${templateId}/like`)
    return response.data.data
  }

  /**
   * Adiciona/remove template dos favoritos
   */
  static async toggleFavorite(templateId: string): Promise<{ favorited: boolean }> {
    const response = await api.post(`/marketplace/templates/${templateId}/favorite`)
    return response.data.data
  }

  /**
   * Adiciona/atualiza review de um template
   */
  static async addReview(templateId: string, review: {
    rating: number
    comment?: string
  }): Promise<void> {
    await api.post(`/marketplace/templates/${templateId}/review`, review)
  }

  /**
   * Obtém categorias disponíveis
   */
  static async getCategories(): Promise<string[]> {
    const response = await api.get('/marketplace/categories')
    return response.data.data
  }

  /**
   * Obtém templates em destaque
   */
  static async getFeaturedTemplates(): Promise<MarketplaceTemplate[]> {
    const response = await api.get('/marketplace/featured')
    return response.data.data
  }

  /**
   * Obtém templates populares
   */
  static async getPopularTemplates(limit: number = 10): Promise<MarketplaceTemplate[]> {
    const response = await api.get(`/marketplace/templates?sortBy=popular&limit=${limit}`)
    return response.data.data.templates
  }

  /**
   * Obtém templates recentes
   */
  static async getRecentTemplates(limit: number = 10): Promise<MarketplaceTemplate[]> {
    const response = await api.get(`/marketplace/templates?sortBy=recent&limit=${limit}`)
    return response.data.data.templates
  }

  /**
   * Obtém templates por categoria
   */
  static async getTemplatesByCategory(category: string, limit: number = 20): Promise<MarketplaceSearchResult> {
    return this.searchTemplates({ category, limit })
  }

  /**
   * Obtém templates por tags
   */
  static async getTemplatesByTags(tags: string[], limit: number = 20): Promise<MarketplaceSearchResult> {
    return this.searchTemplates({ tags, limit })
  }

  /**
   * Busca templates por texto
   */
  static async searchTemplatesByText(search: string, limit: number = 20): Promise<MarketplaceSearchResult> {
    return this.searchTemplates({ search, limit })
  }

  /**
   * Obtém estatísticas do marketplace
   */
  static async getMarketplaceStats(): Promise<{
    totalTemplates: number
    totalDownloads: number
    totalUsers: number
    popularCategories: Array<{ category: string; count: number }>
  }> {
    // Esta seria uma rota adicional para estatísticas gerais
    // Por enquanto, vamos simular os dados
    return {
      totalTemplates: 0,
      totalDownloads: 0,
      totalUsers: 0,
      popularCategories: []
    }
  }

  /**
   * Valida dados de publicação no marketplace
   */
  static validatePublishData(data: {
    category: string
    tags: string[]
    deviceTypes: string[]
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!data.category || data.category.trim() === '') {
      errors.push('Categoria é obrigatória')
    }

    if (!data.tags || data.tags.length === 0) {
      errors.push('Pelo menos uma tag é obrigatória')
    }

    if (data.tags && data.tags.some(tag => tag.trim() === '')) {
      errors.push('Tags não podem estar vazias')
    }

    if (!data.deviceTypes || data.deviceTypes.length === 0) {
      errors.push('Pelo menos um tipo de dispositivo é obrigatório')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Formata dados para exibição
   */
  static formatTemplateForDisplay(template: MarketplaceTemplate) {
    return {
      ...template,
      formattedDownloads: this.formatNumber(template.downloads),
      formattedLikes: this.formatNumber(template.likes),
      formattedRating: template.stats.averageRating.toFixed(1),
      formattedDate: new Date(template.createdAt).toLocaleDateString('pt-BR'),
      tagsString: template.tags.join(', '),
      deviceTypesString: template.deviceTypes.join(', ')
    }
  }

  /**
   * Formata números para exibição
   */
  private static formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  /**
   * Gera cores para categorias
   */
  static getCategoryColor(category: string): string {
    const colors = [
      'bg-blue-100 text-blue-800',
      'bg-green-100 text-green-800',
      'bg-yellow-100 text-yellow-800',
      'bg-red-100 text-red-800',
      'bg-purple-100 text-purple-800',
      'bg-indigo-100 text-indigo-800',
      'bg-pink-100 text-pink-800',
      'bg-gray-100 text-gray-800'
    ]
    
    const hash = category.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0)
    
    return colors[Math.abs(hash) % colors.length]
  }

  /**
   * Gera ícone para categoria
   */
  static getCategoryIcon(category: string): string {
    const icons: { [key: string]: string } = {
      'Network': '🌐',
      'Security': '🔒',
      'Monitoring': '📊',
      'Backup': '💾',
      'System': '⚙️',
      'Database': '🗄️',
      'Web': '🌍',
      'DevOps': '🚀',
      'Cloud': '☁️',
      'Automation': '🤖'
    }
    
    return icons[category] || '📋'
  }
}

export default MarketplaceService

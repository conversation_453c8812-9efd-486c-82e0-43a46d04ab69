# Implementação da API Nativa do Mikrotik

## Visão Geral

Este documento descreve a implementação da API nativa do Mikrotik para executar comandos em dispositivos RouterOS, substituindo a abordagem anterior baseada apenas em SSH.

## Motivação

A implementação anterior usando apenas SSH para dispositivos Mikrotik enfrentava problemas de timeout de keepalive, mesmo com configurações de timeout dinâmico. A API nativa do Mikrotik oferece uma conexão mais estável e eficiente para gerenciar dispositivos Mikrotik.

## Implementação

A nova implementação utiliza uma abordagem híbrida:

1. **API Nativa como Primeira Opção**: Tenta executar comandos usando a API nativa do Mikrotik
2. **SSH como Fallback**: Se a API falhar, usa SSH como método alternativo

### Biblioteca Utilizada

- **node-routeros-v2**: Uma implementação moderna da API do Mikrotik em TypeScript/JavaScript
  - Suporta promessas (promises)
  - Gerencia conexões e reconexões automaticamente
  - Mantém conexões ativas (keepalive)

### Arquitetura

A implementação mantém a mesma interface pública do executor anterior, garantindo compatibilidade com o restante do sistema:

- `MikrotikExecutor` implementa a interface `ICommandExecutor`
- Extrai informações de conexão da sessão SSH existente
- Inicializa a API do RouterOS com as mesmas credenciais
- Implementa lógica de fallback para SSH quando a API falha

### Funcionalidades

1. **Conexão Automática**: Tenta conectar à API quando necessário
2. **Reconexão**: Implementa lógica de reconexão em caso de falha
3. **Formatação de Comandos**: Converte comandos do formato CLI para o formato da API
4. **Formatação de Resultados**: Converte resultados da API para o formato esperado pelo sistema
5. **Suporte a Comandos Múltiplos**: Executa comandos com múltiplas linhas
6. **Limpeza de Recursos**: Fecha conexões da API quando o serviço é encerrado

## Vantagens

1. **Estabilidade**: Menos problemas de timeout de keepalive
2. **Desempenho**: Execução de comandos mais rápida
3. **Eficiência**: Menor consumo de recursos no dispositivo Mikrotik
4. **Confiabilidade**: Melhor tratamento de erros e reconexão

## Limitações

1. **Porta da API**: Assume que a porta padrão da API (8728) está disponível
2. **Compatibilidade de Comandos**: Alguns comandos podem ter sintaxe ligeiramente diferente entre CLI e API
3. **Fallback para SSH**: Em alguns casos, pode ser necessário usar SSH como alternativa

## Exemplo de Uso

A implementação é transparente para o restante do sistema. Os comandos são executados da mesma forma:

```typescript
// O código cliente não precisa mudar
const sshService = new SSHService();
await sshService.connect(server);
const result = await sshService.executeCommand('/system identity print');
```

## Troubleshooting

### Problemas Comuns

1. **Porta da API Bloqueada**: Verifique se a porta 8728 está acessível no dispositivo Mikrotik
2. **Credenciais Inválidas**: Verifique se as credenciais têm permissão para acessar a API
3. **Comandos Incompatíveis**: Alguns comandos podem precisar de ajustes para funcionar via API

### Logs

A implementação inclui logs detalhados para ajudar na depuração:

- Inicialização da API
- Tentativas de conexão
- Execução de comandos
- Fallback para SSH
- Erros e reconexões

## Referências

- [Documentação da API do Mikrotik](https://help.mikrotik.com/docs/spaces/ROS/pages/47579160/API)
- [Documentação do node-routeros-v2](https://github.com/jace254/node-routeros)

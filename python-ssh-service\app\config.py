import os
from pydantic import BaseSettings
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    app_name: str = "SSH Service"
    debug: bool = os.getenv("DEBUG", "False").lower() == "true"
    log_level: str = os.getenv("LOG_LEVEL", "INFO")
    
    # Configurações de segurança
    token_secret: str = os.getenv("TOKEN_SECRET", "")
    
    # Configurações de timeout
    default_timeout: int = int(os.getenv("DEFAULT_TIMEOUT", "60"))
    max_timeout: int = int(os.getenv("MAX_TIMEOUT", "300"))
    
    # Configurações de conexão
    max_concurrent_connections: int = int(os.getenv("MAX_CONCURRENT_CONNECTIONS", "10"))
    connection_pool_size: int = int(os.getenv("CONNECTION_POOL_SIZE", "5"))
    
    # Configurações específicas para dispositivos
    huawei_timeout: int = int(os.getenv("HUAWEI_TIMEOUT", "90"))
    huawei_keepalive: int = int(os.getenv("HUAWEI_KEEPALIVE", "10"))
    
    # Redis para cache (opcional)
    redis_url: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    class Config:
        env_file = ".env"

settings = Settings()

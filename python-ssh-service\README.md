# Python SSH Service

Microserviço especializado para conexão SSH com dispositivos de rede problemáticos, especialmente Huawei/HarmonyOS, Nokia e outros equipamentos que apresentam dificuldades com implementações Node.js.

## 🎯 Objetivo

Este serviço foi desenvolvido para resolver problemas específicos de conectividade e estabilidade com dispositivos de rede que causam travamentos ou timeouts na implementação Node.js principal.

## 🚀 Funcionalidades

- **Execução de comandos SSH** otimizada para diferentes tipos de dispositivos
- **Suporte especializado** para Huawei/HarmonyOS com timeouts estendidos
- **Detecção automática** de informações do dispositivo
- **Execução em lote** para múltiplos comandos
- **Teste de conectividade** sem execução de comandos
- **Estatísticas** de uso e performance
- **API REST** completa com documentação automática

## 🛠️ Tipos de Dispositivos Suportados

- **Huawei/HarmonyOS** (VRP) - Configurações otimizadas
- **Nokia** (SR OS) - Timeouts estendidos
- **Mikrotik** (RouterOS) - Configurações padrão
- **Cisco** (IOS) - Configurações padrão
- **Linux** - Servidores padrão
- **Windows** - Servidores Windows
- **Generic** - Terminal servers e outros

## 📋 Pré-requisitos

- Python 3.10 ou superior
- pip (gerenciador de pacotes Python)
- Acesso de rede aos dispositivos SSH

## 🔧 Instalação e Configuração

### Instalação Local

1. **Clone o repositório** (se ainda não foi feito):
```bash
git clone [url-do-repositorio]
cd sem-fronteiras/python-ssh-service
```

2. **Execute o script de inicialização**:
```bash
./start.sh
```

O script irá:
- Criar ambiente virtual Python
- Instalar dependências
- Configurar arquivo .env
- Iniciar o serviço

### Instalação Manual

1. **Criar ambiente virtual**:
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate     # Windows
```

2. **Instalar dependências**:
```bash
pip install -r requirements.txt
```

3. **Configurar variáveis de ambiente**:
```bash
cp .env.example .env
# Edite o arquivo .env conforme necessário
```

4. **Iniciar o serviço**:
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

### Instalação com Docker

```bash
# Construir a imagem
docker build -t python-ssh-service .

# Executar o container
docker run -p 8000:8000 -v $(pwd)/logs:/app/logs python-ssh-service
```

## 🌐 Uso da API

### Endpoints Principais

#### Executar Comando SSH
```http
POST /ssh/execute
Content-Type: application/json

{
  "host": "***********",
  "port": 22,
  "username": "admin",
  "password": "password123",
  "command": "display version",
  "device_type": "huawei",
  "timeout": 90
}
```

#### Executar Múltiplos Comandos
```http
POST /ssh/execute-batch
Content-Type: application/json

[
  {
    "host": "***********",
    "username": "admin",
    "password": "password123",
    "command": "display version",
    "device_type": "huawei"
  },
  {
    "host": "***********",
    "username": "admin",
    "password": "password123",
    "command": "show version",
    "device_type": "nokia"
  }
]
```

#### Testar Conectividade
```http
POST /ssh/test-connection
Content-Type: application/json

{
  "host": "***********",
  "username": "admin",
  "password": "password123",
  "device_type": "huawei"
}
```

#### Obter Estatísticas
```http
GET /ssh/stats
```

#### Health Check
```http
GET /health
```

### Documentação Interativa

Acesse a documentação completa da API em:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🔧 Configuração

### Variáveis de Ambiente

| Variável | Padrão | Descrição |
|----------|--------|-----------|
| `DEBUG` | `false` | Modo debug |
| `LOG_LEVEL` | `INFO` | Nível de log |
| `DEFAULT_TIMEOUT` | `60` | Timeout padrão (segundos) |
| `MAX_TIMEOUT` | `300` | Timeout máximo (segundos) |
| `MAX_CONCURRENT_CONNECTIONS` | `10` | Conexões simultâneas |
| `HUAWEI_TIMEOUT` | `90` | Timeout para Huawei (segundos) |
| `HUAWEI_KEEPALIVE` | `10` | Keepalive para Huawei (segundos) |
| `REDIS_URL` | `redis://localhost:6379` | URL do Redis (opcional) |

### Configurações por Dispositivo

#### Huawei/HarmonyOS
- Timeout estendido (90 segundos)
- Delay factor aumentado (2x)
- Keepalive mais frequente
- Tratamento especial para prompts

#### Nokia
- Timeout de 90 segundos
- Delay factor de 1.5x
- Configurações otimizadas para SR OS

#### Outros Dispositivos
- Configurações padrão do Netmiko
- Timeouts ajustáveis por requisição

## 🧪 Testes

### Executar Testes
```bash
# Executar todos os testes
./start.sh test

# Ou manualmente
python -m pytest tests/ -v

# Executar testes específicos
python -m pytest tests/test_ssh.py::TestSSHService::test_connect_and_execute_success -v
```

### Cobertura de Testes
```bash
pip install pytest-cov
python -m pytest tests/ --cov=app --cov-report=html
```

## 📊 Monitoramento

### Logs
- Logs são salvos em `logs/app.log`
- Logs de sessão SSH em `logs/{host}_{timestamp}.log`
- Nível de log configurável via `LOG_LEVEL`

### Métricas
- Total de comandos executados
- Taxa de sucesso/falha
- Tempo médio de execução
- Conexões ativas

### Health Check
```bash
curl http://localhost:8000/health
```

## 🔗 Integração com Node.js

O serviço é automaticamente integrado com o backend Node.js através da classe `PythonSSHService`. A integração inclui:

- **Roteamento inteligente**: Comandos para dispositivos problemáticos são automaticamente roteados para o Python
- **Fallback**: Se o serviço Python falhar, usa a implementação Node.js como backup
- **Transparência**: A API principal não muda, a integração é transparente

## 🐛 Troubleshooting

### Problemas Comuns

1. **Erro de conexão SSH**:
   - Verificar credenciais
   - Verificar conectividade de rede
   - Verificar se o SSH está habilitado no dispositivo

2. **Timeout de comando**:
   - Aumentar o timeout na requisição
   - Verificar se o comando é válido para o dispositivo
   - Verificar logs para mais detalhes

3. **Erro de dependências**:
   - Reinstalar dependências: `pip install -r requirements.txt`
   - Verificar versão do Python (3.10+)

### Logs de Debug

Para habilitar logs detalhados:
```bash
export DEBUG=true
export LOG_LEVEL=DEBUG
```

## 📈 Performance

### Otimizações Implementadas

- **Pool de conexões**: Reutilização de conexões quando possível
- **Timeouts dinâmicos**: Ajustados por tipo de dispositivo
- **Execução assíncrona**: Suporte a múltiplos comandos simultâneos
- **Configurações específicas**: Otimizadas para cada tipo de equipamento

### Limites

- Máximo de 10 conexões simultâneas (configurável)
- Timeout máximo de 5 minutos
- Logs rotacionados automaticamente

## 🔒 Segurança

- Credenciais não são armazenadas
- Logs de sessão podem ser desabilitados
- Validação de entrada rigorosa
- Execução em usuário não-root no Docker

## 🤝 Contribuição

Para contribuir com o projeto:

1. Faça um fork do repositório
2. Crie uma branch para sua feature
3. Implemente os testes
4. Faça commit das mudanças
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a mesma licença do projeto principal REMOTEOPS.

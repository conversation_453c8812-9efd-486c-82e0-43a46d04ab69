# Alert rules for RemoteOps monitoring

groups:
  - name: remoteops.rules
    rules:
      # Instance down alerts
      - alert: InstanceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Instance {{ $labels.instance }} down"
          description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minute."

      # Backend health alerts
      - alert: BackendUnhealthy
        expr: remoteops_health_status < 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "RemoteOps backend {{ $labels.instance }} unhealthy"
          description: "Backend instance {{ $labels.instance }} has been unhealthy for more than 2 minutes."

      - alert: BackendCritical
        expr: remoteops_health_status == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "RemoteOps backend {{ $labels.instance }} critical"
          description: "Backend instance {{ $labels.instance }} is in critical state."

      # Database alerts
      - alert: DatabaseUnhealthy
        expr: remoteops_database_status < 1
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Database unhealthy on {{ $labels.instance }}"
          description: "Database connection is unhealthy on instance {{ $labels.instance }}."

      - alert: DatabaseSlowResponse
        expr: remoteops_database_response_time > 2000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Database slow response on {{ $labels.instance }}"
          description: "Database response time is {{ $value }}ms on instance {{ $labels.instance }}."

      # Redis alerts
      - alert: RedisUnhealthy
        expr: remoteops_redis_status < 1
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Redis unhealthy on {{ $labels.instance }}"
          description: "Redis connection is unhealthy on instance {{ $labels.instance }}."

      # Memory alerts
      - alert: HighMemoryUsage
        expr: remoteops_memory_usage_bytes{type="used"} / remoteops_memory_usage_bytes{type="total"} > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is above 80% on instance {{ $labels.instance }}."

      - alert: CriticalMemoryUsage
        expr: remoteops_memory_usage_bytes{type="used"} / remoteops_memory_usage_bytes{type="total"} > 0.9
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Critical memory usage on {{ $labels.instance }}"
          description: "Memory usage is above 90% on instance {{ $labels.instance }}."

      # Error rate alerts
      - alert: HighErrorRate
        expr: remoteops_error_rate > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate on {{ $labels.instance }}"
          description: "Error rate is {{ $value }}% on instance {{ $labels.instance }}."

      - alert: CriticalErrorRate
        expr: remoteops_error_rate > 10
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Critical error rate on {{ $labels.instance }}"
          description: "Error rate is {{ $value }}% on instance {{ $labels.instance }}."

      # Response time alerts
      - alert: SlowResponseTime
        expr: remoteops_response_time_avg > 2000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Slow response time on {{ $labels.instance }}"
          description: "Average response time is {{ $value }}ms on instance {{ $labels.instance }}."

      # Python microservice alerts
      - alert: PythonMicroserviceUnhealthy
        expr: remoteops_python_microservice_status < 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Python microservice unhealthy on {{ $labels.instance }}"
          description: "Python microservice is unhealthy on instance {{ $labels.instance }}."

      # Load balancer alerts
      - alert: LoadBalancerDown
        expr: up{job="nginx"} == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "Load balancer down"
          description: "NGINX load balancer is down."

      # Multiple instances down
      - alert: MultipleInstancesDown
        expr: count(up{job="remoteops-backend"} == 0) >= 2
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Multiple backend instances down"
          description: "{{ $value }} backend instances are down."

      # No healthy instances
      - alert: NoHealthyInstances
        expr: count(remoteops_health_status == 1) == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "No healthy backend instances"
          description: "All backend instances are unhealthy or down."

  - name: system.rules
    rules:
      # System-level alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is above 80% on {{ $labels.instance }}."

      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High disk usage on {{ $labels.instance }}"
          description: "Disk usage is above 80% on {{ $labels.instance }}."

      - alert: HighNetworkTraffic
        expr: rate(node_network_receive_bytes_total[5m]) > 100000000  # 100MB/s
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High network traffic on {{ $labels.instance }}"
          description: "Network receive traffic is above 100MB/s on {{ $labels.instance }}."

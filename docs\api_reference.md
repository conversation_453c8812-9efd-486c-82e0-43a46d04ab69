# 📡 API Reference - REMOTEOPS

## 📋 Visão Geral

Esta documentação detalha todos os endpoints da API REST do sistema REMOTEOPS SSH Management System.

**Base URL**: `http://localhost:3001` (desenvolvimento) ou `https://seu-dominio.com/api` (produção)

## 🔐 Autenticação

Todas as rotas (exceto login) requerem autenticação via JWT Bearer Token.

```http
Authorization: Bearer <seu_jwt_token>
```

### POST /login
Autentica um usuário e retorna JWT token.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "senha123"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "1",
    "email": "<EMAIL>",
    "name": "Administrador",
    "role": "ADMIN"
  }
}
```

## 👥 Usuários

### GET /api/users
Lista todos os usuários.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "1",
      "email": "<EMAIL>",
      "name": "Administrador",
      "role": "ADMIN",
      "isActive": true,
      "createdAt": "2023-12-01T10:00:00Z"
    }
  ]
}
```

### POST /api/users
Cria um novo usuário.

**Request:**
```json
{
  "email": "<EMAIL>",
  "name": "Novo Usuário",
  "password": "senha123",
  "role": "USER"
}
```

### PUT /api/users/:id
Atualiza um usuário existente.

### DELETE /api/users/:id
Remove um usuário.

## 🖥️ Servidores

### GET /api/servers
Lista todos os servidores do usuário.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "1",
      "name": "Router Principal",
      "host": "***********",
      "port": 22,
      "username": "admin",
      "deviceType": "HUAWEI",
      "description": "Router principal da rede",
      "isActive": true,
      "groupId": "1"
    }
  ]
}
```

### POST /api/servers
Cria um novo servidor.

**Request:**
```json
{
  "name": "Novo Router",
  "host": "***********0",
  "port": 22,
  "username": "admin",
  "password": "senha123",
  "deviceType": "MIKROTIK",
  "description": "Router secundário",
  "groupId": "1"
}
```

### PUT /api/servers/:id
Atualiza um servidor existente.

### DELETE /api/servers/:id
Remove um servidor.

### POST /api/servers/:id/test-connection
Testa a conexão com um servidor.

**Response:**
```json
{
  "success": true,
  "data": {
    "connected": true,
    "responseTime": 1250,
    "deviceInfo": "VRP (R) software, Version 8.180"
  }
}
```

## ⚡ Comandos SSH

### POST /api/ssh/connect
Conecta a um servidor SSH.

**Request:**
```json
{
  "serverId": "1"
}
```

### POST /api/ssh/execute
Executa um comando SSH.

**Request:**
```json
{
  "command": "display version"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "stdout": "Huawei Versatile Routing Platform Software\nVRP (R) software, Version 8.180...",
    "stderr": "",
    "code": 0,
    "executionTime": 1500,
    "timestamp": "2023-12-01T10:30:00Z"
  }
}
```

### POST /api/ssh/disconnect
Desconecta do servidor atual.

## 📝 Templates de Comandos

### GET /api/command-templates
Lista todos os templates de comandos.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "1",
      "name": "Verificar Versão",
      "command": "display version",
      "description": "Mostra informações de versão do sistema",
      "deviceTypes": ["HUAWEI", "NOKIA"],
      "category": "SYSTEM",
      "isPublic": true
    }
  ]
}
```

### POST /api/command-templates
Cria um novo template.

**Request:**
```json
{
  "name": "Backup Config",
  "command": "display current-configuration",
  "description": "Faz backup da configuração atual",
  "deviceTypes": ["HUAWEI"],
  "category": "BACKUP",
  "isPublic": false
}
```

## 📊 Grupos de Servidores

### GET /api/server-groups
Lista todos os grupos de servidores.

### POST /api/server-groups
Cria um novo grupo.

**Request:**
```json
{
  "name": "Routers Principais",
  "description": "Grupo dos routers principais da rede",
  "color": "#3B82F6"
}
```

## 📈 Monitoramento

### GET /api/monitoring/health
Verifica a saúde do sistema.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "services": {
      "nodejs": { "status": "healthy", "uptime": 86400 },
      "python": { "status": "healthy", "uptime": 86400 },
      "database": { "status": "healthy", "connections": 5 },
      "redis": { "status": "healthy", "memory": "45MB" }
    },
    "alerts": [],
    "lastCheck": "2023-12-01T10:30:00Z"
  }
}
```

### GET /api/monitoring/metrics/devices
Obtém métricas por tipo de dispositivo.

### GET /api/monitoring/metrics/services
Obtém métricas por serviço (Node.js/Python).

### GET /api/monitoring/performance
Obtém métricas de performance.

**Query Parameters:**
- `timeRange`: `1h`, `24h`, `7d`, `30d` (padrão: `24h`)

### GET /api/monitoring/alerts
Lista alertas ativos.

### GET /api/monitoring/alerts/history
Histórico de alertas.

**Query Parameters:**
- `limit`: Número máximo de alertas (padrão: 100)

## ⚡ Cache

### GET /api/monitoring/cache/stats
Estatísticas do cache.

**Response:**
```json
{
  "success": true,
  "data": {
    "totalKeys": 150,
    "memoryUsage": 25600000,
    "hitRate": 0.85,
    "totalHits": 1250,
    "totalMisses": 220,
    "evictions": 5
  }
}
```

### GET /api/monitoring/cache/popular
Comandos mais populares do cache.

### POST /api/monitoring/cache/clear
Limpa todo o cache.

### POST /api/monitoring/cache/invalidate/:serverId
Invalida cache para um servidor específico.

## 🚀 Performance

### GET /api/monitoring/performance/stats
Estatísticas de performance dos servidores.

### GET /api/monitoring/performance/recommendations
Recomendações de otimização.

### POST /api/monitoring/performance/optimize
Força otimização para todos os servidores.

### GET /api/monitoring/performance/settings/:serverId
Configurações otimizadas para um servidor.

## 🔒 Backup

### GET /api/backup/list
Lista todos os backups disponíveis.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "backup-2023-12-01T02-00-00-000Z",
      "timestamp": "2023-12-01T02:00:00Z",
      "version": "1.0.0",
      "size": 2048576,
      "tables": ["users", "servers", "commandHistory"],
      "checksum": "a1b2c3d4e5f6...",
      "description": "Backup automático"
    }
  ]
}
```

### POST /api/backup/create
Cria um novo backup.

**Request:**
```json
{
  "includeHistory": true,
  "includeLogs": false,
  "compress": true,
  "description": "Backup manual antes da atualização"
}
```

### POST /api/backup/restore
Restaura um backup.

**Request:**
```json
{
  "backupId": "backup-2023-12-01T02-00-00-000Z",
  "skipTables": ["commandHistory"],
  "dryRun": false
}
```

### DELETE /api/backup/:backupId
Remove um backup específico.

### POST /api/backup/cleanup
Remove backups antigos baseado na política de retenção.

### GET /api/backup/status
Status do sistema de backup.

## 📊 Dashboard

### GET /api/monitoring/dashboard
Dados consolidados para dashboard.

**Response:**
```json
{
  "success": true,
  "data": {
    "devices": [...],
    "services": [...],
    "health": {...},
    "performance": {...},
    "alerts": [...],
    "cache": {...}
  }
}
```

## 🔍 Histórico

### GET /api/history
Lista histórico de comandos.

**Query Parameters:**
- `page`: Página (padrão: 1)
- `limit`: Itens por página (padrão: 50)
- `serverId`: Filtrar por servidor
- `userId`: Filtrar por usuário
- `startDate`: Data inicial (ISO 8601)
- `endDate`: Data final (ISO 8601)

## ❌ Códigos de Erro

### Códigos HTTP
- `200` - Sucesso
- `201` - Criado
- `400` - Requisição inválida
- `401` - Não autorizado
- `403` - Acesso negado
- `404` - Não encontrado
- `409` - Conflito
- `422` - Entidade não processável
- `500` - Erro interno do servidor

### Formato de Erro
```json
{
  "success": false,
  "error": "Tipo do erro",
  "message": "Descrição detalhada do erro",
  "details": {
    "field": "Campo específico com erro",
    "code": "ERROR_CODE"
  }
}
```

## 📝 Notas Importantes

1. **Rate Limiting**: 100 requisições por 15 minutos por IP
2. **Timeout**: 30 segundos para comandos SSH
3. **Tamanho máximo**: 10MB para uploads
4. **Formato de data**: ISO 8601 (UTC)
5. **Paginação**: Links de navegação no header `Link`

## 🔧 Exemplos de Uso

### Fluxo Completo
```javascript
// 1. Login
const loginResponse = await fetch('/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email: '<EMAIL>', password: 'senha123' })
});
const { token } = await loginResponse.json();

// 2. Listar servidores
const serversResponse = await fetch('/api/servers', {
  headers: { 'Authorization': `Bearer ${token}` }
});
const { data: servers } = await serversResponse.json();

// 3. Conectar ao servidor
await fetch('/api/ssh/connect', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ serverId: servers[0].id })
});

// 4. Executar comando
const commandResponse = await fetch('/api/ssh/execute', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ command: 'display version' })
});
const result = await commandResponse.json();
```

---

**Documentação da API - REMOTEOPS SSH Management System**

import { api } from '../lib/api'

export interface CacheStats {
  totalKeys: number
  memoryUsage: number
  hitRate: number
  totalHits: number
  totalMisses: number
  evictions: number
}

export interface PopularCommand {
  command: string
  hits: number
  serverId: string
  serverName?: string
  lastUsed: string
}

export interface CacheConfig {
  defaultTTL: number
  maxMemory: string
  evictionPolicy: string
  enabled: boolean
}

export interface CacheEntry {
  key: string
  command: string
  serverId: string
  serverName?: string
  hits: number
  timestamp: string
  ttl: number
  size: number
}

export interface CacheMetrics {
  hitRate: number
  missRate: number
  evictionRate: number
  memoryUsage: number
  keyCount: number
  averageResponseTime: number
  topCommands: PopularCommand[]
  serverDistribution: { serverId: string; serverName: string; keyCount: number }[]
}

/**
 * Serviço para gerenciamento de cache distribuído
 */
export class CacheService {
  
  /**
   * Obtém estatísticas do cache
   */
  static async getStats(): Promise<CacheStats> {
    const response = await api.get('/monitoring/cache/stats')
    return response.data.data
  }

  /**
   * Obtém comandos mais populares do cache
   */
  static async getPopularCommands(limit: number = 10): Promise<PopularCommand[]> {
    const response = await api.get(`/monitoring/cache/popular?limit=${limit}`)
    return response.data.data
  }

  /**
   * Limpa todo o cache
   */
  static async clearCache(): Promise<void> {
    await api.post('/monitoring/cache/clear')
  }

  /**
   * Invalida cache para um servidor específico
   */
  static async invalidateServer(serverId: string): Promise<void> {
    await api.post(`/monitoring/cache/invalidate/${serverId}`)
  }

  /**
   * Invalida cache por padrão de comando
   */
  static async invalidatePattern(pattern: string): Promise<void> {
    await api.post('/monitoring/cache/invalidate-pattern', { pattern })
  }

  /**
   * Obtém configuração atual do cache
   */
  static async getConfig(): Promise<CacheConfig> {
    const response = await api.get('/monitoring/cache/config')
    return response.data.data
  }

  /**
   * Atualiza configuração do cache
   */
  static async updateConfig(config: Partial<CacheConfig>): Promise<void> {
    await api.put('/monitoring/cache/config', config)
  }

  /**
   * Obtém todas as entradas do cache
   */
  static async getCacheEntries(page: number = 1, limit: number = 50): Promise<{
    entries: CacheEntry[]
    total: number
    page: number
    totalPages: number
  }> {
    const response = await api.get(`/monitoring/cache/entries?page=${page}&limit=${limit}`)
    return response.data.data
  }

  /**
   * Remove uma entrada específica do cache
   */
  static async removeEntry(key: string): Promise<void> {
    await api.delete(`/monitoring/cache/entries/${encodeURIComponent(key)}`)
  }

  /**
   * Obtém métricas detalhadas do cache
   */
  static async getMetrics(timeRange: '1h' | '24h' | '7d' = '24h'): Promise<CacheMetrics> {
    const response = await api.get(`/monitoring/cache/metrics?timeRange=${timeRange}`)
    return response.data.data
  }

  /**
   * Força refresh do cache para um servidor
   */
  static async refreshServer(serverId: string): Promise<void> {
    await api.post(`/monitoring/cache/refresh/${serverId}`)
  }

  /**
   * Obtém informações de saúde do Redis
   */
  static async getRedisHealth(): Promise<{
    connected: boolean
    uptime: number
    version: string
    memoryUsage: number
    maxMemory: number
    keyspaceHits: number
    keyspaceMisses: number
    connectedClients: number
  }> {
    const response = await api.get('/monitoring/cache/redis-health')
    return response.data.data
  }

  /**
   * Executa comando Redis personalizado (apenas para admins)
   */
  static async executeRedisCommand(command: string): Promise<any> {
    const response = await api.post('/monitoring/cache/redis-command', { command })
    return response.data.data
  }

  /**
   * Obtém estatísticas de performance por servidor
   */
  static async getServerPerformance(): Promise<Array<{
    serverId: string
    serverName: string
    cacheHitRate: number
    averageResponseTime: number
    totalCommands: number
    cachedCommands: number
  }>> {
    const response = await api.get('/monitoring/cache/server-performance')
    return response.data.data
  }

  /**
   * Exporta configurações do cache
   */
  static async exportConfig(): Promise<Blob> {
    const response = await api.get('/monitoring/cache/export-config', {
      responseType: 'blob'
    })
    return response.data
  }

  /**
   * Importa configurações do cache
   */
  static async importConfig(file: File): Promise<void> {
    const formData = new FormData()
    formData.append('config', file)
    
    await api.post('/monitoring/cache/import-config', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  /**
   * Obtém recomendações de otimização do cache
   */
  static async getOptimizationRecommendations(): Promise<Array<{
    type: 'warning' | 'info' | 'success'
    title: string
    description: string
    action?: string
    impact: 'low' | 'medium' | 'high'
  }>> {
    const response = await api.get('/monitoring/cache/recommendations')
    return response.data.data
  }

  /**
   * Aplica otimizações automáticas
   */
  static async applyOptimizations(): Promise<{
    applied: number
    skipped: number
    errors: string[]
  }> {
    const response = await api.post('/monitoring/cache/optimize')
    return response.data.data
  }
}

export default CacheService

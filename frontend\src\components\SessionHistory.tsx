import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  TextField,
  Box,
  Typography,
  Chip,
  Divider,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Menu,
  MenuItem,
  ListItemIcon
} from '@mui/material';
import {
  History as HistoryIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as StatsIcon,
  Clear as ClearIcon,
  Download as DownloadIcon,
  PlayArrow as PlayIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { 
  useCommandHistory, 
  useFrequentCommands, 
  useServerCommandStats 
} from '../hooks/useCommandHistory';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface SessionHistoryProps {
  open: boolean;
  onClose: () => void;
  onSelectCommand?: (command: string) => void;
  currentServerId?: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ pt: 2 }}>{children}</Box>}
    </div>
  );
};

export const SessionHistory: React.FC<SessionHistoryProps> = ({
  open,
  onClose,
  onSelectCommand,
  currentServerId
}) => {
  const {
    history,
    clearHistory,
    searchHistory,
    removeCommand,
    exportHistory,
    getSessionStats,
    getCommandsByServer
  } = useCommandHistory();

  const frequentCommands = useFrequentCommands();
  const serverStats = useServerCommandStats();

  const [searchQuery, setSearchQuery] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const filteredHistory = searchQuery 
    ? searchHistory(searchQuery)
    : currentServerId 
      ? getCommandsByServer(currentServerId)
      : history;

  const sessionStats = getSessionStats();

  const handleSelectCommand = (command: string) => {
    if (onSelectCommand) {
      onSelectCommand(command);
    }
    onClose();
  };

  const handleExport = () => {
    const data = exportHistory();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `historico-sessao-${format(new Date(), 'yyyy-MM-dd-HH-mm')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    handleMenuClose();
  };

  const handleClearHistory = () => {
    if (window.confirm('Tem certeza que deseja limpar todo o histórico da sessão?')) {
      clearHistory();
      handleMenuClose();
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const getStatusIcon = (success?: boolean) => {
    if (success === true) return <SuccessIcon color="success" fontSize="small" />;
    if (success === false) return <ErrorIcon color="error" fontSize="small" />;
    return null;
  };

  const renderHistoryList = (commandsList: typeof history) => (
    <List>
      {commandsList.map((entry) => (
        <ListItem key={entry.id} divider>
          <ListItemText
            primary={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography
                  variant="body1"
                  sx={{ 
                    fontFamily: 'monospace',
                    cursor: 'pointer',
                    '&:hover': { color: 'primary.main' }
                  }}
                  onClick={() => handleSelectCommand(entry.command)}
                >
                  {entry.command}
                </Typography>
                {getStatusIcon(entry.success)}
                {entry.deviceType && (
                  <Chip label={entry.deviceType} size="small" />
                )}
              </Box>
            }
            secondary={
              <Box>
                {entry.serverName && (
                  <Typography variant="body2" color="text.secondary">
                    Servidor: {entry.serverName}
                  </Typography>
                )}
                <Typography variant="caption" color="text.secondary">
                  {format(entry.timestamp, 'dd/MM/yyyy HH:mm:ss', { locale: ptBR })}
                </Typography>
                {entry.result && (
                  <Typography 
                    variant="caption" 
                    color="text.secondary"
                    sx={{ 
                      display: 'block',
                      maxWidth: '300px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    Resultado: {entry.result}
                  </Typography>
                )}
              </Box>
            }
          />
          <ListItemSecondaryAction>
            <IconButton
              size="small"
              onClick={() => handleSelectCommand(entry.command)}
              title="Usar comando"
            >
              <PlayIcon fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              onClick={() => removeCommand(entry.id)}
              color="error"
              title="Remover do histórico"
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </ListItemSecondaryAction>
        </ListItem>
      ))}
      {commandsList.length === 0 && (
        <ListItem>
          <ListItemText
            primary="Nenhum comando encontrado"
            secondary="Execute alguns comandos para vê-los no histórico da sessão"
          />
        </ListItem>
      )}
    </List>
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <HistoryIcon color="primary" />
            <Typography variant="h6">Histórico da Sessão</Typography>
          </Box>
          <IconButton onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleExport}>
          <ListItemIcon>
            <DownloadIcon fontSize="small" />
          </ListItemIcon>
          Exportar Histórico
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleClearHistory} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <ClearIcon fontSize="small" color="error" />
          </ListItemIcon>
          Limpar Histórico
        </MenuItem>
      </Menu>

      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Pesquisar no histórico..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
          />
        </Box>

        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab 
            label={`Histórico (${filteredHistory.length})`} 
            icon={<HistoryIcon />} 
            iconPosition="start"
          />
          <Tab 
            label={`Frequentes (${frequentCommands.length})`} 
            icon={<TrendingUpIcon />} 
            iconPosition="start"
          />
          <Tab 
            label="Estatísticas" 
            icon={<StatsIcon />} 
            iconPosition="start"
          />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          {renderHistoryList(filteredHistory)}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <List>
            {frequentCommands.map((item, index) => (
              <ListItem key={index} divider>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography
                        variant="body1"
                        sx={{ 
                          fontFamily: 'monospace',
                          cursor: 'pointer',
                          '&:hover': { color: 'primary.main' }
                        }}
                        onClick={() => handleSelectCommand(item.command)}
                      >
                        {item.command}
                      </Typography>
                      <Chip 
                        label={`${item.count}x`} 
                        size="small" 
                        color="primary" 
                        variant="outlined"
                      />
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <IconButton
                    size="small"
                    onClick={() => handleSelectCommand(item.command)}
                  >
                    <PlayIcon fontSize="small" />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Comandos Executados
                  </Typography>
                  <Typography variant="h5">
                    {sessionStats.totalCommands}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {sessionStats.uniqueCommands} únicos
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Card>
                <CardContent>
                  <Typography color="textSecondary" gutterBottom>
                    Taxa de Sucesso
                  </Typography>
                  <Typography variant="h5">
                    {sessionStats.successRate}%
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Comandos com resultado
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Servidores Utilizados ({sessionStats.serversUsed})
                  </Typography>
                  <List dense>
                    {serverStats.map((server) => (
                      <ListItem key={server.serverId}>
                        <ListItemText
                          primary={server.serverName}
                          secondary={
                            <Box>
                              <Typography variant="caption">
                                {server.commandCount} comandos • 
                                {server.successCount > 0 && ` ${Math.round((server.successCount / server.commandCount) * 100)}% sucesso • `}
                                Último uso: {format(server.lastUsed, 'dd/MM HH:mm', { locale: ptBR })}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Fechar</Button>
      </DialogActions>
    </Dialog>
  );
};

export default SessionHistory;

import { FastifyRequest, FastifyReply } from 'fastify'
import { PrismaClient } from '@prisma/client'
import { Logger } from '../utils/Logger'

const prisma = new PrismaClient()

declare module 'fastify' {
  interface FastifyRequest {
    organizationId?: string
    organizationRole?: string
    canAccessResource?: (resourceOrgId?: string) => boolean
  }
}

/**
 * Middleware para isolamento de dados por tenant (organização)
 * Garante que usuários só acessem dados de sua organização
 */
export async function tenantIsolationMiddleware(
  request: FastifyRequest,
  reply: FastifyReply
) {
  try {
    // Pular isolamento para rotas públicas
    const publicRoutes = [
      '/api/auth/login',
      '/api/auth/register',
      '/api/auth/refresh',
      '/api/marketplace', // Marketplace é público
      '/api/health'
    ]

    const isPublicRoute = publicRoutes.some(route => 
      request.url.startsWith(route)
    )

    if (isPublicRoute) {
      return
    }

    // Verificar se usuário está autenticado
    const user = (request as any).user
    if (!user) {
      return reply.status(401).send({
        success: false,
        error: 'Usuário não autenticado'
      })
    }

    // Buscar dados completos do usuário incluindo organização
    const fullUser = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        organization: true
      }
    })

    if (!fullUser) {
      return reply.status(401).send({
        success: false,
        error: 'Usuário não encontrado'
      })
    }

    // Adicionar informações de organização ao request
    request.organizationId = fullUser.organizationId || undefined
    request.organizationRole = fullUser.organizationRole

    // Função helper para verificar acesso a recursos
    request.canAccessResource = (resourceOrgId?: string) => {
      // Super admin pode acessar tudo
      if (fullUser.role === 'ADMIN') {
        return true
      }

      // Se não há organização, só pode acessar recursos próprios
      if (!fullUser.organizationId) {
        return !resourceOrgId || resourceOrgId === fullUser.id
      }

      // Verificar se o recurso pertence à mesma organização
      return !resourceOrgId || resourceOrgId === fullUser.organizationId
    }

    // Para rotas de super admin, verificar permissões especiais
    if (request.url.startsWith('/api/super-admin')) {
      if (fullUser.role !== 'ADMIN') {
        return reply.status(403).send({
          success: false,
          error: 'Acesso negado: apenas super administradores'
        })
      }
    }

    // Para rotas de organização, verificar se usuário pertence a uma organização
    if (request.url.startsWith('/api/organizations')) {
      if (!fullUser.organizationId && !request.url.includes('/create')) {
        return reply.status(403).send({
          success: false,
          error: 'Usuário não pertence a nenhuma organização'
        })
      }
    }

  } catch (error) {
    Logger.error('Erro no middleware de isolamento de tenant:', error)
    return reply.status(500).send({
      success: false,
      error: 'Erro interno do servidor'
    })
  }
}

/**
 * Middleware específico para verificar limites da organização
 */
export async function checkOrganizationLimits(
  request: FastifyRequest,
  reply: FastifyReply,
  resourceType: 'server' | 'user' | 'template'
) {
  try {
    const organizationId = request.organizationId

    if (!organizationId) {
      // Usuários sem organização têm limites padrão
      return
    }

    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
      include: {
        _count: {
          select: {
            servers: true,
            users: true,
            commandTemplates: true
          }
        }
      }
    })

    if (!organization) {
      return reply.status(404).send({
        success: false,
        error: 'Organização não encontrada'
      })
    }

    // Verificar limites baseados no tipo de recurso
    let canCreate = true
    let errorMessage = ''

    switch (resourceType) {
      case 'server':
        if (organization.maxServers > 0 && organization._count.servers >= organization.maxServers) {
          canCreate = false
          errorMessage = `Limite de servidores atingido (${organization.maxServers}). Faça upgrade do seu plano.`
        }
        break

      case 'user':
        if (organization.maxUsers > 0 && organization._count.users >= organization.maxUsers) {
          canCreate = false
          errorMessage = `Limite de usuários atingido (${organization.maxUsers}). Faça upgrade do seu plano.`
        }
        break

      case 'template':
        // Templates podem ter limites específicos no futuro
        break
    }

    if (!canCreate) {
      return reply.status(403).send({
        success: false,
        error: errorMessage,
        code: 'LIMIT_EXCEEDED',
        limits: {
          current: organization._count,
          max: {
            servers: organization.maxServers,
            users: organization.maxUsers
          }
        }
      })
    }

  } catch (error) {
    Logger.error('Erro ao verificar limites da organização:', error)
    return reply.status(500).send({
      success: false,
      error: 'Erro interno do servidor'
    })
  }
}

/**
 * Helper para adicionar filtro de organização às queries
 */
export function addOrganizationFilter(
  baseWhere: any,
  organizationId?: string,
  userId?: string
): any {
  if (!organizationId) {
    // Se não há organização, filtrar por userId
    return {
      ...baseWhere,
      userId
    }
  }

  // Se há organização, filtrar por organizationId
  return {
    ...baseWhere,
    organizationId
  }
}

/**
 * Helper para verificar se usuário pode acessar recurso específico
 */
export async function canAccessResource(
  userId: string,
  resourceType: string,
  resourceId: string
): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return false
    }

    // Super admin pode acessar tudo
    if (user.role === 'ADMIN') {
      return true
    }

    // Verificar baseado no tipo de recurso
    switch (resourceType) {
      case 'server':
        const server = await prisma.server.findUnique({
          where: { id: resourceId }
        })
        return server?.organizationId === user.organizationId || server?.userId === userId

      case 'template':
        const template = await prisma.commandTemplate.findUnique({
          where: { id: resourceId }
        })
        return template?.organizationId === user.organizationId || template?.userId === userId

      case 'serverGroup':
        const group = await prisma.serverGroup.findUnique({
          where: { id: resourceId }
        })
        return group?.organizationId === user.organizationId || group?.userId === userId

      default:
        return false
    }
  } catch (error) {
    Logger.error('Erro ao verificar acesso ao recurso:', error)
    return false
  }
}

/**
 * Decorator para aplicar isolamento de tenant automaticamente
 */
export function withTenantIsolation(handler: Function) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    await tenantIsolationMiddleware(request, reply)
    
    if (reply.sent) {
      return
    }
    
    return handler(request, reply)
  }
}

/**
 * Decorator para verificar limites antes de criar recursos
 */
export function withLimitCheck(resourceType: 'server' | 'user' | 'template') {
  return function(handler: Function) {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      await checkOrganizationLimits(request, reply, resourceType)
      
      if (reply.sent) {
        return
      }
      
      return handler(request, reply)
    }
  }
}

export default tenantIsolationMiddleware

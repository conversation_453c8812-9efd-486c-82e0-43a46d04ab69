#!/bin/bash

# RemoteOps AWS EKS Deployment Script
# Deploys RemoteOps to Amazon EKS with AWS-specific optimizations

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CLUSTER_NAME="remoteops-eks"
REGION="us-west-2"
NODE_GROUP_NAME="remoteops-nodes"
NAMESPACE="remoteops"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check AWS CLI and eksctl
check_prerequisites() {
    log_info "Checking AWS prerequisites..."

    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed"
        echo "Please install AWS CLI: https://docs.aws.amazon.com/cli/latest/userguide/install-cliv2.html"
        exit 1
    fi

    if ! command -v eksctl &> /dev/null; then
        log_error "eksctl is not installed"
        echo "Please install eksctl: https://docs.aws.amazon.com/eks/latest/userguide/eksctl.html"
        exit 1
    fi

    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi

    if ! command -v helm &> /dev/null; then
        log_error "Helm is not installed"
        exit 1
    fi

    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS credentials not configured"
        echo "Please run: aws configure"
        exit 1
    fi

    log_success "Prerequisites check passed"
}

# Create EKS cluster
create_cluster() {
    log_info "Creating EKS cluster: $CLUSTER_NAME"

    # Check if cluster already exists
    if aws eks describe-cluster --name "$CLUSTER_NAME" --region "$REGION" &> /dev/null; then
        log_warning "Cluster $CLUSTER_NAME already exists"
        return
    fi

    # Create cluster configuration
    cat > /tmp/eks-cluster.yaml << EOF
apiVersion: eksctl.io/v1alpha5
kind: ClusterConfig

metadata:
  name: $CLUSTER_NAME
  region: $REGION
  version: "1.28"

# VPC configuration
vpc:
  enableDnsHostnames: true
  enableDnsSupport: true
  cidr: "10.0.0.0/16"

# IAM configuration
iam:
  withOIDC: true
  serviceAccounts:
    - metadata:
        name: aws-load-balancer-controller
        namespace: kube-system
      wellKnownPolicies:
        awsLoadBalancerController: true
    - metadata:
        name: ebs-csi-controller-sa
        namespace: kube-system
      wellKnownPolicies:
        ebsCSIController: true
    - metadata:
        name: cluster-autoscaler
        namespace: kube-system
      wellKnownPolicies:
        autoScaler: true

# Node groups
nodeGroups:
  - name: $NODE_GROUP_NAME
    instanceType: t3.medium
    desiredCapacity: 3
    minSize: 2
    maxSize: 10
    volumeSize: 50
    volumeType: gp3
    amiFamily: AmazonLinux2
    ssh:
      allow: false
    iam:
      withAddonPolicies:
        imageBuilder: true
        autoScaler: true
        externalDNS: true
        certManager: true
        appMesh: true
        appMeshPreview: true
        ebs: true
        fsx: true
        efs: true
        awsLoadBalancerController: true
        xRay: true
        cloudWatch: true
    labels:
      role: worker
      environment: production
    tags:
      k8s.io/cluster-autoscaler/enabled: "true"
      k8s.io/cluster-autoscaler/$CLUSTER_NAME: "owned"

# Add-ons
addons:
  - name: vpc-cni
    version: latest
  - name: coredns
    version: latest
  - name: kube-proxy
    version: latest
  - name: aws-ebs-csi-driver
    version: latest

# CloudWatch logging
cloudWatch:
  clusterLogging:
    enable: ["api", "audit", "authenticator", "controllerManager", "scheduler"]
    logRetentionInDays: 30
EOF

    # Create cluster
    eksctl create cluster -f /tmp/eks-cluster.yaml

    log_success "EKS cluster created successfully"
}

# Install AWS Load Balancer Controller
install_alb_controller() {
    log_info "Installing AWS Load Balancer Controller..."

    # Add EKS Helm repository
    helm repo add eks https://aws.github.io/eks-charts
    helm repo update

    # Install AWS Load Balancer Controller
    helm upgrade --install aws-load-balancer-controller eks/aws-load-balancer-controller \
        -n kube-system \
        --set clusterName="$CLUSTER_NAME" \
        --set serviceAccount.create=false \
        --set serviceAccount.name=aws-load-balancer-controller \
        --set region="$REGION" \
        --set vpcId=$(aws eks describe-cluster --name "$CLUSTER_NAME" --region "$REGION" --query "cluster.resourcesVpcConfig.vpcId" --output text)

    log_success "AWS Load Balancer Controller installed"
}

# Install Cluster Autoscaler
install_cluster_autoscaler() {
    log_info "Installing Cluster Autoscaler..."

    # Create Cluster Autoscaler deployment
    cat > /tmp/cluster-autoscaler.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cluster-autoscaler
  namespace: kube-system
  labels:
    app: cluster-autoscaler
spec:
  selector:
    matchLabels:
      app: cluster-autoscaler
  template:
    metadata:
      labels:
        app: cluster-autoscaler
      annotations:
        prometheus.io/scrape: 'true'
        prometheus.io/port: '8085'
    spec:
      serviceAccountName: cluster-autoscaler
      containers:
      - image: k8s.gcr.io/autoscaling/cluster-autoscaler:v1.21.0
        name: cluster-autoscaler
        resources:
          limits:
            cpu: 100m
            memory: 300Mi
          requests:
            cpu: 100m
            memory: 300Mi
        command:
        - ./cluster-autoscaler
        - --v=4
        - --stderrthreshold=info
        - --cloud-provider=aws
        - --skip-nodes-with-local-storage=false
        - --expander=least-waste
        - --node-group-auto-discovery=asg:tag=k8s.io/cluster-autoscaler/enabled,k8s.io/cluster-autoscaler/$CLUSTER_NAME
        - --balance-similar-node-groups
        - --skip-nodes-with-system-pods=false
        env:
        - name: AWS_REGION
          value: $REGION
        volumeMounts:
        - name: ssl-certs
          mountPath: /etc/ssl/certs/ca-certificates.crt
          readOnly: true
        imagePullPolicy: "Always"
      volumes:
      - name: ssl-certs
        hostPath:
          path: "/etc/ssl/certs/ca-bundle.crt"
EOF

    kubectl apply -f /tmp/cluster-autoscaler.yaml

    log_success "Cluster Autoscaler installed"
}

# Install EBS CSI Driver
install_ebs_csi() {
    log_info "Installing EBS CSI Driver..."

    # The EBS CSI driver is already installed as an add-on
    # Create StorageClass for gp3 volumes
    cat > /tmp/gp3-storageclass.yaml << EOF
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: gp3
  annotations:
    storageclass.kubernetes.io/is-default-class: "true"
provisioner: ebs.csi.aws.com
parameters:
  type: gp3
  iops: "3000"
  throughput: "125"
  encrypted: "true"
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
EOF

    # Remove default annotation from gp2 if it exists
    kubectl annotate storageclass gp2 storageclass.kubernetes.io/is-default-class- || true

    kubectl apply -f /tmp/gp3-storageclass.yaml

    log_success "EBS CSI Driver configured with gp3 StorageClass"
}

# Create AWS-specific values file
create_aws_values() {
    local values_file="$PROJECT_ROOT/kubernetes/helm/remoteops/values-aws.yaml"

    log_info "Creating AWS-specific values file..."

    cat > "$values_file" << EOF
# AWS EKS specific values
global:
  storageClass: "gp3"

app:
  environment: production

# Backend configuration with AWS optimizations
backend:
  replicaCount: 3
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 20
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi

  nodeSelector:
    role: worker

  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: app.kubernetes.io/component
              operator: In
              values:
              - backend
          topologyKey: kubernetes.io/hostname

# Frontend configuration
frontend:
  replicaCount: 2
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10

  nodeSelector:
    role: worker

# Python service configuration
pythonService:
  replicaCount: 2
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 15

  nodeSelector:
    role: worker

# PostgreSQL with EBS volumes
postgresql:
  enabled: true
  primary:
    persistence:
      enabled: true
      storageClass: "gp3"
      size: 100Gi

    resources:
      limits:
        cpu: 2000m
        memory: 4Gi
      requests:
        cpu: 1000m
        memory: 2Gi

    nodeSelector:
      role: worker

# Redis with EBS volumes
redis:
  enabled: true
  master:
    persistence:
      enabled: true
      storageClass: "gp3"
      size: 20Gi

    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi

    nodeSelector:
      role: worker

# Ingress with AWS Load Balancer
ingress:
  enabled: true
  className: "alb"
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:$REGION:ACCOUNT_ID:certificate/CERT_ID"
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/healthcheck-path: /api/health
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '30'
    alb.ingress.kubernetes.io/healthcheck-timeout-seconds: '5'
    alb.ingress.kubernetes.io/healthy-threshold-count: '2'
    alb.ingress.kubernetes.io/unhealthy-threshold-count: '3'

  hosts:
    - host: remoteops.example.com
      paths:
        - path: /
          pathType: Prefix
          service:
            name: frontend
            port: 80
        - path: /api
          pathType: Prefix
          service:
            name: backend
            port: 3000
        - path: /python-api
          pathType: Prefix
          service:
            name: python-service
            port: 8000

# Monitoring with CloudWatch integration
monitoring:
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true

  grafana:
    enabled: true
    adminPassword: "admin"

# Security configurations
security:
  networkPolicy:
    enabled: true

  podSecurityPolicy:
    enabled: true

# Pod Disruption Budgets
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Resource quotas
resourceQuota:
  enabled: true
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
EOF

    log_success "AWS values file created: $values_file"
}

# Deploy to EKS
deploy_to_eks() {
    log_info "Deploying RemoteOps to EKS..."

    # Update kubeconfig
    aws eks update-kubeconfig --region "$REGION" --name "$CLUSTER_NAME"

    # Deploy using Kubernetes script with AWS values
    "$SCRIPT_DIR/deploy-kubernetes.sh" deploy aws

    log_success "RemoteOps deployed to EKS successfully"
}

# Show AWS-specific information
show_aws_info() {
    log_info "AWS EKS Cluster Information:"

    echo -e "\n${BLUE}Cluster Details:${NC}"
    aws eks describe-cluster --name "$CLUSTER_NAME" --region "$REGION" --query 'cluster.{Name:name,Status:status,Version:version,Endpoint:endpoint}' --output table

    echo -e "\n${BLUE}Node Groups:${NC}"
    aws eks describe-nodegroup --cluster-name "$CLUSTER_NAME" --nodegroup-name "$NODE_GROUP_NAME" --region "$REGION" --query 'nodegroup.{Name:nodegroupName,Status:status,InstanceTypes:instanceTypes,DesiredSize:scalingConfig.desiredSize,MinSize:scalingConfig.minSize,MaxSize:scalingConfig.maxSize}' --output table

    echo -e "\n${BLUE}Load Balancer:${NC}"
    kubectl get ingress -n "$NAMESPACE" -o wide

    echo -e "\n${BLUE}Estimated Monthly Cost:${NC}"
    echo "EKS Cluster: ~\$73/month"
    echo "3x t3.medium nodes: ~\$95/month"
    echo "EBS volumes (170GB): ~\$17/month"
    echo "Load Balancer: ~\$23/month"
    echo "Total estimated: ~\$208/month"
}

# Cleanup EKS resources
cleanup() {
    log_warning "This will delete the entire EKS cluster and all resources!"
    read -p "Are you sure? Type 'DELETE' to confirm: " confirm

    if [ "$confirm" = "DELETE" ]; then
        log_info "Deleting EKS cluster..."
        eksctl delete cluster --name "$CLUSTER_NAME" --region "$REGION"
        log_success "EKS cluster deleted"
    else
        log_info "Cleanup cancelled"
    fi
}

# Show help
show_help() {
    echo "RemoteOps AWS EKS Deployment Script"
    echo "==================================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  create-cluster    Create EKS cluster"
    echo "  deploy           Deploy RemoteOps to existing cluster"
    echo "  full-deploy      Create cluster and deploy RemoteOps"
    echo "  status           Show cluster and deployment status"
    echo "  info             Show AWS-specific information"
    echo "  cleanup          Delete EKS cluster"
    echo "  help             Show this help"
    echo ""
    echo "Environment Variables:"
    echo "  CLUSTER_NAME     EKS cluster name (default: remoteops-eks)"
    echo "  REGION           AWS region (default: us-west-2)"
    echo "  NODE_GROUP_NAME  Node group name (default: remoteops-nodes)"
}

# Main execution
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    RemoteOps AWS EKS Deployment${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""

    check_prerequisites

    case "${1:-full-deploy}" in
        "create-cluster")
            create_cluster
            install_alb_controller
            install_cluster_autoscaler
            install_ebs_csi
            ;;
        "deploy")
            create_aws_values
            deploy_to_eks
            ;;
        "full-deploy")
            create_cluster
            install_alb_controller
            install_cluster_autoscaler
            install_ebs_csi
            create_aws_values
            deploy_to_eks
            show_aws_info

            echo ""
            log_success "🎉 RemoteOps deployed successfully to AWS EKS!"
            echo ""
            log_info "Next steps:"
            echo "1. Update the certificate ARN in the ingress annotations"
            echo "2. Configure Route 53 DNS records"
            echo "3. Set up CloudWatch monitoring and alerts"
            echo "4. Configure backup strategies for EBS volumes"
            ;;
        "status")
            "$SCRIPT_DIR/deploy-kubernetes.sh" status
            ;;
        "info")
            show_aws_info
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

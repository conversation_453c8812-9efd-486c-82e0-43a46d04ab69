import React, { useState, useEffect } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { Users, UserPlus, X, Trash2 } from 'lucide-react'
import { api } from '../lib/api'
import { useQuery } from '@tanstack/react-query'
import { useAuth } from '../contexts/AuthContext'

interface User {
  id: string
  name: string
  email: string
  active: boolean
}

interface ServerUser {
  id: string
  userId: string
  serverId: string
  user: {
    id: string
    name: string
    email: string
    active: boolean
  }
  createdAt: string
}

interface ServerUserAccessProps {
  isOpen: boolean
  onClose: () => void
  serverId: string
  serverName: string
}

export default function ServerUserAccess({
  isOpen,
  onClose,
  serverId,
  serverName,
}: ServerUserAccessProps) {
  const { user: currentUser } = useAuth()
  const [selectedUserId, setSelectedUserId] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [serverCreatorId, setServerCreatorId] = useState<string | null>(null)

  // Buscar informações do servidor para identificar o criador
  useEffect(() => {
    if (isOpen && serverId) {
      api.get(`/api/servers/${serverId}`).then(response => {
        setServerCreatorId(response.data.userId)
      }).catch(err => {
        console.error('Erro ao buscar informações do servidor:', err)
      })
    }
  }, [isOpen, serverId])

  // Buscar usuários com acesso ao servidor
  const { data: serverUsers, isLoading: isLoadingServerUsers, refetch: refetchServerUsers } = useQuery<ServerUser[]>({
    queryKey: ['serverUsers', serverId],
    queryFn: async () => {
      try {
        const response = await api.get(`/api/servers/${serverId}/users`)
        console.log('Usuários com acesso ao servidor (resposta completa):', response)
        console.log('Usuários com acesso ao servidor (dados):', response.data)
        return response.data
      } catch (error) {
        console.error('Erro ao buscar usuários com acesso:', error)
        throw error
      }
    },
    enabled: isOpen && !!serverId,
  })

  // Buscar todos os usuários (apenas para administradores)
  const { data: users, isLoading: isLoadingUsers } = useQuery<User[]>({
    queryKey: ['users'],
    queryFn: async () => {
      const response = await api.get('/api/users')
      console.log('Todos os usuários:', response.data)
      return response.data
    },
    enabled: isOpen && currentUser?.role === 'ADMIN',
  })

  // Filtra usuários que já têm acesso e que estão ativos
  const availableUsers = users?.filter(
    (user) => 
      // Não mostrar o próprio usuário atual na lista
      user.id !== currentUser?.id && 
      // Não mostrar usuários que já têm acesso
      !serverUsers?.some((serverUser) => serverUser.userId === user.id) &&
      // Não mostrar usuários inativos
      user.active
  )

  // Filtra apenas usuários ativos com acesso (para exibição)
  const activeServerUsers = serverUsers?.filter(serverUser => serverUser.user.active)
  console.log('Usuários ativos com acesso:', activeServerUsers)

  async function handleAddUserAccess() {
    if (!selectedUserId) {
      setError('Selecione um usuário')
      return
    }

    setIsSubmitting(true)
    setError('')

    try {
      await api.post('/api/server-users', {
        userId: selectedUserId,
        serverId,
      })
      
      setSelectedUserId('')
      refetchServerUsers()
    } catch (err: any) {
      setError(err.response?.data?.error || 'Erro ao adicionar acesso')
    } finally {
      setIsSubmitting(false)
    }
  }

  async function handleRemoveUserAccess(serverUserId: string) {
    if (!confirm('Tem certeza que deseja remover o acesso deste usuário?')) {
      return
    }

    try {
      await api.delete(`/api/server-users/${serverUserId}`)
      refetchServerUsers()
    } catch (err: any) {
      alert(err.response?.data?.error || 'Erro ao remover acesso')
    }
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="div" className="flex justify-between items-center mb-4">
                  <div className="flex items-center gap-2">
                    <Users className="h-6 w-6 text-primary-500" />
                    <h3 className="text-lg font-medium text-gray-900">
                      Gerenciar Acesso - {serverName}
                    </h3>
                  </div>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </Dialog.Title>

                {currentUser?.role === 'ADMIN' && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Adicionar Usuário</h4>
                    <div className="flex gap-2">
                      <select
                        value={selectedUserId}
                        onChange={(e) => setSelectedUserId(e.target.value)}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        disabled={isLoadingUsers || !availableUsers?.length}
                      >
                        <option value="">Selecione um usuário</option>
                        {availableUsers?.map((user) => (
                          <option key={user.id} value={user.id}>
                            {user.name} ({user.email})
                          </option>
                        ))}
                      </select>
                      <button
                        onClick={handleAddUserAccess}
                        disabled={isSubmitting || !selectedUserId}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center gap-1"
                      >
                        <UserPlus className="h-4 w-4" />
                        Adicionar
                      </button>
                    </div>
                    {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
                  </div>
                )}

                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Usuários com Acesso</h4>
                  {!activeServerUsers || activeServerUsers.length === 0 ? (
                    <p className="text-sm text-gray-500">Nenhum usuário tem acesso a este servidor.</p>
                  ) : (
                    <ul className="divide-y divide-gray-200">
                      {activeServerUsers.map((serverUser) => (
                        <li key={serverUser.id} className="py-3 flex justify-between items-center">
                          <div>
                            <p className="text-sm font-medium text-gray-900">{serverUser.user.name}</p>
                            <p className="text-xs text-gray-500">{serverUser.user.email}</p>
                          </div>
                          {currentUser?.role === 'ADMIN' && (
                            <button
                              onClick={() => handleRemoveUserAccess(serverUser.id)}
                              className="text-red-600 hover:text-red-800"
                              title="Remover acesso"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          )}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
} 
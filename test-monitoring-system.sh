#!/bin/bash

# Script para testar o sistema completo de monitoramento

echo "🔍 Testando Sistema de Monitoramento REMOTEOPS"
echo "=================================================="

# Verificar se os serviços estão rodando
echo ""
echo "📋 Verificando status dos serviços..."

# Verificar Backend Node.js
echo "🔍 Testando Backend Node.js..."
if curl -f -s http://localhost:3000/health > /dev/null; then
    echo "✅ Backend Node.js está respondendo"
else
    echo "❌ Backend Node.js não está respondendo"
    exit 1
fi

# Verificar Python SSH Service
echo "🔍 Testando Python SSH Service..."
if curl -f -s http://localhost:8000/health > /dev/null; then
    echo "✅ Python SSH Service está respondendo"
else
    echo "❌ Python SSH Service não está respondendo"
    exit 1
fi

# Verificar Frontend
echo "🔍 Testando Frontend..."
if curl -f -s http://localhost:5173 > /dev/null; then
    echo "✅ Frontend está respondendo"
else
    echo "❌ Frontend não está respondendo"
    exit 1
fi

echo ""
echo "🧪 Testando APIs de Monitoramento..."

# Função para fazer requisições autenticadas
make_auth_request() {
    local endpoint=$1
    local method=${2:-GET}
    local data=${3:-}
    
    # Primeiro, fazer login para obter token (usando credenciais padrão)
    local login_response=$(curl -s -X POST http://localhost:3000/login \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"admin123"}')
    
    local token=$(echo $login_response | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    
    if [ -z "$token" ]; then
        echo "❌ Falha ao obter token de autenticação"
        return 1
    fi
    
    # Fazer a requisição autenticada
    if [ "$method" = "GET" ]; then
        curl -s -H "Authorization: Bearer $token" \
             -H "Content-Type: application/json" \
             "http://localhost:3000/api/monitoring$endpoint"
    else
        curl -s -X "$method" \
             -H "Authorization: Bearer $token" \
             -H "Content-Type: application/json" \
             -d "$data" \
             "http://localhost:3000/api/monitoring$endpoint"
    fi
}

# Testar endpoint de saúde do sistema
echo "🔍 Testando /api/monitoring/health..."
health_response=$(make_auth_request "/health")
if echo "$health_response" | grep -q '"success":true'; then
    echo "✅ Health check funcionando"
    echo "   Status: $(echo $health_response | grep -o '"status":"[^"]*"' | cut -d'"' -f4)"
else
    echo "❌ Health check falhou"
    echo "   Resposta: $health_response"
fi

# Testar métricas de dispositivos
echo "🔍 Testando /api/monitoring/metrics/devices..."
devices_response=$(make_auth_request "/metrics/devices")
if echo "$devices_response" | grep -q '"success":true'; then
    echo "✅ Métricas de dispositivos funcionando"
    device_count=$(echo $devices_response | grep -o '"deviceType"' | wc -l)
    echo "   Tipos de dispositivos encontrados: $device_count"
else
    echo "❌ Métricas de dispositivos falharam"
    echo "   Resposta: $devices_response"
fi

# Testar métricas de serviços
echo "🔍 Testando /api/monitoring/metrics/services..."
services_response=$(make_auth_request "/metrics/services")
if echo "$services_response" | grep -q '"success":true'; then
    echo "✅ Métricas de serviços funcionando"
    service_count=$(echo $services_response | grep -o '"service"' | wc -l)
    echo "   Serviços encontrados: $service_count"
else
    echo "❌ Métricas de serviços falharam"
    echo "   Resposta: $services_response"
fi

# Testar alertas
echo "🔍 Testando /api/monitoring/alerts..."
alerts_response=$(make_auth_request "/alerts")
if echo "$alerts_response" | grep -q '"success":true'; then
    echo "✅ Sistema de alertas funcionando"
    alert_count=$(echo $alerts_response | grep -o '"id"' | wc -l)
    echo "   Alertas ativos: $alert_count"
else
    echo "❌ Sistema de alertas falhou"
    echo "   Resposta: $alerts_response"
fi

# Testar regras de alerta
echo "🔍 Testando /api/monitoring/alerts/rules..."
rules_response=$(make_auth_request "/alerts/rules")
if echo "$rules_response" | grep -q '"success":true'; then
    echo "✅ Regras de alerta funcionando"
    rule_count=$(echo $rules_response | grep -o '"id"' | wc -l)
    echo "   Regras configuradas: $rule_count"
else
    echo "❌ Regras de alerta falharam"
    echo "   Resposta: $rules_response"
fi

# Testar dashboard consolidado
echo "🔍 Testando /api/monitoring/dashboard..."
dashboard_response=$(make_auth_request "/dashboard")
if echo "$dashboard_response" | grep -q '"success":true'; then
    echo "✅ Dashboard consolidado funcionando"
    echo "   Dados incluem: devices, services, health, alerts"
else
    echo "❌ Dashboard consolidado falhou"
    echo "   Resposta: $dashboard_response"
fi

echo ""
echo "🧪 Testando Python SSH Service diretamente..."

# Testar health check do Python
python_health=$(curl -s http://localhost:8000/health)
if echo "$python_health" | grep -q '"status":"healthy"'; then
    echo "✅ Python SSH Service health check OK"
else
    echo "❌ Python SSH Service health check falhou"
    echo "   Resposta: $python_health"
fi

# Testar estatísticas do Python
python_stats=$(curl -s http://localhost:8000/ssh/stats)
if echo "$python_stats" | grep -q '"total_commands"'; then
    echo "✅ Python SSH Service estatísticas OK"
    total_commands=$(echo $python_stats | grep -o '"total_commands":[0-9]*' | cut -d':' -f2)
    echo "   Total de comandos executados: $total_commands"
else
    echo "❌ Python SSH Service estatísticas falharam"
    echo "   Resposta: $python_stats"
fi

echo ""
echo "📊 Testando Frontend de Monitoramento..."

# Verificar se a página de monitoramento está acessível
frontend_monitoring=$(curl -s http://localhost:5173/monitoring)
if echo "$frontend_monitoring" | grep -q "html"; then
    echo "✅ Página de monitoramento acessível"
else
    echo "❌ Página de monitoramento não acessível"
fi

echo ""
echo "🎯 Resumo dos Testes:"
echo "===================="

# Contar sucessos e falhas
success_count=$(grep -c "✅" <<< "$(grep "✅\|❌" /dev/stdout)")
total_tests=10

echo "✅ Testes bem-sucedidos: $success_count/$total_tests"

if [ $success_count -eq $total_tests ]; then
    echo ""
    echo "🎉 TODOS OS TESTES PASSARAM!"
    echo "   O sistema de monitoramento está funcionando perfeitamente."
    echo ""
    echo "📍 Acesse o dashboard em: http://localhost:5173/monitoring"
    echo "📚 Documentação da API Python: http://localhost:8000/docs"
    echo "🔍 Health checks:"
    echo "   - Backend: http://localhost:3000/health"
    echo "   - Python: http://localhost:8000/health"
    echo ""
    echo "🚀 Sistema pronto para produção!"
else
    echo ""
    echo "⚠️  Alguns testes falharam. Verifique os logs acima."
    echo "   Certifique-se de que todos os serviços estão rodando:"
    echo "   - docker-compose up -d"
    echo "   - Aguarde alguns minutos para inicialização completa"
fi

echo ""
echo "💡 Dicas para uso:"
echo "=================="
echo "1. Acesse /monitoring como administrador para ver o dashboard"
echo "2. O sistema coleta métricas automaticamente durante o uso"
echo "3. Alertas são verificados a cada 2 minutos"
echo "4. Use /api/monitoring/dashboard para dados consolidados"
echo "5. Configure webhooks nas regras de alerta para notificações externas"

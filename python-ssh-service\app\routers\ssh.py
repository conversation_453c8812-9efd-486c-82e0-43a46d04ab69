from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
import logging
import async<PERSON>
from typing import List
from ..models import SSHCommandRequest, CommandResult, ServiceStats
from ..services.ssh_service import SSHService
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/ssh",
    tags=["ssh"],
    responses={404: {"description": "Not found"}},
)

# Instância global do serviço SSH
ssh_service = SSHService()

@router.post("/execute", response_model=CommandResult)
async def execute_command(request: SSHCommandRequest):
    """
    Executa um comando SSH em um servidor remoto
    
    Este endpoint é otimizado para dispositivos de rede problemáticos,
    especialmente Huawei/HarmonyOS, Nokia e outros equipamentos que
    apresentam dificuldades com a implementação Node.js.
    """
    try:
        logger.info(f"Recebida solicitação para executar comando em {request.host}")
        
        # Validar se temos credenciais
        if not request.password and not request.private_key:
            raise HTTPException(
                status_code=400, 
                detail="Nenhum método de autenticação fornecido (password ou private_key)"
            )
        
        # Executar o comando
        result = await ssh_service.connect_and_execute(request)
        
        # Se houve erro, retornar como erro HTTP
        if result.code != 0:
            logger.warning(f"Comando falhou em {request.host}: {result.stderr}")
            raise HTTPException(
                status_code=500, 
                detail={
                    "message": "Falha na execução do comando",
                    "error": result.stderr,
                    "execution_time": result.execution_time
                }
            )
        
        logger.info(f"Comando executado com sucesso em {request.host}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro inesperado ao processar solicitação: {str(e)}")
        raise HTTPException(
            status_code=500, 
            detail=f"Erro interno do servidor: {str(e)}"
        )

@router.post("/execute-batch", response_model=List[CommandResult])
async def execute_batch_commands(requests: List[SSHCommandRequest]):
    """
    Executa múltiplos comandos SSH em paralelo (limitado)
    
    Útil para executar o mesmo comando em múltiplos servidores
    ou múltiplos comandos no mesmo servidor.
    """
    if len(requests) > settings.max_concurrent_connections:
        raise HTTPException(
            status_code=400,
            detail=f"Máximo de {settings.max_concurrent_connections} comandos simultâneos permitidos"
        )
    
    try:
        logger.info(f"Executando {len(requests)} comandos em lote")
        
        # Executar comandos em paralelo com semáforo para limitar concorrência
        semaphore = asyncio.Semaphore(settings.max_concurrent_connections)
        
        async def execute_with_semaphore(request):
            async with semaphore:
                return await ssh_service.connect_and_execute(request)
        
        # Executar todos os comandos
        results = await asyncio.gather(
            *[execute_with_semaphore(req) for req in requests],
            return_exceptions=True
        )
        
        # Processar resultados e exceções
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Erro no comando {i}: {str(result)}")
                processed_results.append(CommandResult(
                    stderr=f"Erro na execução: {str(result)}",
                    code=1
                ))
            else:
                processed_results.append(result)
        
        logger.info(f"Lote de {len(requests)} comandos processado")
        return processed_results
        
    except Exception as e:
        logger.error(f"Erro ao processar lote de comandos: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao processar lote: {str(e)}"
        )

@router.get("/stats", response_model=ServiceStats)
async def get_service_stats():
    """
    Retorna estatísticas do serviço SSH
    """
    try:
        stats = ssh_service.get_stats()
        return ServiceStats(
            total_commands=stats['total_commands'],
            successful_commands=stats['successful_commands'],
            failed_commands=stats['failed_commands'],
            average_execution_time=stats['average_execution_time'],
            active_connections=stats['active_connections'],
            uptime="N/A"  # TODO: Implementar cálculo de uptime
        )
    except Exception as e:
        logger.error(f"Erro ao obter estatísticas: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao obter estatísticas: {str(e)}"
        )

@router.post("/test-connection")
async def test_connection(request: SSHCommandRequest):
    """
    Testa a conectividade SSH sem executar comandos
    """
    try:
        # Criar uma cópia da requisição com comando simples
        test_request = request.copy()
        
        # Usar comando apropriado para cada tipo de dispositivo
        test_commands = {
            "huawei": "display version | include Version",
            "huawei_vrp": "display version | include Version", 
            "nokia": "show version | match Version",
            "mikrotik": "/system identity print",
            "cisco": "show version | include Version",
            "linux": "echo 'Connection test successful'",
            "windows": "echo Connection test successful",
            "generic": "echo 'Connection test successful'"
        }
        
        test_request.command = test_commands.get(
            request.device_type.value, 
            "echo 'Connection test successful'"
        )
        test_request.timeout = min(30, request.timeout)  # Timeout menor para teste
        
        result = await ssh_service.connect_and_execute(test_request)
        
        if result.code == 0:
            return {
                "status": "success",
                "message": "Conexão SSH estabelecida com sucesso",
                "device_info": result.device_info,
                "execution_time": result.execution_time
            }
        else:
            return {
                "status": "failed",
                "message": "Falha na conexão SSH",
                "error": result.stderr,
                "execution_time": result.execution_time
            }
            
    except Exception as e:
        logger.error(f"Erro ao testar conexão: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erro ao testar conexão: {str(e)}"
        )

@router.get("/health")
async def health_check():
    """
    Health check do serviço
    """
    return {
        "status": "healthy",
        "service": "Python SSH Service",
        "version": "1.0.0",
        "timestamp": "2024-12-19T10:00:00Z"
    }

import Redis from 'ioredis';
import { Logger } from '../../utils/logger';
import { CommandResult } from '../../types/server';

interface CacheConfig {
  defaultTTL: number; // Time to live em segundos
  maxMemory: string;
  evictionPolicy: string;
}

interface CacheEntry {
  result: CommandResult;
  timestamp: number;
  serverId: string;
  command: string;
  hits: number;
}

interface CacheStats {
  totalKeys: number;
  memoryUsage: number;
  hitRate: number;
  totalHits: number;
  totalMisses: number;
  evictions: number;
}

/**
 * Serviço de cache inteligente para comandos SSH
 * Implementa estratégias de cache baseadas em padrões de uso
 */
export class CacheService {
  private redis: Redis | null = null;
  private enabled: boolean = false;
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    evictions: 0
  };

  private config: CacheConfig = {
    defaultTTL: 300, // 5 minutos
    maxMemory: '100mb',
    evictionPolicy: 'allkeys-lru'
  };

  // Comandos que são seguros para cache (somente leitura)
  private cacheableCommands = [
    'display version',
    'show version',
    'display current-configuration',
    'show running-config',
    'display interface',
    'show interface',
    'display ip routing-table',
    'show ip route',
    'display arp',
    'show arp',
    'display mac-address',
    'show mac address-table',
    'display device',
    'show inventory',
    '/system identity print',
    '/interface print',
    '/ip address print',
    '/ip route print'
  ];

  constructor() {
    this.initializeRedis();
  }

  /**
   * Inicializa conexão com Redis
   */
  private async initializeRedis(): Promise<void> {
    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

      this.redis = new Redis(redisUrl, {
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keepAlive: 30000,
        family: 4,
        keyPrefix: 'ssh_cache:',
      });

      await this.redis.connect();

      // Configurar políticas de cache
      await this.redis.config('SET', 'maxmemory', this.config.maxMemory);
      await this.redis.config('SET', 'maxmemory-policy', this.config.evictionPolicy);

      this.enabled = true;
      Logger.log('CacheService inicializado com Redis');

      // Monitorar eventos do Redis
      this.redis.on('error', (error) => {
        Logger.error('Erro no Redis:', error);
        this.enabled = false;
      });

      this.redis.on('connect', () => {
        Logger.log('Conectado ao Redis');
        this.enabled = true;
      });

      this.redis.on('close', () => {
        Logger.warn('Conexão com Redis fechada');
        this.enabled = false;
      });

    } catch (error) {
      Logger.error('Falha ao inicializar Redis:', error);
      this.enabled = false;
    }
  }

  /**
   * Verifica se um comando é cacheável
   */
  private isCacheableCommand(command: string): boolean {
    const normalizedCommand = command.toLowerCase().trim();

    // Verificar se é um comando de leitura conhecido
    const isReadCommand = this.cacheableCommands.some(cacheableCmd =>
      normalizedCommand.includes(cacheableCmd.toLowerCase())
    );

    // Não cachear comandos que modificam configuração
    const isWriteCommand = /\b(config|configure|set|add|delete|remove|create|modify|change|update|write|save|commit)\b/i.test(normalizedCommand);

    return isReadCommand && !isWriteCommand;
  }

  /**
   * Gera chave de cache baseada no servidor e comando
   */
  private generateCacheKey(serverId: string, command: string): string {
    const normalizedCommand = command.toLowerCase().trim().replace(/\s+/g, ' ');
    const hash = this.simpleHash(normalizedCommand);
    return `cmd:${serverId}:${hash}`;
  }

  /**
   * Hash simples para comandos
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Determina TTL baseado no tipo de comando
   */
  private determineTTL(command: string): number {
    const normalizedCommand = command.toLowerCase();

    // Comandos de configuração: cache mais longo (30 minutos)
    if (normalizedCommand.includes('configuration') || normalizedCommand.includes('running-config')) {
      return 1800;
    }

    // Comandos de versão/sistema: cache longo (1 hora)
    if (normalizedCommand.includes('version') || normalizedCommand.includes('identity')) {
      return 3600;
    }

    // Comandos de interface/rede: cache médio (10 minutos)
    if (normalizedCommand.includes('interface') || normalizedCommand.includes('route') || normalizedCommand.includes('address')) {
      return 600;
    }

    // Comandos dinâmicos (ARP, MAC): cache curto (2 minutos)
    if (normalizedCommand.includes('arp') || normalizedCommand.includes('mac')) {
      return 120;
    }

    // Default: 5 minutos
    return this.config.defaultTTL;
  }

  /**
   * Obtém resultado do cache
   */
  async get(serverId: string, command: string): Promise<CommandResult | null> {
    if (!this.enabled || !this.redis || !this.isCacheableCommand(command)) {
      return null;
    }

    try {
      const key = this.generateCacheKey(serverId, command);
      const cached = await this.redis.get(key);

      if (cached) {
        const entry: CacheEntry = JSON.parse(cached);

        // Incrementar contador de hits
        entry.hits++;
        await this.redis.setex(key, this.determineTTL(command), JSON.stringify(entry));

        this.stats.hits++;
        Logger.log(`Cache HIT para comando: ${command.substring(0, 50)}...`);

        return entry.result;
      } else {
        this.stats.misses++;
        return null;
      }
    } catch (error) {
      Logger.error('Erro ao buscar no cache:', error);
      return null;
    }
  }

  /**
   * Armazena resultado no cache
   */
  async set(serverId: string, command: string, result: CommandResult): Promise<void> {
    if (!this.enabled || !this.redis || !this.isCacheableCommand(command)) {
      return;
    }

    // Não cachear resultados com erro
    if (result.code !== 0 || result.stderr) {
      return;
    }

    try {
      const key = this.generateCacheKey(serverId, command);
      const ttl = this.determineTTL(command);

      const entry: CacheEntry = {
        result,
        timestamp: Date.now(),
        serverId,
        command: command.substring(0, 200), // Limitar tamanho
        hits: 0
      };

      await this.redis.setex(key, ttl, JSON.stringify(entry));
      this.stats.sets++;

      Logger.log(`Cache SET para comando: ${command.substring(0, 50)}... (TTL: ${ttl}s)`);
    } catch (error) {
      Logger.error('Erro ao armazenar no cache:', error);
    }
  }

  /**
   * Invalida cache para um servidor específico
   */
  async invalidateServer(serverId: string): Promise<void> {
    if (!this.enabled || !this.redis) {
      return;
    }

    try {
      const pattern = `cmd:${serverId}:*`;
      const keys = await this.redis.keys(pattern);

      if (keys.length > 0) {
        await this.redis.del(...keys);
        Logger.log(`Cache invalidado para servidor ${serverId}: ${keys.length} chaves removidas`);
      }
    } catch (error) {
      Logger.error('Erro ao invalidar cache do servidor:', error);
    }
  }

  /**
   * Invalida cache por padrão de comando
   */
  async invalidateByPattern(pattern: string): Promise<void> {
    if (!this.enabled || !this.redis) {
      return;
    }

    try {
      const keys = await this.redis.keys(`*${pattern}*`);

      if (keys.length > 0) {
        await this.redis.del(...keys);
        Logger.log(`Cache invalidado por padrão "${pattern}": ${keys.length} chaves removidas`);
      }
    } catch (error) {
      Logger.error('Erro ao invalidar cache por padrão:', error);
    }
  }

  /**
   * Limpa todo o cache
   */
  async clear(): Promise<void> {
    if (!this.enabled || !this.redis) {
      return;
    }

    try {
      await this.redis.flushdb();
      Logger.log('Cache completamente limpo');
    } catch (error) {
      Logger.error('Erro ao limpar cache:', error);
    }
  }

  /**
   * Obtém estatísticas do cache
   */
  async getStats(): Promise<CacheStats> {
    if (!this.enabled || !this.redis) {
      return {
        totalKeys: 0,
        memoryUsage: 0,
        hitRate: 0,
        totalHits: this.stats.hits,
        totalMisses: this.stats.misses,
        evictions: this.stats.evictions
      };
    }

    try {
      const info = await this.redis.info('memory');
      const keyspace = await this.redis.info('keyspace');

      // Extrair informações do Redis
      const memoryUsage = this.extractInfoValue(info, 'used_memory');
      const totalKeys = this.extractKeyspaceValue(keyspace, 'keys');

      const totalRequests = this.stats.hits + this.stats.misses;
      const hitRate = totalRequests > 0 ? this.stats.hits / totalRequests : 0;

      return {
        totalKeys,
        memoryUsage: parseInt(memoryUsage) || 0,
        hitRate,
        totalHits: this.stats.hits,
        totalMisses: this.stats.misses,
        evictions: this.stats.evictions
      };
    } catch (error) {
      Logger.error('Erro ao obter estatísticas do cache:', error);
      return {
        totalKeys: 0,
        memoryUsage: 0,
        hitRate: 0,
        totalHits: this.stats.hits,
        totalMisses: this.stats.misses,
        evictions: this.stats.evictions
      };
    }
  }

  /**
   * Obtém comandos mais populares do cache
   */
  async getPopularCommands(limit: number = 10): Promise<Array<{command: string, hits: number}>> {
    if (!this.enabled || !this.redis) {
      return [];
    }

    try {
      const keys = await this.redis.keys('cmd:*');
      const commands: Array<{command: string, hits: number}> = [];

      for (const key of keys) {
        const cached = await this.redis.get(key);
        if (cached) {
          const entry: CacheEntry = JSON.parse(cached);
          commands.push({
            command: entry.command,
            hits: entry.hits
          });
        }
      }

      return commands
        .sort((a, b) => b.hits - a.hits)
        .slice(0, limit);
    } catch (error) {
      Logger.error('Erro ao obter comandos populares:', error);
      return [];
    }
  }

  /**
   * Verifica se o cache está habilitado e funcionando
   */
  isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * Fecha conexão com Redis
   */
  async disconnect(): Promise<void> {
    if (this.redis) {
      await this.redis.quit();
      this.enabled = false;
      Logger.log('Conexão com Redis fechada');
    }
  }

  /**
   * Invalida cache por padrão de comando
   */
  async invalidatePattern(pattern: string): Promise<void> {
    if (!this.enabled || !this.redis) {
      return;
    }

    try {
      const keys = await this.redis.keys(`*${pattern}*`);
      if (keys.length > 0) {
        await this.redis.del(...keys);
        Logger.log(`Cache invalidado para padrão: ${pattern} (${keys.length} chaves)`);
      }
    } catch (error) {
      Logger.error('Erro ao invalidar cache por padrão:', error);
    }
  }

  /**
   * Obtém configuração atual do cache
   */
  async getConfig(): Promise<any> {
    return {
      ...this.config,
      enabled: this.enabled
    };
  }

  /**
   * Atualiza configuração do cache
   */
  async updateConfig(newConfig: any): Promise<void> {
    if (!this.redis) return;

    try {
      if (newConfig.defaultTTL !== undefined) {
        this.config.defaultTTL = newConfig.defaultTTL;
      }
      if (newConfig.maxMemory !== undefined) {
        this.config.maxMemory = newConfig.maxMemory;
        await this.redis.config('SET', 'maxmemory', newConfig.maxMemory);
      }
      if (newConfig.evictionPolicy !== undefined) {
        this.config.evictionPolicy = newConfig.evictionPolicy;
        await this.redis.config('SET', 'maxmemory-policy', newConfig.evictionPolicy);
      }
      if (newConfig.enabled !== undefined) {
        this.enabled = newConfig.enabled;
      }

      Logger.log('Configuração do cache atualizada');
    } catch (error) {
      Logger.error('Erro ao atualizar configuração do cache:', error);
      throw error;
    }
  }

  /**
   * Obtém entradas do cache com paginação
   */
  async getCacheEntries(page: number = 1, limit: number = 50): Promise<any> {
    if (!this.enabled || !this.redis) {
      return { entries: [], total: 0, page, totalPages: 0 };
    }

    try {
      const keys = await this.redis.keys('cmd:*');
      const total = keys.length;
      const totalPages = Math.ceil(total / limit);
      const start = (page - 1) * limit;
      const end = start + limit;
      const pageKeys = keys.slice(start, end);

      const entries = [];
      for (const key of pageKeys) {
        const cached = await this.redis.get(key);
        const ttl = await this.redis.ttl(key);
        if (cached) {
          const entry: CacheEntry = JSON.parse(cached);
          entries.push({
            key,
            command: entry.command,
            serverId: entry.serverId,
            hits: entry.hits,
            timestamp: entry.timestamp,
            ttl,
            size: Buffer.byteLength(cached, 'utf8')
          });
        }
      }

      return { entries, total, page, totalPages };
    } catch (error) {
      Logger.error('Erro ao obter entradas do cache:', error);
      return { entries: [], total: 0, page, totalPages: 0 };
    }
  }

  /**
   * Remove uma entrada específica do cache
   */
  async removeEntry(key: string): Promise<void> {
    if (!this.enabled || !this.redis) {
      return;
    }

    try {
      await this.redis.del(key);
      Logger.log(`Entrada removida do cache: ${key}`);
    } catch (error) {
      Logger.error('Erro ao remover entrada do cache:', error);
      throw error;
    }
  }

  /**
   * Obtém métricas detalhadas do cache
   */
  async getMetrics(timeRange: string = '24h'): Promise<any> {
    const stats = await this.getStats();
    const popularCommands = await this.getPopularCommands(10);

    // Simular distribuição por servidor (em um cenário real, isso viria do Redis)
    const serverDistribution = [
      { serverId: 'server-1', serverName: 'Servidor Principal', keyCount: Math.floor(stats.totalKeys * 0.4) },
      { serverId: 'server-2', serverName: 'Servidor Backup', keyCount: Math.floor(stats.totalKeys * 0.3) },
      { serverId: 'server-3', serverName: 'Servidor Teste', keyCount: Math.floor(stats.totalKeys * 0.3) }
    ];

    return {
      hitRate: stats.hitRate,
      missRate: 1 - stats.hitRate,
      evictionRate: stats.evictions / (stats.totalHits + stats.totalMisses || 1),
      memoryUsage: stats.memoryUsage,
      keyCount: stats.totalKeys,
      averageResponseTime: 50 + Math.random() * 100, // Simular tempo de resposta
      topCommands: popularCommands,
      serverDistribution
    };
  }

  /**
   * Força refresh do cache para um servidor
   */
  async refreshServer(serverId: string): Promise<void> {
    await this.invalidateServer(serverId);
    Logger.log(`Cache atualizado para servidor: ${serverId}`);
  }

  /**
   * Obtém informações de saúde do Redis
   */
  async getRedisHealth(): Promise<any> {
    if (!this.enabled || !this.redis) {
      return {
        connected: false,
        uptime: 0,
        version: 'N/A',
        memoryUsage: 0,
        maxMemory: 0,
        keyspaceHits: 0,
        keyspaceMisses: 0,
        connectedClients: 0
      };
    }

    try {
      const info = await this.redis.info();
      const serverInfo = await this.redis.info('server');
      const memoryInfo = await this.redis.info('memory');
      const statsInfo = await this.redis.info('stats');
      const clientsInfo = await this.redis.info('clients');

      return {
        connected: true,
        uptime: parseInt(this.extractInfoValue(serverInfo, 'uptime_in_seconds')),
        version: this.extractInfoValue(serverInfo, 'redis_version'),
        memoryUsage: parseInt(this.extractInfoValue(memoryInfo, 'used_memory')),
        maxMemory: parseInt(this.extractInfoValue(memoryInfo, 'maxmemory')),
        keyspaceHits: parseInt(this.extractInfoValue(statsInfo, 'keyspace_hits')),
        keyspaceMisses: parseInt(this.extractInfoValue(statsInfo, 'keyspace_misses')),
        connectedClients: parseInt(this.extractInfoValue(clientsInfo, 'connected_clients'))
      };
    } catch (error) {
      Logger.error('Erro ao obter saúde do Redis:', error);
      return {
        connected: false,
        uptime: 0,
        version: 'N/A',
        memoryUsage: 0,
        maxMemory: 0,
        keyspaceHits: 0,
        keyspaceMisses: 0,
        connectedClients: 0
      };
    }
  }

  /**
   * Executa comando Redis personalizado (apenas para admins)
   */
  async executeRedisCommand(command: string): Promise<any> {
    if (!this.enabled || !this.redis) {
      throw new Error('Redis não está disponível');
    }

    try {
      const parts = command.split(' ');
      const cmd = parts[0].toLowerCase();
      const args = parts.slice(1);

      // Comandos permitidos para segurança
      const allowedCommands = ['info', 'keys', 'get', 'ttl', 'exists', 'type', 'memory'];
      if (!allowedCommands.includes(cmd)) {
        throw new Error(`Comando não permitido: ${cmd}`);
      }

      const result = await (this.redis as any)[cmd](...args);
      return result;
    } catch (error) {
      Logger.error('Erro ao executar comando Redis:', error);
      throw error;
    }
  }

  /**
   * Obtém estatísticas de performance por servidor
   */
  async getServerPerformance(): Promise<any[]> {
    // Simular dados de performance por servidor
    // Em um cenário real, isso viria de métricas coletadas
    return [
      {
        serverId: 'server-1',
        serverName: 'Servidor Principal',
        cacheHitRate: 0.85,
        averageResponseTime: 45,
        totalCommands: 1250,
        cachedCommands: 1062
      },
      {
        serverId: 'server-2',
        serverName: 'Servidor Backup',
        cacheHitRate: 0.72,
        averageResponseTime: 68,
        totalCommands: 890,
        cachedCommands: 641
      },
      {
        serverId: 'server-3',
        serverName: 'Servidor Teste',
        cacheHitRate: 0.91,
        averageResponseTime: 32,
        totalCommands: 456,
        cachedCommands: 415
      }
    ];
  }

  /**
   * Exporta configurações do cache
   */
  async exportConfig(): Promise<any> {
    const config = await this.getConfig();
    const stats = await this.getStats();

    return {
      config,
      stats,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };
  }

  /**
   * Importa configurações do cache
   */
  async importConfig(configData: any): Promise<void> {
    if (!configData.config) {
      throw new Error('Dados de configuração inválidos');
    }

    await this.updateConfig(configData.config);
    Logger.log('Configuração importada com sucesso');
  }

  /**
   * Obtém recomendações de otimização do cache
   */
  async getOptimizationRecommendations(): Promise<any[]> {
    const stats = await this.getStats();
    const health = await this.getRedisHealth();
    const recommendations = [];

    // Verificar hit rate
    if (stats.hitRate < 0.7) {
      recommendations.push({
        type: 'warning',
        title: 'Hit Rate Baixo',
        description: `O hit rate atual é de ${(stats.hitRate * 100).toFixed(1)}%. Considere aumentar o TTL dos comandos mais utilizados.`,
        action: 'Ajustar TTL',
        impact: 'high'
      });
    }

    // Verificar uso de memória
    if (health.maxMemory > 0 && health.memoryUsage / health.maxMemory > 0.8) {
      recommendations.push({
        type: 'warning',
        title: 'Uso de Memória Alto',
        description: 'O uso de memória está acima de 80%. Considere aumentar o limite ou ajustar a política de eviction.',
        action: 'Aumentar memória',
        impact: 'high'
      });
    }

    // Verificar evictions
    if (stats.evictions > 100) {
      recommendations.push({
        type: 'info',
        title: 'Muitas Evictions',
        description: `${stats.evictions} evictions detectadas. Isso pode indicar que o cache está muito pequeno.`,
        action: 'Aumentar capacidade',
        impact: 'medium'
      });
    }

    // Recomendação positiva se tudo estiver bem
    if (recommendations.length === 0) {
      recommendations.push({
        type: 'success',
        title: 'Cache Otimizado',
        description: 'O cache está funcionando de forma otimizada. Todas as métricas estão dentro dos parâmetros ideais.',
        impact: 'low'
      });
    }

    return recommendations;
  }

  /**
   * Aplica otimizações automáticas
   */
  async applyOptimizations(): Promise<any> {
    const recommendations = await this.getOptimizationRecommendations();
    let applied = 0;
    let skipped = 0;
    const errors: string[] = [];

    for (const rec of recommendations) {
      try {
        if (rec.action === 'Ajustar TTL') {
          // Aumentar TTL padrão
          await this.updateConfig({ defaultTTL: this.config.defaultTTL * 1.5 });
          applied++;
        } else if (rec.action === 'Aumentar memória') {
          // Aumentar limite de memória em 20%
          const currentMemory = parseInt(this.config.maxMemory.replace(/\D/g, ''));
          const newMemory = Math.floor(currentMemory * 1.2);
          await this.updateConfig({ maxMemory: `${newMemory}mb` });
          applied++;
        } else {
          skipped++;
        }
      } catch (error) {
        errors.push(`Erro ao aplicar ${rec.action}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
        skipped++;
      }
    }

    return { applied, skipped, errors };
  }

  // Métodos auxiliares privados
  private extractInfoValue(info: string, key: string): string {
    const match = info.match(new RegExp(`${key}:([^\\r\\n]+)`));
    return match ? match[1] : '0';
  }

  private extractKeyspaceValue(keyspace: string, key: string): number {
    const match = keyspace.match(new RegExp(`${key}=(\\d+)`));
    return match ? parseInt(match[1]) : 0;
  }
}

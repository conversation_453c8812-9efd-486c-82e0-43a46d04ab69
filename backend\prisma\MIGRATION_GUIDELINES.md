# Diretrizes para Migrações do Prisma

Este documento contém boas práticas para criar e gerenciar migrações do Prisma, especialmente quando trabalhando com ambientes diferentes (desenvolvimento, staging, produção).

## Problemas Comuns

1. **Diferenças entre ambientes**: Os ambientes de desenvolvimento e produção podem ter estados diferentes do banco de dados.
2. **Falhas em operações não idempotentes**: Operações como `DROP INDEX` falham se o objeto não existir.
3. **Perda de dados**: Migrações mal planejadas podem resultar em perda de dados.

## Boas Práticas

### Antes de Criar Migrações

1. **Sincronize os ambientes**: Certifique-se de que seu ambiente de desenvolvimento reflete o estado atual da produção.
   ```bash
   # Exporte o schema da produção
   pg_dump -s -h host_producao -U usuario -d banco > schema_producao.sql
   
   # Compare com o schema local
   diff schema_producao.sql schema_local.sql
   ```

2. **Planeje cuidadosamente**: Documente as alterações que você pretende fazer e seus impactos.

### Ao Criar Migrações

1. **Use operações condicionais**: Utilize os exemplos em `utils/safe-migration.sql` para tornar suas migrações mais robustas.

2. **Divida migrações complexas**: Em vez de uma grande migração, crie várias pequenas.

3. **Adicione comentários**: Documente o propósito de cada migração.

4. **Teste em staging**: Sempre teste suas migrações em um ambiente de staging antes de aplicá-las em produção.

### Ao Aplicar Migrações

1. **Faça backup**: Sempre faça backup do banco de dados antes de aplicar migrações.

2. **Aplique em horários de baixo tráfego**: Migrações podem bloquear tabelas.

3. **Tenha um plano de rollback**: Saiba como reverter as alterações se algo der errado.

## Comandos Úteis do Prisma

```bash
# Criar uma nova migração
npx prisma migrate dev --name nome_da_migracao

# Aplicar migrações pendentes
npx prisma migrate deploy

# Verificar status das migrações
npx prisma migrate status

# Resolver problemas de migração
npx prisma migrate resolve --applied "nome_da_migracao"
npx prisma migrate resolve --rolled-back "nome_da_migracao"
```

## Recursos Adicionais

- [Documentação oficial do Prisma sobre migrações](https://www.prisma.io/docs/concepts/components/prisma-migrate)
- [Estratégias para migrações em produção](https://www.prisma.io/docs/guides/deployment/deploy-database-changes-with-prisma-migrate)
- [Resolução de problemas comuns](https://www.prisma.io/docs/concepts/components/prisma-migrate/migration-issues)

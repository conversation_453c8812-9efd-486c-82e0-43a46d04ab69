#!/bin/bash

# Script para deploy completo do sistema REMOTEOPS com microserviço Python

echo "🚀 Deploy do Sistema REMOTEOPS com Microserviço Python"
echo "============================================================"

# Verificar se o Docker está instalado
if ! command -v docker &> /dev/null; then
    echo "❌ Docker não encontrado. Por favor, instale o Docker primeiro."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose não encontrado. Por favor, instale o Docker Compose primeiro."
    exit 1
fi

# Verificar se estamos no diretório correto
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Arquivo docker-compose.yml não encontrado. Execute este script na raiz do projeto."
    exit 1
fi

# Função para verificar se um serviço está saudável
check_service_health() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1
    
    echo "🔍 Verificando saúde do serviço $service_name..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            echo "✅ $service_name está saudável!"
            return 0
        fi
        
        echo "⏳ Tentativa $attempt/$max_attempts - Aguardando $service_name..."
        sleep 5
        ((attempt++))
    done
    
    echo "❌ $service_name não respondeu após $max_attempts tentativas"
    return 1
}

# Parar containers existentes
echo "🛑 Parando containers existentes..."
docker-compose down

# Limpar volumes órfãos (opcional)
if [ "$1" = "--clean" ]; then
    echo "🧹 Limpando volumes órfãos..."
    docker system prune -f
    docker volume prune -f
fi

# Construir e iniciar todos os serviços
echo "🔨 Construindo e iniciando serviços..."
docker-compose up -d --build

# Verificar se os containers estão rodando
echo "📋 Verificando status dos containers..."
docker-compose ps

# Aguardar e verificar saúde dos serviços
echo ""
echo "🏥 Verificando saúde dos serviços..."

# Verificar PostgreSQL
echo "🔍 Aguardando PostgreSQL..."
sleep 10

# Verificar Redis
echo "🔍 Aguardando Redis..."
sleep 5

# Verificar Python SSH Service
if ! check_service_health "Python SSH Service" "http://localhost:8000/health"; then
    echo "❌ Falha na verificação do Python SSH Service"
    echo "📋 Logs do Python SSH Service:"
    docker-compose logs python-ssh
    exit 1
fi

# Verificar Backend Node.js
if ! check_service_health "Backend Node.js" "http://localhost:3000/health"; then
    echo "❌ Falha na verificação do Backend Node.js"
    echo "📋 Logs do Backend:"
    docker-compose logs backend
    exit 1
fi

# Verificar Frontend
if ! check_service_health "Frontend" "http://localhost:5173"; then
    echo "❌ Falha na verificação do Frontend"
    echo "📋 Logs do Frontend:"
    docker-compose logs frontend
    exit 1
fi

echo ""
echo "🎉 Deploy concluído com sucesso!"
echo "================================"
echo ""
echo "📍 Serviços disponíveis:"
echo "  🌐 Frontend:              http://localhost:5173"
echo "  🔧 Backend API:           http://localhost:3000"
echo "  🐍 Python SSH Service:    http://localhost:8000"
echo "  📚 Python API Docs:       http://localhost:8000/docs"
echo "  🗄️  PostgreSQL:            localhost:5432"
echo "  🔴 Redis:                 localhost:6379"
echo ""
echo "🔍 Comandos úteis:"
echo "  📋 Ver logs:              docker-compose logs -f [serviço]"
echo "  🔄 Reiniciar serviço:     docker-compose restart [serviço]"
echo "  🛑 Parar tudo:            docker-compose down"
echo "  📊 Status:                docker-compose ps"
echo ""
echo "🧪 Testar integração Python:"
echo "  curl -X POST http://localhost:8000/ssh/test-connection \\"
echo "    -H 'Content-Type: application/json' \\"
echo "    -d '{\"host\":\"exemplo.com\",\"username\":\"test\",\"password\":\"test\",\"device_type\":\"linux\"}'"
echo ""
echo "📝 Para ver este guia novamente: ./deploy-with-python.sh --help"

# Se foi passado --help, mostrar informações adicionais
if [ "$1" = "--help" ]; then
    echo ""
    echo "📖 Informações Adicionais:"
    echo "=========================="
    echo ""
    echo "🐍 Microserviço Python:"
    echo "  - Resolve problemas com dispositivos HarmonyOS/Huawei"
    echo "  - Fallback automático para Node.js em caso de falha"
    echo "  - Suporte especializado para Nokia, DMOS e outros"
    echo "  - API completa com documentação automática"
    echo ""
    echo "🔄 Roteamento Inteligente:"
    echo "  - Dispositivos HUAWEI → Python Service"
    echo "  - Dispositivos NOKIA → Python Service"
    echo "  - Dispositivos DMOS → Python Service"
    echo "  - Outros dispositivos → Node.js (com fallback para Python)"
    echo ""
    echo "🛠️ Troubleshooting:"
    echo "  - Logs detalhados: docker-compose logs -f python-ssh"
    echo "  - Health check: curl http://localhost:8000/health"
    echo "  - Estatísticas: curl http://localhost:8000/ssh/stats"
    echo ""
    echo "🔧 Configurações:"
    echo "  - Variáveis de ambiente no docker-compose.yml"
    echo "  - Configurações Python em python-ssh-service/.env"
    echo "  - Timeouts otimizados por tipo de dispositivo"
fi

import React from 'react';
import {
  <PERSON>con<PERSON><PERSON>on,
  <PERSON>ltip,
  <PERSON>u,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Box,
  Typography
} from '@mui/material';
import {
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  SettingsBrightness as AutoModeIcon,
  Check as CheckIcon
} from '@mui/icons-material';
import { useTheme, useSystemTheme } from '../contexts/ThemeContext';

interface ThemeToggleProps {
  variant?: 'icon' | 'menu';
  showLabel?: boolean;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  variant = 'icon', 
  showLabel = false 
}) => {
  const { mode, toggleTheme, setTheme } = useTheme();
  const systemTheme = useSystemTheme();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [themeMode, setThemeMode] = React.useState<'light' | 'dark' | 'system'>(() => {
    const savedMode = localStorage.getItem('themeMode');
    return (savedMode as 'light' | 'dark' | 'system') || 'system';
  });

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (variant === 'icon') {
      toggleTheme();
    } else {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleThemeChange = (newMode: 'light' | 'dark' | 'system') => {
    setThemeMode(newMode);
    localStorage.setItem('themeMode', newMode);
    
    if (newMode === 'system') {
      setTheme(systemTheme);
    } else {
      setTheme(newMode);
    }
    
    handleClose();
  };

  // Atualizar tema quando a preferência do sistema mudar e o modo for 'system'
  React.useEffect(() => {
    if (themeMode === 'system') {
      setTheme(systemTheme);
    }
  }, [systemTheme, themeMode, setTheme]);

  const getIcon = () => {
    if (themeMode === 'system') {
      return <AutoModeIcon />;
    }
    return mode === 'dark' ? <LightModeIcon /> : <DarkModeIcon />;
  };

  const getTooltip = () => {
    if (themeMode === 'system') {
      return `Tema automático (${mode === 'dark' ? 'escuro' : 'claro'})`;
    }
    return mode === 'dark' ? 'Alternar para tema claro' : 'Alternar para tema escuro';
  };

  if (variant === 'icon') {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Tooltip title={getTooltip()}>
          <IconButton
            onClick={handleClick}
            color="inherit"
            size="small"
          >
            {getIcon()}
          </IconButton>
        </Tooltip>
        {showLabel && (
          <Typography variant="body2" color="text.secondary">
            {themeMode === 'system' ? 'Auto' : mode === 'dark' ? 'Escuro' : 'Claro'}
          </Typography>
        )}
      </Box>
    );
  }

  return (
    <>
      <Tooltip title="Configurações de tema">
        <IconButton
          onClick={handleClick}
          color="inherit"
          size="small"
        >
          {getIcon()}
        </IconButton>
      </Tooltip>
      
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        PaperProps={{
          sx: {
            minWidth: 180,
            mt: 1
          }
        }}
      >
        <MenuItem 
          onClick={() => handleThemeChange('light')}
          selected={themeMode === 'light'}
        >
          <ListItemIcon>
            <LightModeIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Claro</ListItemText>
          {themeMode === 'light' && (
            <CheckIcon fontSize="small" color="primary" />
          )}
        </MenuItem>
        
        <MenuItem 
          onClick={() => handleThemeChange('dark')}
          selected={themeMode === 'dark'}
        >
          <ListItemIcon>
            <DarkModeIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Escuro</ListItemText>
          {themeMode === 'dark' && (
            <CheckIcon fontSize="small" color="primary" />
          )}
        </MenuItem>
        
        <MenuItem 
          onClick={() => handleThemeChange('system')}
          selected={themeMode === 'system'}
        >
          <ListItemIcon>
            <AutoModeIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>
            <Box>
              <Typography variant="body2">Automático</Typography>
              <Typography variant="caption" color="text.secondary">
                Segue o sistema ({systemTheme === 'dark' ? 'escuro' : 'claro'})
              </Typography>
            </Box>
          </ListItemText>
          {themeMode === 'system' && (
            <CheckIcon fontSize="small" color="primary" />
          )}
        </MenuItem>
      </Menu>
    </>
  );
};

export default ThemeToggle;

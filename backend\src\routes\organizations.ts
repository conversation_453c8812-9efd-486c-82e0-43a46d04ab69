import { FastifyInstance } from 'fastify'
import { OrganizationService } from '../services/OrganizationService'
import { withTenantIsolation, withLimitCheck } from '../middleware/tenantIsolation'
import { Logger } from '../utils/Logger'
import { PlanType, OrganizationRole } from '@prisma/client'

/**
 * Rotas para gerenciamento de organizações multi-tenant
 */
export async function organizationRoutes(fastify: FastifyInstance) {
  
  /**
   * POST /api/organizations
   * Cria uma nova organização
   */
  fastify.post('/', withTenantIsolation(async (request: any, reply: any) => {
    try {
      const userId = request.user.id
      const { name, slug, description, website, planType } = request.body as {
        name: string
        slug: string
        description?: string
        website?: string
        planType?: PlanType
      }

      if (!name || !slug) {
        return reply.status(400).send({
          success: false,
          error: 'Nome e slug são obrigatórios'
        })
      }

      // Verificar se usuário já pertence a uma organização
      if (request.organizationId) {
        return reply.status(400).send({
          success: false,
          error: 'Usuário já pertence a uma organização'
        })
      }

      const organization = await OrganizationService.createOrganization(userId, {
        name,
        slug,
        description,
        website,
        planType
      })

      return {
        success: true,
        data: organization,
        message: 'Organização criada com sucesso',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao criar organização:', error)
      return reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  }))

  /**
   * GET /api/organizations/current
   * Obtém organização atual do usuário
   */
  fastify.get('/current', withTenantIsolation(async (request: any, reply: any) => {
    try {
      if (!request.organizationId) {
        return reply.status(404).send({
          success: false,
          error: 'Usuário não pertence a nenhuma organização'
        })
      }

      const organization = await OrganizationService.getOrganizationWithStats(request.organizationId)

      if (!organization) {
        return reply.status(404).send({
          success: false,
          error: 'Organização não encontrada'
        })
      }

      return {
        success: true,
        data: organization,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao obter organização atual:', error)
      return reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor'
      })
    }
  }))

  /**
   * GET /api/organizations/my
   * Lista organizações do usuário
   */
  fastify.get('/my', withTenantIsolation(async (request: any, reply: any) => {
    try {
      const userId = request.user.id
      const organizations = await OrganizationService.getUserOrganizations(userId)

      return {
        success: true,
        data: organizations,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao listar organizações:', error)
      return reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor'
      })
    }
  }))

  /**
   * POST /api/organizations/invite
   * Convida usuário para organização
   */
  fastify.post('/invite', withTenantIsolation(async (request: any, reply: any) => {
    try {
      if (!request.organizationId) {
        return reply.status(400).send({
          success: false,
          error: 'Usuário não pertence a nenhuma organização'
        })
      }

      // Verificar se usuário tem permissão para convidar
      if (!['OWNER', 'ADMIN'].includes(request.organizationRole)) {
        return reply.status(403).send({
          success: false,
          error: 'Sem permissão para convidar usuários'
        })
      }

      const { email, role } = request.body as {
        email: string
        role: OrganizationRole
      }

      if (!email || !role) {
        return reply.status(400).send({
          success: false,
          error: 'Email e role são obrigatórios'
        })
      }

      await OrganizationService.inviteUser(request.organizationId, request.user.id, {
        email,
        role
      })

      return {
        success: true,
        message: 'Convite enviado com sucesso',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao convidar usuário:', error)
      return reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  }))

  /**
   * POST /api/organizations/accept-invite/:token
   * Aceita convite de organização
   */
  fastify.post('/accept-invite/:token', withTenantIsolation(async (request: any, reply: any) => {
    try {
      const { token } = request.params as { token: string }
      const userId = request.user.id

      const organization = await OrganizationService.acceptInvite(token, userId)

      return {
        success: true,
        data: organization,
        message: 'Convite aceito com sucesso',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao aceitar convite:', error)
      return reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  }))

  /**
   * DELETE /api/organizations/users/:userId
   * Remove usuário da organização
   */
  fastify.delete('/users/:userId', withTenantIsolation(async (request: any, reply: any) => {
    try {
      if (!request.organizationId) {
        return reply.status(400).send({
          success: false,
          error: 'Usuário não pertence a nenhuma organização'
        })
      }

      // Verificar permissões
      if (!['OWNER', 'ADMIN'].includes(request.organizationRole)) {
        return reply.status(403).send({
          success: false,
          error: 'Sem permissão para remover usuários'
        })
      }

      const { userId } = request.params as { userId: string }

      await OrganizationService.removeUser(request.organizationId, userId, request.user.id)

      return {
        success: true,
        message: 'Usuário removido com sucesso',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao remover usuário:', error)
      return reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  }))

  /**
   * GET /api/organizations/limits
   * Verifica limites da organização
   */
  fastify.get('/limits', withTenantIsolation(async (request: any, reply: any) => {
    try {
      if (!request.organizationId) {
        return reply.status(400).send({
          success: false,
          error: 'Usuário não pertence a nenhuma organização'
        })
      }

      const limits = await OrganizationService.checkLimits(request.organizationId)

      return {
        success: true,
        data: limits,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao verificar limites:', error)
      return reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor'
      })
    }
  }))

  /**
   * POST /api/organizations/switch/:organizationId
   * Troca de organização (para usuários que pertencem a múltiplas)
   */
  fastify.post('/switch/:organizationId', withTenantIsolation(async (request: any, reply: any) => {
    try {
      const { organizationId } = request.params as { organizationId: string }
      const userId = request.user.id

      // Verificar se usuário tem acesso à organização
      const organizations = await OrganizationService.getUserOrganizations(userId)
      const hasAccess = organizations.some(org => org.id === organizationId)

      if (!hasAccess) {
        return reply.status(403).send({
          success: false,
          error: 'Sem acesso a esta organização'
        })
      }

      // Atualizar organização atual do usuário
      await prisma.user.update({
        where: { id: userId },
        data: { organizationId }
      })

      return {
        success: true,
        message: 'Organização alterada com sucesso',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao trocar organização:', error)
      return reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor'
      })
    }
  }))

  /**
   * PUT /api/organizations/current
   * Atualiza organização atual
   */
  fastify.put('/current', withTenantIsolation(async (request: any, reply: any) => {
    try {
      if (!request.organizationId) {
        return reply.status(400).send({
          success: false,
          error: 'Usuário não pertence a nenhuma organização'
        })
      }

      // Verificar permissões
      if (!['OWNER', 'ADMIN'].includes(request.organizationRole)) {
        return reply.status(403).send({
          success: false,
          error: 'Sem permissão para atualizar organização'
        })
      }

      const { name, description, website, settings } = request.body as {
        name?: string
        description?: string
        website?: string
        settings?: any
      }

      const updatedOrg = await prisma.organization.update({
        where: { id: request.organizationId },
        data: {
          ...(name && { name }),
          ...(description !== undefined && { description }),
          ...(website !== undefined && { website }),
          ...(settings && { settings })
        }
      })

      return {
        success: true,
        data: updatedOrg,
        message: 'Organização atualizada com sucesso',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao atualizar organização:', error)
      return reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor'
      })
    }
  }))
}

export default organizationRoutes

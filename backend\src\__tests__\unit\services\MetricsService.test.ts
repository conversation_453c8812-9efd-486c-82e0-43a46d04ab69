import { MetricsService } from '../../../services/monitoring/MetricsService';

describe('MetricsService', () => {
  let metricsService: MetricsService;

  beforeEach(() => {
    metricsService = new MetricsService();
  });

  describe('recordCommandExecution', () => {
    it('should record a successful command execution', () => {
      const serverId = 'server-1';
      const serverName = 'Test Server';
      const deviceType = 'HUAWEI';
      const command = 'display version';
      const executionTime = 1500;
      const success = true;
      const service = 'nodejs';

      metricsService.recordCommandExecution(
        serverId,
        serverName,
        deviceType,
        command,
        executionTime,
        success,
        service
      );

      const deviceMetrics = metricsService.getDeviceMetrics();
      expect(deviceMetrics).toHaveLength(1);
      expect(deviceMetrics[0]).toMatchObject({
        deviceType: 'HUAWEI',
        totalCommands: 1,
        successfulCommands: 1,
        failedCommands: 0,
        averageExecutionTime: 1500,
        successRate: 1
      });
    });

    it('should record a failed command execution', () => {
      const serverId = 'server-1';
      const serverName = 'Test Server';
      const deviceType = 'HUAWEI';
      const command = 'display version';
      const executionTime = 2000;
      const success = false;
      const service = 'nodejs';
      const errorType = 'TIMEOUT';

      metricsService.recordCommandExecution(
        serverId,
        serverName,
        deviceType,
        command,
        executionTime,
        success,
        service,
        errorType
      );

      const deviceMetrics = metricsService.getDeviceMetrics();
      expect(deviceMetrics).toHaveLength(1);
      expect(deviceMetrics[0]).toMatchObject({
        deviceType: 'HUAWEI',
        totalCommands: 1,
        successfulCommands: 0,
        failedCommands: 1,
        averageExecutionTime: 2000,
        successRate: 0
      });
    });

    it('should aggregate metrics for multiple commands on same device type', () => {
      const serverId = 'server-1';
      const serverName = 'Test Server';
      const deviceType = 'HUAWEI';

      // Primeiro comando - sucesso
      metricsService.recordCommandExecution(
        serverId,
        serverName,
        deviceType,
        'display version',
        1000,
        true,
        'nodejs'
      );

      // Segundo comando - falha
      metricsService.recordCommandExecution(
        serverId,
        serverName,
        deviceType,
        'display interface',
        3000,
        false,
        'nodejs',
        'TIMEOUT'
      );

      const deviceMetrics = metricsService.getDeviceMetrics();
      expect(deviceMetrics).toHaveLength(1);
      expect(deviceMetrics[0]).toMatchObject({
        deviceType: 'HUAWEI',
        totalCommands: 2,
        successfulCommands: 1,
        failedCommands: 1,
        averageExecutionTime: 2000, // (1000 + 3000) / 2
        successRate: 0.5 // 1/2
      });
    });
  });

  describe('getServiceMetrics', () => {
    it('should return metrics grouped by service', () => {
      // Comandos Node.js
      metricsService.recordCommandExecution(
        'server-1',
        'Server 1',
        'MIKROTIK',
        'interface print',
        800,
        true,
        'nodejs'
      );

      metricsService.recordCommandExecution(
        'server-2',
        'Server 2',
        'MIKROTIK',
        'ip route print',
        1200,
        true,
        'nodejs'
      );

      // Comandos Python
      metricsService.recordCommandExecution(
        'server-3',
        'Server 3',
        'HUAWEI',
        'display version',
        2000,
        true,
        'python'
      );

      const serviceMetrics = metricsService.getServiceMetrics();
      expect(serviceMetrics).toHaveLength(2);

      const nodejsMetrics = serviceMetrics.find(m => m.service === 'nodejs');
      const pythonMetrics = serviceMetrics.find(m => m.service === 'python');

      expect(nodejsMetrics).toMatchObject({
        service: 'nodejs',
        totalCommands: 2,
        successfulCommands: 2,
        failedCommands: 0,
        averageExecutionTime: 1000, // (800 + 1200) / 2
        successRate: 1
      });

      expect(pythonMetrics).toMatchObject({
        service: 'python',
        totalCommands: 1,
        successfulCommands: 1,
        failedCommands: 0,
        averageExecutionTime: 2000,
        successRate: 1
      });
    });
  });

  describe('getSystemHealth', () => {
    it('should return healthy status when no issues', async () => {
      // Adicionar algumas métricas de sucesso
      metricsService.recordCommandExecution(
        'server-1',
        'Server 1',
        'HUAWEI',
        'display version',
        1000,
        true,
        'nodejs'
      );

      const health = await metricsService.getSystemHealth();
      expect(health.status).toBe('healthy');
      expect(health.services).toBeDefined();
      expect(health.alerts).toBeDefined();
      expect(health.lastCheck).toBeDefined();
    });
  });

  describe('getPerformanceMetrics', () => {
    it('should return performance metrics for specified time range', () => {
      // Adicionar algumas métricas
      metricsService.recordCommandExecution(
        'server-1',
        'Server 1',
        'HUAWEI',
        'display version',
        1500,
        true,
        'nodejs'
      );

      metricsService.recordCommandExecution(
        'server-2',
        'Server 2',
        'MIKROTIK',
        'interface print',
        800,
        true,
        'python'
      );

      const performance = metricsService.getPerformanceMetrics('24h');
      expect(performance).toMatchObject({
        timeRange: '24h',
        intervals: expect.any(Array),
        summary: expect.objectContaining({
          totalCommands: 2,
          successRate: 1,
          averageExecutionTime: 1150 // (1500 + 800) / 2
        })
      });
    });
  });

  describe('error categorization', () => {
    it('should categorize common errors correctly', () => {
      const deviceMetrics = metricsService.getDeviceMetrics();
      
      // Simular diferentes tipos de erro
      metricsService.recordCommandExecution(
        'server-1',
        'Server 1',
        'HUAWEI',
        'display version',
        1000,
        false,
        'nodejs',
        'TIMEOUT'
      );

      metricsService.recordCommandExecution(
        'server-1',
        'Server 1',
        'HUAWEI',
        'display interface',
        1000,
        false,
        'nodejs',
        'AUTHENTICATION'
      );

      metricsService.recordCommandExecution(
        'server-1',
        'Server 1',
        'HUAWEI',
        'display config',
        1000,
        false,
        'nodejs',
        'TIMEOUT'
      );

      const metrics = metricsService.getDeviceMetrics();
      expect(metrics).toHaveLength(1);
      expect(metrics[0].commonErrors).toEqual({
        'TIMEOUT': 2,
        'AUTHENTICATION': 1
      });
    });
  });

  describe('metrics cleanup', () => {
    it('should maintain maximum metrics history', () => {
      // Simular muitas métricas (mais que o limite)
      for (let i = 0; i < 15000; i++) {
        metricsService.recordCommandExecution(
          `server-${i}`,
          `Server ${i}`,
          'GENERIC',
          'test command',
          1000,
          true,
          'nodejs'
        );
      }

      // Verificar se o limite foi respeitado
      // Nota: Este teste pode precisar ser ajustado dependendo da implementação interna
      const deviceMetrics = metricsService.getDeviceMetrics();
      expect(deviceMetrics.length).toBeGreaterThan(0);
    });
  });
});

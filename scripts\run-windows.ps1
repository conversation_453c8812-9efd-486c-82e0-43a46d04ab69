# RemoteOps Windows PowerShell Automation Script
# Advanced setup and management for Windows environments

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("install", "start", "stop", "restart", "status", "update", "uninstall")]
    [string]$Action = "install",
    
    [Parameter(Mandatory=$false)]
    [switch]$Force,
    
    [Parameter(Mandatory=$false)]
    [switch]$Docker,
    
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "C:\RemoteOps"
)

# Requires PowerShell 5.1 or later
#Requires -Version 5.1

# Set execution policy for current session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Configuration
$Config = @{
    ProjectName = "RemoteOps"
    NodeVersion = "18.17.0"
    PythonVersion = "3.11.0"
    PostgreSQLVersion = "15"
    RedisVersion = "latest"
    ProjectPath = $ProjectPath
    Ports = @{
        Frontend = 3001
        Backend = 3000
        Python = 8000
        PostgreSQL = 5432
        Redis = 6379
    }
}

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Cyan"
    White = "White"
}

# Logging functions
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { $Colors.Red }
        "SUCCESS" { $Colors.Green }
        "WARNING" { $Colors.Yellow }
        "INFO" { $Colors.Blue }
        default { $Colors.White }
    }
    
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Test-Command {
    param([string]$Command)
    
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Install-Chocolatey {
    Write-Log "Installing Chocolatey package manager..." "INFO"
    
    try {
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        
        # Refresh environment
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
        
        Write-Log "Chocolatey installed successfully" "SUCCESS"
        return $true
    }
    catch {
        Write-Log "Failed to install Chocolatey: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Install-Prerequisites {
    Write-Log "Installing prerequisites..." "INFO"
    
    # Install Chocolatey if not present
    if (-not (Test-Command "choco")) {
        if (-not (Install-Chocolatey)) {
            throw "Failed to install Chocolatey"
        }
    }
    
    # Install Node.js
    if (-not (Test-Command "node")) {
        Write-Log "Installing Node.js..." "INFO"
        choco install nodejs --version=$($Config.NodeVersion) -y
        if ($LASTEXITCODE -ne 0) { throw "Failed to install Node.js" }
    }
    
    # Install Python
    if (-not (Test-Command "python")) {
        Write-Log "Installing Python..." "INFO"
        choco install python --version=$($Config.PythonVersion) -y
        if ($LASTEXITCODE -ne 0) { throw "Failed to install Python" }
    }
    
    # Install Git
    if (-not (Test-Command "git")) {
        Write-Log "Installing Git..." "INFO"
        choco install git -y
        if ($LASTEXITCODE -ne 0) { throw "Failed to install Git" }
    }
    
    # Install PostgreSQL
    if (-not (Test-Command "psql")) {
        Write-Log "Installing PostgreSQL..." "INFO"
        choco install postgresql --params '/Password:password' -y
        if ($LASTEXITCODE -ne 0) { throw "Failed to install PostgreSQL" }
    }
    
    # Install Redis
    if (-not (Test-Command "redis-server")) {
        Write-Log "Installing Redis..." "INFO"
        choco install redis-64 -y
        if ($LASTEXITCODE -ne 0) { throw "Failed to install Redis" }
    }
    
    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    
    Write-Log "Prerequisites installed successfully" "SUCCESS"
}

function Setup-Project {
    Write-Log "Setting up RemoteOps project..." "INFO"
    
    # Create project directory
    if (-not (Test-Path $Config.ProjectPath)) {
        New-Item -ItemType Directory -Path $Config.ProjectPath -Force | Out-Null
        Write-Log "Created project directory: $($Config.ProjectPath)" "SUCCESS"
    }
    
    Set-Location $Config.ProjectPath
    
    # Copy project files if not exists
    if (-not (Test-Path "package.json")) {
        $scriptDir = Split-Path -Parent $PSScriptRoot
        Copy-Item -Path "$scriptDir\*" -Destination $Config.ProjectPath -Recurse -Force
        Write-Log "Project files copied" "SUCCESS"
    }
    
    # Install dependencies
    Install-Dependencies
    
    # Setup environment
    Setup-Environment
    
    # Setup database
    Setup-Database
}

function Install-Dependencies {
    Write-Log "Installing project dependencies..." "INFO"
    
    # Backend dependencies
    if (Test-Path "backend\package.json") {
        Set-Location "backend"
        Write-Log "Installing backend dependencies..." "INFO"
        npm install
        if ($LASTEXITCODE -ne 0) { throw "Failed to install backend dependencies" }
        Set-Location ".."
    }
    
    # Frontend dependencies
    if (Test-Path "frontend\package.json") {
        Set-Location "frontend"
        Write-Log "Installing frontend dependencies..." "INFO"
        npm install
        if ($LASTEXITCODE -ne 0) { throw "Failed to install frontend dependencies" }
        Set-Location ".."
    }
    
    # Python dependencies
    if (Test-Path "python-microservice\requirements.txt") {
        Set-Location "python-microservice"
        Write-Log "Installing Python dependencies..." "INFO"
        python -m pip install -r requirements.txt
        if ($LASTEXITCODE -ne 0) { throw "Failed to install Python dependencies" }
        Set-Location ".."
    }
    
    Write-Log "Dependencies installed successfully" "SUCCESS"
}

function Setup-Environment {
    Write-Log "Setting up environment..." "INFO"
    
    if (-not (Test-Path ".env")) {
        $envContent = @"
NODE_ENV=development
JWT_SECRET=$(Get-Random -Minimum 100000 -Maximum 999999)$(Get-Random -Minimum 100000 -Maximum 999999)
DATABASE_URL=postgresql://postgres:password@localhost:5432/remoteops
REDIS_URL=redis://localhost:6379
PORT=3000
FRONTEND_URL=http://localhost:3001
PYTHON_MICROSERVICE_URL=http://localhost:8000
"@
        $envContent | Out-File -FilePath ".env" -Encoding UTF8
        Write-Log "Environment file created" "SUCCESS"
    }
}

function Setup-Database {
    Write-Log "Setting up database..." "INFO"
    
    # Start PostgreSQL service
    Start-Service -Name "postgresql-x64-*" -ErrorAction SilentlyContinue
    
    # Create database
    try {
        & psql -U postgres -c "CREATE DATABASE remoteops;" 2>$null
        Write-Log "Database created" "SUCCESS"
    }
    catch {
        Write-Log "Database might already exist" "WARNING"
    }
    
    # Run migrations
    if (Test-Path "backend\prisma\schema.prisma") {
        Set-Location "backend"
        Write-Log "Running database migrations..." "INFO"
        npx prisma migrate dev --name init
        if ($LASTEXITCODE -ne 0) { throw "Failed to run migrations" }
        Set-Location ".."
    }
    
    # Start Redis service
    Start-Service -Name "Redis" -ErrorAction SilentlyContinue
}

function Start-Applications {
    Write-Log "Starting RemoteOps applications..." "INFO"
    
    Set-Location $Config.ProjectPath
    
    # Start backend
    Write-Log "Starting backend server..." "INFO"
    Start-Process -FilePath "cmd" -ArgumentList "/k", "cd /d `"$($Config.ProjectPath)\backend`" && npm run dev" -WindowStyle Normal
    
    Start-Sleep -Seconds 5
    
    # Start frontend
    Write-Log "Starting frontend server..." "INFO"
    Start-Process -FilePath "cmd" -ArgumentList "/k", "cd /d `"$($Config.ProjectPath)\frontend`" && npm run dev" -WindowStyle Normal
    
    # Start Python microservice
    Write-Log "Starting Python microservice..." "INFO"
    Start-Process -FilePath "cmd" -ArgumentList "/k", "cd /d `"$($Config.ProjectPath)\python-microservice`" && python main.py" -WindowStyle Normal
    
    Write-Log "Applications started successfully" "SUCCESS"
    
    # Wait for services
    Write-Log "Waiting for services to be ready..." "INFO"
    Start-Sleep -Seconds 15
    
    # Open browser
    Write-Log "Opening RemoteOps in browser..." "INFO"
    Start-Process "http://localhost:$($Config.Ports.Frontend)"
}

function Stop-Applications {
    Write-Log "Stopping RemoteOps applications..." "INFO"
    
    # Stop Node.js processes
    Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force
    
    # Stop Python processes
    Get-Process -Name "python" -ErrorAction SilentlyContinue | Where-Object { $_.Path -like "*RemoteOps*" } | Stop-Process -Force
    
    Write-Log "Applications stopped" "SUCCESS"
}

function Get-ApplicationStatus {
    Write-Log "Checking application status..." "INFO"
    
    $status = @{
        Backend = $false
        Frontend = $false
        Python = $false
        Database = $false
        Redis = $false
    }
    
    # Check backend
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$($Config.Ports.Backend)/api/health" -TimeoutSec 5
        $status.Backend = $response.StatusCode -eq 200
    } catch { }
    
    # Check frontend
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$($Config.Ports.Frontend)" -TimeoutSec 5
        $status.Frontend = $response.StatusCode -eq 200
    } catch { }
    
    # Check Python microservice
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$($Config.Ports.Python)/health" -TimeoutSec 5
        $status.Python = $response.StatusCode -eq 200
    } catch { }
    
    # Check database
    $status.Database = (Get-Service -Name "postgresql-x64-*" -ErrorAction SilentlyContinue).Status -eq "Running"
    
    # Check Redis
    $status.Redis = (Get-Service -Name "Redis" -ErrorAction SilentlyContinue).Status -eq "Running"
    
    # Display status
    Write-Host "`nRemoteOps Status:" -ForegroundColor $Colors.Blue
    Write-Host "=================" -ForegroundColor $Colors.Blue
    
    foreach ($service in $status.GetEnumerator()) {
        $color = if ($service.Value) { $Colors.Green } else { $Colors.Red }
        $statusText = if ($service.Value) { "RUNNING" } else { "STOPPED" }
        Write-Host "$($service.Key): $statusText" -ForegroundColor $color
    }
    
    if ($status.Backend -and $status.Frontend) {
        Write-Host "`nApplication URLs:" -ForegroundColor $Colors.Blue
        Write-Host "Frontend: http://localhost:$($Config.Ports.Frontend)" -ForegroundColor $Colors.Green
        Write-Host "Backend API: http://localhost:$($Config.Ports.Backend)" -ForegroundColor $Colors.Green
        Write-Host "Python Service: http://localhost:$($Config.Ports.Python)" -ForegroundColor $Colors.Green
    }
}

# Main execution
try {
    Write-Host "========================================" -ForegroundColor $Colors.Blue
    Write-Host "    RemoteOps Windows PowerShell Script" -ForegroundColor $Colors.Blue
    Write-Host "========================================" -ForegroundColor $Colors.Blue
    Write-Host ""
    
    # Check administrator privileges
    if (-not (Test-Administrator)) {
        Write-Log "This script requires administrator privileges" "ERROR"
        Write-Log "Please run PowerShell as Administrator" "ERROR"
        exit 1
    }
    
    switch ($Action.ToLower()) {
        "install" {
            Install-Prerequisites
            Setup-Project
            Start-Applications
            
            Write-Host "`n" -NoNewline
            Write-Log "RemoteOps installation completed successfully!" "SUCCESS"
            Write-Host "`nDefault credentials:" -ForegroundColor $Colors.Blue
            Write-Host "Email: <EMAIL>" -ForegroundColor $Colors.Green
            Write-Host "Password: admin123" -ForegroundColor $Colors.Green
        }
        
        "start" {
            Start-Applications
        }
        
        "stop" {
            Stop-Applications
        }
        
        "restart" {
            Stop-Applications
            Start-Sleep -Seconds 3
            Start-Applications
        }
        
        "status" {
            Get-ApplicationStatus
        }
        
        "update" {
            Stop-Applications
            Install-Dependencies
            Start-Applications
        }
        
        "uninstall" {
            Stop-Applications
            if ($Force) {
                Remove-Item -Path $Config.ProjectPath -Recurse -Force -ErrorAction SilentlyContinue
                Write-Log "RemoteOps uninstalled" "SUCCESS"
            } else {
                Write-Log "Use -Force parameter to confirm uninstallation" "WARNING"
            }
        }
        
        default {
            Write-Log "Invalid action: $Action" "ERROR"
            Write-Log "Valid actions: install, start, stop, restart, status, update, uninstall" "INFO"
        }
    }
}
catch {
    Write-Log "Error: $($_.Exception.Message)" "ERROR"
    exit 1
}

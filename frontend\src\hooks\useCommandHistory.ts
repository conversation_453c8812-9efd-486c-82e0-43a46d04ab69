import { useState, useEffect, useCallback } from 'react';

interface CommandHistoryEntry {
  id: string;
  command: string;
  timestamp: Date;
  serverId?: string;
  serverName?: string;
  deviceType?: string;
  success?: boolean;
  result?: string;
}

interface UseCommandHistoryReturn {
  history: CommandHistoryEntry[];
  addCommand: (command: string, metadata?: Partial<CommandHistoryEntry>) => void;
  clearHistory: () => void;
  getRecentCommands: (limit?: number) => string[];
  getCommandsByServer: (serverId: string) => CommandHistoryEntry[];
  searchHistory: (query: string) => CommandHistoryEntry[];
  removeCommand: (id: string) => void;
  updateCommandResult: (id: string, success: boolean, result?: string) => void;
  exportHistory: () => string;
  getSessionStats: () => {
    totalCommands: number;
    uniqueCommands: number;
    successRate: number;
    serversUsed: number;
  };
}

const STORAGE_KEY = 'sem-fronteiras-session-history';
const MAX_HISTORY_SIZE = 1000; // Máximo de comandos no histórico

export const useCommandHistory = (): UseCommandHistoryReturn => {
  const [history, setHistory] = useState<CommandHistoryEntry[]>([]);

  // Carregar histórico do sessionStorage na inicialização
  useEffect(() => {
    try {
      const stored = sessionStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Converter strings de data de volta para objetos Date
        const historyWithDates = parsed.map((entry: any) => ({
          ...entry,
          timestamp: new Date(entry.timestamp)
        }));
        setHistory(historyWithDates);
      }
    } catch (error) {
      console.error('Erro ao carregar histórico da sessão:', error);
    }
  }, []);

  // Salvar histórico no sessionStorage sempre que mudar
  useEffect(() => {
    try {
      sessionStorage.setItem(STORAGE_KEY, JSON.stringify(history));
    } catch (error) {
      console.error('Erro ao salvar histórico da sessão:', error);
    }
  }, [history]);

  const addCommand = useCallback((command: string, metadata: Partial<CommandHistoryEntry> = {}) => {
    const trimmedCommand = command.trim();
    if (!trimmedCommand) return;

    const newEntry: CommandHistoryEntry = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      command: trimmedCommand,
      timestamp: new Date(),
      ...metadata
    };

    setHistory(prev => {
      const newHistory = [newEntry, ...prev];
      // Manter apenas os últimos MAX_HISTORY_SIZE comandos
      return newHistory.slice(0, MAX_HISTORY_SIZE);
    });
  }, []);

  const clearHistory = useCallback(() => {
    setHistory([]);
  }, []);

  const getRecentCommands = useCallback((limit: number = 10) => {
    // Retornar comandos únicos mais recentes
    const uniqueCommands = new Set<string>();
    const recentCommands: string[] = [];

    for (const entry of history) {
      if (!uniqueCommands.has(entry.command)) {
        uniqueCommands.add(entry.command);
        recentCommands.push(entry.command);
        if (recentCommands.length >= limit) break;
      }
    }

    return recentCommands;
  }, [history]);

  const getCommandsByServer = useCallback((serverId: string) => {
    return history.filter(entry => entry.serverId === serverId);
  }, [history]);

  const searchHistory = useCallback((query: string) => {
    const lowerQuery = query.toLowerCase();
    return history.filter(entry =>
      entry.command.toLowerCase().includes(lowerQuery) ||
      (entry.serverName && entry.serverName.toLowerCase().includes(lowerQuery)) ||
      (entry.result && entry.result.toLowerCase().includes(lowerQuery))
    );
  }, [history]);

  const removeCommand = useCallback((id: string) => {
    setHistory(prev => prev.filter(entry => entry.id !== id));
  }, []);

  const updateCommandResult = useCallback((id: string, success: boolean, result?: string) => {
    setHistory(prev =>
      prev.map(entry =>
        entry.id === id
          ? { ...entry, success, result }
          : entry
      )
    );
  }, []);

  const exportHistory = useCallback(() => {
    return JSON.stringify(history, null, 2);
  }, [history]);

  const getSessionStats = useCallback(() => {
    const totalCommands = history.length;
    const uniqueCommands = new Set(history.map(entry => entry.command)).size;
    const successfulCommands = history.filter(entry => entry.success === true).length;
    const commandsWithResult = history.filter(entry => entry.success !== undefined).length;
    const successRate = commandsWithResult > 0 ? (successfulCommands / commandsWithResult) * 100 : 0;
    const serversUsed = new Set(history.map(entry => entry.serverId).filter(Boolean)).size;

    return {
      totalCommands,
      uniqueCommands,
      successRate: Math.round(successRate * 100) / 100,
      serversUsed
    };
  }, [history]);

  return {
    history,
    addCommand,
    clearHistory,
    getRecentCommands,
    getCommandsByServer,
    searchHistory,
    removeCommand,
    updateCommandResult,
    exportHistory,
    getSessionStats
  };
};

// Hook para obter comandos mais frequentes da sessão
export const useFrequentCommands = (limit: number = 10) => {
  const { history } = useCommandHistory();

  const frequentCommands = React.useMemo(() => {
    const commandCounts = new Map<string, number>();

    // Contar frequência de cada comando
    history.forEach(entry => {
      const count = commandCounts.get(entry.command) || 0;
      commandCounts.set(entry.command, count + 1);
    });

    // Converter para array e ordenar por frequência
    return Array.from(commandCounts.entries())
      .map(([command, count]) => ({ command, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }, [history, limit]);

  return frequentCommands;
};

// Hook para obter estatísticas de comandos por servidor
export const useServerCommandStats = () => {
  const { history } = useCommandHistory();

  const serverStats = React.useMemo(() => {
    const stats = new Map<string, {
      serverId: string;
      serverName: string;
      commandCount: number;
      successCount: number;
      lastUsed: Date;
    }>();

    history.forEach(entry => {
      if (!entry.serverId) return;

      const existing = stats.get(entry.serverId) || {
        serverId: entry.serverId,
        serverName: entry.serverName || 'Servidor desconhecido',
        commandCount: 0,
        successCount: 0,
        lastUsed: entry.timestamp
      };

      existing.commandCount++;
      if (entry.success === true) {
        existing.successCount++;
      }
      if (entry.timestamp > existing.lastUsed) {
        existing.lastUsed = entry.timestamp;
      }

      stats.set(entry.serverId, existing);
    });

    return Array.from(stats.values())
      .sort((a, b) => b.commandCount - a.commandCount);
  }, [history]);

  return serverStats;
};

export default useCommandHistory;

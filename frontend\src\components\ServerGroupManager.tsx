import React, { useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { X, Plus, Edit2, Trash2, Users, FolderPlus } from 'lucide-react'
import {
  listServerGroups,
  createServerGroup,
  updateServerGroup,
  deleteServerGroup
} from '../services/serverGroupApi'
import { ServerGroup, CreateServerGroupDTO, GROUP_COLORS } from '../types/serverGroup'
import toast from 'react-hot-toast'

interface ServerGroupManagerProps {
  isOpen: boolean
  onClose: () => void
}

export default function ServerGroupManager({ isOpen, onClose }: ServerGroupManagerProps) {
  const queryClient = useQueryClient()
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingGroup, setEditingGroup] = useState<ServerGroup | null>(null)

  // Buscar grupos
  const { data: groups, isLoading } = useQuery({
    queryKey: ['server-groups'],
    queryFn: listServerGroups,
    enabled: isOpen,
  })

  // Mutação para criar grupo
  const createMutation = useMutation({
    mutationFn: createServerGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['server-groups'] })
      setIsCreateModalOpen(false)
      toast.success('Grupo criado com sucesso!')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao criar grupo')
    },
  })

  // Mutação para atualizar grupo
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string, data: CreateServerGroupDTO }) =>
      updateServerGroup(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['server-groups'] })
      setEditingGroup(null)
      toast.success('Grupo atualizado com sucesso!')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao atualizar grupo')
    },
  })

  // Mutação para excluir grupo
  const deleteMutation = useMutation({
    mutationFn: deleteServerGroup,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['server-groups'] })
      toast.success('Grupo excluído com sucesso!')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Erro ao excluir grupo')
    },
  })

  const handleDelete = (group: ServerGroup) => {
    if (confirm(`Tem certeza que deseja excluir o grupo "${group.name}"?`)) {
      deleteMutation.mutate(group.id)
    }
  }

  return (
    <>
      <Transition appear show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="div" className="flex justify-between items-center mb-6">
                    <div className="flex items-center gap-2">
                      <FolderPlus className="h-6 w-6 text-blue-600" />
                      <h3 className="text-lg font-medium text-gray-900">
                        Gerenciar Grupos de Servidores
                      </h3>
                    </div>
                    <button
                      onClick={onClose}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-6 w-6" />
                    </button>
                  </Dialog.Title>

                  <div className="space-y-4">
                    {/* Botão para criar novo grupo */}
                    <button
                      onClick={() => setIsCreateModalOpen(true)}
                      className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Plus className="h-5 w-5" />
                      Criar Novo Grupo
                    </button>

                    {/* Lista de grupos */}
                    {isLoading ? (
                      <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="mt-2 text-gray-500">Carregando grupos...</p>
                      </div>
                    ) : groups && groups.length > 0 ? (
                      <div className="space-y-3">
                        {groups.map((group) => (
                          <div
                            key={group.id}
                            className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                          >
                            <div className="flex items-center gap-3">
                              <div
                                className="w-4 h-4 rounded-full"
                                style={{ backgroundColor: group.color || '#3B82F6' }}
                              />
                              <div>
                                <h4 className="font-medium text-gray-900">{group.name}</h4>
                                {group.description && (
                                  <p className="text-sm text-gray-500">{group.description}</p>
                                )}
                                <div className="flex items-center gap-1 mt-1">
                                  <Users className="h-4 w-4 text-gray-400" />
                                  <span className="text-sm text-gray-500">
                                    {group._count?.members || 0} servidor(es)
                                  </span>
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => setEditingGroup(group)}
                                className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                              >
                                <Edit2 className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDelete(group)}
                                className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <FolderPlus className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500">Nenhum grupo criado ainda.</p>
                        <p className="text-sm text-gray-400 mt-1">
                          Crie seu primeiro grupo para organizar seus servidores.
                        </p>
                      </div>
                    )}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* Modal para criar/editar grupo */}
      <GroupFormModal
        isOpen={isCreateModalOpen || !!editingGroup}
        onClose={() => {
          setIsCreateModalOpen(false)
          setEditingGroup(null)
        }}
        group={editingGroup}
        onSubmit={(data) => {
          if (editingGroup) {
            updateMutation.mutate({ id: editingGroup.id, data })
          } else {
            createMutation.mutate(data)
          }
        }}
        isLoading={createMutation.isPending || updateMutation.isPending}
      />
    </>
  )
}

// Componente do formulário de grupo
interface GroupFormModalProps {
  isOpen: boolean
  onClose: () => void
  group?: ServerGroup | null
  onSubmit: (data: CreateServerGroupDTO) => void
  isLoading: boolean
}

function GroupFormModal({ isOpen, onClose, group, onSubmit, isLoading }: GroupFormModalProps) {
  const [formData, setFormData] = useState<CreateServerGroupDTO>({
    name: '',
    description: '',
    color: '#3B82F6',
  })

  React.useEffect(() => {
    if (group) {
      setFormData({
        name: group.name,
        description: group.description || '',
        color: group.color || '#3B82F6',
      })
    } else {
      setFormData({
        name: '',
        description: '',
        color: '#3B82F6',
      })
    }
  }, [group])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim()) {
      toast.error('Nome do grupo é obrigatório')
      return
    }
    onSubmit(formData)
  }

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <Dialog.Title as="div" className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    {group ? 'Editar Grupo' : 'Criar Grupo'}
                  </h3>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </Dialog.Title>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Nome do Grupo *
                    </label>
                    <input
                      type="text"
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Ex: Servidores de Produção"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                      Descrição
                    </label>
                    <textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Descrição opcional do grupo"
                      rows={3}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Cor do Grupo
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {GROUP_COLORS.map((color) => (
                        <button
                          key={color}
                          type="button"
                          onClick={() => setFormData({ ...formData, color })}
                          className={`w-8 h-8 rounded-full border-2 ${
                            formData.color === color ? 'border-gray-400' : 'border-gray-200'
                          }`}
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-end gap-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      Cancelar
                    </button>
                    <button
                      type="submit"
                      disabled={isLoading}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isLoading ? 'Salvando...' : (group ? 'Atualizar' : 'Criar')}
                    </button>
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  )
}

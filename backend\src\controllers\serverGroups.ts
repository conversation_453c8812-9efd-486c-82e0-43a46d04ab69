import { FastifyRequest, FastifyReply } from 'fastify'
import { PrismaClient } from '@prisma/client'
import {
  CreateServerGroupDTO,
  UpdateServerGroupDTO,
  AddServerToGroupDTO
} from '../types/serverGroup'

const prisma = new PrismaClient()

// Listar grupos do usuário
export async function listServerGroups(
  request: FastifyRequest,
  reply: FastifyReply,
) {
  try {
    const groups = await prisma.serverGroup.findMany({
      where: {
        userId: request.user.id,
      },
      include: {
        _count: {
          select: {
            members: true,
          },
        },
        members: {
          include: {
            server: {
              select: {
                id: true,
                name: true,
                host: true,
                port: true,
                deviceType: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return reply.send({ groups })
  } catch (error) {
    console.error('Erro ao listar grupos:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

// Obter grupo específico
export async function getServerGroup(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply,
) {
  try {
    const { id } = request.params

    const group = await prisma.serverGroup.findFirst({
      where: {
        id,
        userId: request.user.id,
      },
      include: {
        _count: {
          select: {
            members: true,
          },
        },
        members: {
          include: {
            server: {
              select: {
                id: true,
                name: true,
                host: true,
                port: true,
                deviceType: true,
              },
            },
          },
        },
      },
    })

    if (!group) {
      return reply.status(404).send({ error: 'Grupo não encontrado' })
    }

    return reply.send(group)
  } catch (error) {
    console.error('Erro ao obter grupo:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

// Criar grupo
export async function createServerGroup(
  request: FastifyRequest<{ Body: CreateServerGroupDTO }>,
  reply: FastifyReply,
) {
  try {
    const { name, description, color } = request.body

    // Verificar se já existe um grupo com o mesmo nome para o usuário
    const existingGroup = await prisma.serverGroup.findFirst({
      where: {
        name,
        userId: request.user.id,
      },
    })

    if (existingGroup) {
      return reply.status(400).send({
        error: 'Já existe um grupo com este nome'
      })
    }

    const group = await prisma.serverGroup.create({
      data: {
        name,
        description,
        color: color || '#3B82F6',
        userId: request.user.id,
      },
      include: {
        _count: {
          select: {
            members: true,
          },
        },
      },
    })

    return reply.status(201).send(group)
  } catch (error) {
    console.error('Erro ao criar grupo:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

// Atualizar grupo
export async function updateServerGroup(
  request: FastifyRequest<{
    Params: { id: string },
    Body: UpdateServerGroupDTO
  }>,
  reply: FastifyReply,
) {
  try {
    const { id } = request.params
    const { name, description, color } = request.body

    // Verificar se o grupo existe e pertence ao usuário
    const existingGroup = await prisma.serverGroup.findFirst({
      where: {
        id,
        userId: request.user.id,
      },
    })

    if (!existingGroup) {
      return reply.status(404).send({ error: 'Grupo não encontrado' })
    }

    // Se o nome está sendo alterado, verificar se não existe outro grupo com o mesmo nome
    if (name && name !== existingGroup.name) {
      const duplicateGroup = await prisma.serverGroup.findFirst({
        where: {
          name,
          userId: request.user.id,
          id: {
            not: id,
          },
        },
      })

      if (duplicateGroup) {
        return reply.status(400).send({
          error: 'Já existe um grupo com este nome'
        })
      }
    }

    const updatedGroup = await prisma.serverGroup.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
        ...(color && { color }),
      },
      include: {
        _count: {
          select: {
            members: true,
          },
        },
      },
    })

    return reply.send(updatedGroup)
  } catch (error) {
    console.error('Erro ao atualizar grupo:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

// Excluir grupo
export async function deleteServerGroup(
  request: FastifyRequest<{ Params: { id: string } }>,
  reply: FastifyReply,
) {
  try {
    const { id } = request.params

    // Verificar se o grupo existe e pertence ao usuário
    const group = await prisma.serverGroup.findFirst({
      where: {
        id,
        userId: request.user.id,
      },
    })

    if (!group) {
      return reply.status(404).send({ error: 'Grupo não encontrado' })
    }

    await prisma.serverGroup.delete({
      where: { id },
    })

    return reply.send({ message: 'Grupo excluído com sucesso' })
  } catch (error) {
    console.error('Erro ao excluir grupo:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

// Adicionar servidor ao grupo
export async function addServerToGroup(
  request: FastifyRequest<{
    Params: { id: string },
    Body: AddServerToGroupDTO
  }>,
  reply: FastifyReply,
) {
  try {
    const { id: groupId } = request.params
    const { serverId } = request.body

    // Verificar se o grupo existe e pertence ao usuário
    const group = await prisma.serverGroup.findFirst({
      where: {
        id: groupId,
        userId: request.user.id,
      },
    })

    if (!group) {
      return reply.status(404).send({ error: 'Grupo não encontrado' })
    }

    // Verificar se o servidor existe e o usuário tem acesso a ele
    const server = await prisma.server.findFirst({
      where: {
        id: serverId,
        OR: [
          { userId: request.user.id },
          {
            userAccess: {
              some: {
                userId: request.user.id,
              },
            },
          },
        ],
      },
    })

    if (!server) {
      return reply.status(404).send({
        error: 'Servidor não encontrado ou sem acesso'
      })
    }

    // Verificar se o servidor já está no grupo
    const existingMember = await prisma.serverGroupMember.findFirst({
      where: {
        groupId,
        serverId,
      },
    })

    if (existingMember) {
      return reply.status(400).send({
        error: 'Servidor já está neste grupo'
      })
    }

    // Adicionar servidor ao grupo
    const member = await prisma.serverGroupMember.create({
      data: {
        groupId,
        serverId,
      },
      include: {
        server: {
          select: {
            id: true,
            name: true,
            host: true,
            port: true,
            deviceType: true,
          },
        },
      },
    })

    return reply.status(201).send(member)
  } catch (error) {
    console.error('Erro ao adicionar servidor ao grupo:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

// Remover servidor do grupo
export async function removeServerFromGroup(
  request: FastifyRequest<{
    Params: { id: string, serverId: string }
  }>,
  reply: FastifyReply,
) {
  try {
    const { id: groupId, serverId } = request.params

    // Verificar se o grupo existe e pertence ao usuário
    const group = await prisma.serverGroup.findFirst({
      where: {
        id: groupId,
        userId: request.user.id,
      },
    })

    if (!group) {
      return reply.status(404).send({ error: 'Grupo não encontrado' })
    }

    // Verificar se o servidor está no grupo
    const member = await prisma.serverGroupMember.findFirst({
      where: {
        groupId,
        serverId,
      },
    })

    if (!member) {
      return reply.status(404).send({
        error: 'Servidor não está neste grupo'
      })
    }

    // Remover servidor do grupo
    await prisma.serverGroupMember.delete({
      where: {
        id: member.id,
      },
    })

    return reply.send({ message: 'Servidor removido do grupo com sucesso' })
  } catch (error) {
    console.error('Erro ao remover servidor do grupo:', error)
    return reply.status(500).send({ error: 'Erro interno do servidor' })
  }
}

@echo off
REM RemoteOps Windows Automation Script
REM Automatically sets up and runs RemoteOps on Windows

setlocal enabledelayedexpansion

echo.
echo ========================================
echo    RemoteOps Windows Setup Script
echo ========================================
echo.

REM Colors for output (Windows 10+)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "RESET=[0m"

REM Configuration
set "PROJECT_NAME=remoteops"
set "NODE_VERSION=18"
set "PYTHON_VERSION=3.11"

echo %BLUE%[INFO]%RESET% Starting RemoteOps setup for Windows...

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%[ERROR]%RESET% This script requires administrator privileges.
    echo Please run as administrator.
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%RESET% Running with administrator privileges

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo %BLUE%[INFO]%RESET% Windows version: %VERSION%

REM Function to check if command exists
:check_command
where %1 >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%[ERROR]%RESET% %1 is not installed or not in PATH
    exit /b 1
) else (
    echo %GREEN%[SUCCESS]%RESET% %1 is available
)
goto :eof

REM Check prerequisites
echo.
echo %BLUE%[INFO]%RESET% Checking prerequisites...

REM Check if Chocolatey is installed
where choco >nul 2>&1
if %errorLevel% neq 0 (
    echo %YELLOW%[WARNING]%RESET% Chocolatey not found. Installing...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    if !errorLevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install Chocolatey
        pause
        exit /b 1
    )
    echo %GREEN%[SUCCESS]%RESET% Chocolatey installed successfully
    REM Refresh environment variables
    call refreshenv
)

REM Install Node.js if not present
where node >nul 2>&1
if %errorLevel% neq 0 (
    echo %YELLOW%[WARNING]%RESET% Node.js not found. Installing...
    choco install nodejs --version=%NODE_VERSION% -y
    if !errorLevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install Node.js
        pause
        exit /b 1
    )
    call refreshenv
)

REM Install Python if not present
where python >nul 2>&1
if %errorLevel% neq 0 (
    echo %YELLOW%[WARNING]%RESET% Python not found. Installing...
    choco install python --version=%PYTHON_VERSION% -y
    if !errorLevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install Python
        pause
        exit /b 1
    )
    call refreshenv
)

REM Install Git if not present
where git >nul 2>&1
if %errorLevel% neq 0 (
    echo %YELLOW%[WARNING]%RESET% Git not found. Installing...
    choco install git -y
    if !errorLevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install Git
        pause
        exit /b 1
    )
    call refreshenv
)

REM Install Docker Desktop if not present
where docker >nul 2>&1
if %errorLevel% neq 0 (
    echo %YELLOW%[WARNING]%RESET% Docker not found. Installing Docker Desktop...
    choco install docker-desktop -y
    if !errorLevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install Docker Desktop
        echo Please install Docker Desktop manually from https://www.docker.com/products/docker-desktop
        pause
        exit /b 1
    )
    echo %YELLOW%[WARNING]%RESET% Docker Desktop installed. Please restart your computer and run this script again.
    pause
    exit /b 0
)

REM Install PostgreSQL if not present
where psql >nul 2>&1
if %errorLevel% neq 0 (
    echo %YELLOW%[WARNING]%RESET% PostgreSQL not found. Installing...
    choco install postgresql --params '/Password:password' -y
    if !errorLevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install PostgreSQL
        pause
        exit /b 1
    )
    call refreshenv
)

REM Install Redis if not present
where redis-server >nul 2>&1
if %errorLevel% neq 0 (
    echo %YELLOW%[WARNING]%RESET% Redis not found. Installing...
    choco install redis-64 -y
    if !errorLevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install Redis
        pause
        exit /b 1
    )
    call refreshenv
)

echo %GREEN%[SUCCESS]%RESET% All prerequisites installed

REM Setup project
echo.
echo %BLUE%[INFO]%RESET% Setting up RemoteOps project...

REM Create project directory if it doesn't exist
if not exist "C:\RemoteOps" (
    mkdir "C:\RemoteOps"
    echo %GREEN%[SUCCESS]%RESET% Created project directory: C:\RemoteOps
)

cd /d "C:\RemoteOps"

REM Check if project already exists
if not exist "package.json" (
    echo %BLUE%[INFO]%RESET% Initializing new RemoteOps project...
    
    REM Copy project files (assuming script is run from project directory)
    xcopy "%~dp0\.." . /E /I /Y >nul 2>&1
    if !errorLevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to copy project files
        echo Please ensure you're running this script from the RemoteOps project directory
        pause
        exit /b 1
    )
)

REM Install dependencies
echo %BLUE%[INFO]%RESET% Installing dependencies...

REM Backend dependencies
if exist "backend\package.json" (
    cd backend
    echo %BLUE%[INFO]%RESET% Installing backend dependencies...
    call npm install
    if !errorLevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install backend dependencies
        pause
        exit /b 1
    )
    cd ..
)

REM Frontend dependencies
if exist "frontend\package.json" (
    cd frontend
    echo %BLUE%[INFO]%RESET% Installing frontend dependencies...
    call npm install
    if !errorLevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install frontend dependencies
        pause
        exit /b 1
    )
    cd ..
)

REM Python dependencies
if exist "python-microservice\requirements.txt" (
    cd python-microservice
    echo %BLUE%[INFO]%RESET% Installing Python dependencies...
    python -m pip install -r requirements.txt
    if !errorLevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to install Python dependencies
        pause
        exit /b 1
    )
    cd ..
)

echo %GREEN%[SUCCESS]%RESET% Dependencies installed

REM Setup environment
echo %BLUE%[INFO]%RESET% Setting up environment...

if not exist ".env" (
    echo %BLUE%[INFO]%RESET% Creating environment file...
    (
        echo NODE_ENV=development
        echo JWT_SECRET=%RANDOM%%RANDOM%%RANDOM%
        echo DATABASE_URL=postgresql://postgres:password@localhost:5432/remoteops
        echo REDIS_URL=redis://localhost:6379
        echo PORT=3000
        echo FRONTEND_URL=http://localhost:3001
        echo PYTHON_MICROSERVICE_URL=http://localhost:8000
    ) > .env
    echo %GREEN%[SUCCESS]%RESET% Environment file created
)

REM Setup database
echo %BLUE%[INFO]%RESET% Setting up database...

REM Start PostgreSQL service
net start postgresql-x64-14 >nul 2>&1

REM Create database
psql -U postgres -c "CREATE DATABASE remoteops;" >nul 2>&1
if %errorLevel% equ 0 (
    echo %GREEN%[SUCCESS]%RESET% Database created
) else (
    echo %YELLOW%[WARNING]%RESET% Database might already exist
)

REM Run migrations
if exist "backend\prisma\schema.prisma" (
    cd backend
    echo %BLUE%[INFO]%RESET% Running database migrations...
    call npx prisma migrate dev --name init
    if !errorLevel! neq 0 (
        echo %RED%[ERROR]%RESET% Failed to run migrations
        pause
        exit /b 1
    )
    cd ..
)

REM Start Redis service
net start Redis >nul 2>&1

echo %GREEN%[SUCCESS]%RESET% Services started

REM Start applications
echo.
echo %BLUE%[INFO]%RESET% Starting RemoteOps applications...

REM Start backend in new window
start "RemoteOps Backend" cmd /k "cd /d C:\RemoteOps\backend && npm run dev"

REM Wait a bit for backend to start
timeout /t 5 /nobreak >nul

REM Start frontend in new window
start "RemoteOps Frontend" cmd /k "cd /d C:\RemoteOps\frontend && npm run dev"

REM Start Python microservice in new window
start "RemoteOps Python" cmd /k "cd /d C:\RemoteOps\python-microservice && python main.py"

echo.
echo %GREEN%[SUCCESS]%RESET% RemoteOps is starting up!
echo.
echo %BLUE%[INFO]%RESET% Application URLs:
echo   Frontend: http://localhost:3001
echo   Backend API: http://localhost:3000
echo   Python Microservice: http://localhost:8000
echo.
echo %BLUE%[INFO]%RESET% Default credentials:
echo   Email: <EMAIL>
echo   Password: admin123
echo.
echo %YELLOW%[WARNING]%RESET% Please wait a few moments for all services to start...

REM Wait for services to be ready
echo %BLUE%[INFO]%RESET% Waiting for services to be ready...
timeout /t 15 /nobreak >nul

REM Open browser
echo %BLUE%[INFO]%RESET% Opening RemoteOps in your default browser...
start http://localhost:3001

echo.
echo %GREEN%[SUCCESS]%RESET% RemoteOps setup completed successfully!
echo.
echo Press any key to exit...
pause >nul

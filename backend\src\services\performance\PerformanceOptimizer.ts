import { Logger } from '../../utils/logger';
import { SSHServer } from '../../types/server';

interface ConnectionPool {
  [serverId: string]: {
    connection: any;
    lastUsed: number;
    inUse: boolean;
    connectionCount: number;
  };
}

interface PerformanceMetrics {
  serverId: string;
  averageConnectionTime: number;
  averageExecutionTime: number;
  successRate: number;
  lastOptimized: number;
  recommendedSettings: {
    timeout: number;
    keepalive: number;
    maxRetries: number;
    connectionPoolSize: number;
  };
}

interface OptimizationRule {
  condition: (metrics: PerformanceMetrics) => boolean;
  action: (metrics: PerformanceMetrics) => Partial<PerformanceMetrics['recommendedSettings']>;
  description: string;
}

/**
 * Otimizador de performance para conexões SSH
 * Analisa métricas e ajusta configurações automaticamente
 */
export class PerformanceOptimizer {
  private connectionPool: ConnectionPool = {};
  private performanceMetrics: Map<string, PerformanceMetrics> = new Map();
  private optimizationRules: OptimizationRule[] = [];
  private maxPoolSize = 10;
  private connectionTTL = 300000; // 5 minutos
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.initializeOptimizationRules();
    this.startCleanupTimer();
    Logger.log('PerformanceOptimizer inicializado');
  }

  /**
   * Inicializa regras de otimização
   */
  private initializeOptimizationRules(): void {
    this.optimizationRules = [
      // Regra para dispositivos lentos
      {
        condition: (metrics) => metrics.averageExecutionTime > 30000, // > 30s
        action: (metrics) => ({
          timeout: Math.min(metrics.recommendedSettings.timeout * 1.5, 120),
          keepalive: Math.max(metrics.recommendedSettings.keepalive * 0.8, 5)
        }),
        description: 'Aumentar timeout para dispositivos lentos'
      },

      // Regra para dispositivos instáveis
      {
        condition: (metrics) => metrics.successRate < 0.8,
        action: (metrics) => ({
          maxRetries: Math.min(metrics.recommendedSettings.maxRetries + 1, 5),
          timeout: Math.min(metrics.recommendedSettings.timeout * 1.2, 90)
        }),
        description: 'Aumentar tentativas para dispositivos instáveis'
      },

      // Regra para dispositivos rápidos e estáveis
      {
        condition: (metrics) => metrics.successRate > 0.95 && metrics.averageExecutionTime < 10000,
        action: (metrics) => ({
          timeout: Math.max(metrics.recommendedSettings.timeout * 0.9, 30),
          connectionPoolSize: Math.min(metrics.recommendedSettings.connectionPoolSize + 1, 5)
        }),
        description: 'Otimizar para dispositivos rápidos'
      },

      // Regra para dispositivos com alta latência de conexão
      {
        condition: (metrics) => metrics.averageConnectionTime > 15000, // > 15s
        action: (metrics) => ({
          connectionPoolSize: Math.min(metrics.recommendedSettings.connectionPoolSize + 1, 3),
          keepalive: Math.min(metrics.recommendedSettings.keepalive * 1.2, 30)
        }),
        description: 'Manter conexões ativas para dispositivos com alta latência'
      },

      // Regra para dispositivos Mikrotik
      {
        condition: (metrics) => metrics.serverId.includes('mikrotik') || metrics.serverId.includes('routeros'),
        action: (metrics) => ({
          keepalive: 15,
          timeout: 60,
          maxRetries: 3
        }),
        description: 'Configurações otimizadas para Mikrotik'
      },

      // Regra para dispositivos Huawei
      {
        condition: (metrics) => metrics.serverId.includes('huawei') || metrics.serverId.includes('harmony'),
        action: (metrics) => ({
          timeout: 90,
          keepalive: 10,
          maxRetries: 2
        }),
        description: 'Configurações otimizadas para Huawei'
      }
    ];
  }

  /**
   * Registra métricas de performance para um servidor
   */
  recordPerformanceMetrics(
    serverId: string,
    connectionTime: number,
    executionTime: number,
    success: boolean
  ): void {
    let metrics = this.performanceMetrics.get(serverId);
    
    if (!metrics) {
      metrics = {
        serverId,
        averageConnectionTime: connectionTime,
        averageExecutionTime: executionTime,
        successRate: success ? 1 : 0,
        lastOptimized: 0,
        recommendedSettings: {
          timeout: 60,
          keepalive: 30,
          maxRetries: 3,
          connectionPoolSize: 2
        }
      };
    } else {
      // Calcular médias móveis (peso maior para valores recentes)
      const alpha = 0.3; // Fator de suavização
      metrics.averageConnectionTime = (1 - alpha) * metrics.averageConnectionTime + alpha * connectionTime;
      metrics.averageExecutionTime = (1 - alpha) * metrics.averageExecutionTime + alpha * executionTime;
      metrics.successRate = (1 - alpha) * metrics.successRate + alpha * (success ? 1 : 0);
    }

    this.performanceMetrics.set(serverId, metrics);

    // Verificar se precisa otimizar (a cada 10 execuções ou 1 hora)
    const now = Date.now();
    if (now - metrics.lastOptimized > 3600000) { // 1 hora
      this.optimizeServerSettings(serverId);
    }
  }

  /**
   * Otimiza configurações para um servidor específico
   */
  private optimizeServerSettings(serverId: string): void {
    const metrics = this.performanceMetrics.get(serverId);
    if (!metrics) return;

    const originalSettings = { ...metrics.recommendedSettings };
    let optimizationsApplied: string[] = [];

    // Aplicar regras de otimização
    for (const rule of this.optimizationRules) {
      if (rule.condition(metrics)) {
        const newSettings = rule.action(metrics);
        Object.assign(metrics.recommendedSettings, newSettings);
        optimizationsApplied.push(rule.description);
      }
    }

    metrics.lastOptimized = Date.now();

    if (optimizationsApplied.length > 0) {
      Logger.log(`Configurações otimizadas para servidor ${serverId}:`);
      Logger.log(`- Aplicadas: ${optimizationsApplied.join(', ')}`);
      Logger.log(`- Antes: ${JSON.stringify(originalSettings)}`);
      Logger.log(`- Depois: ${JSON.stringify(metrics.recommendedSettings)}`);
    }
  }

  /**
   * Obtém configurações otimizadas para um servidor
   */
  getOptimizedSettings(serverId: string, deviceType?: string): {
    timeout: number;
    keepalive: number;
    maxRetries: number;
    connectionPoolSize: number;
  } {
    const metrics = this.performanceMetrics.get(serverId);
    
    if (metrics) {
      return { ...metrics.recommendedSettings };
    }

    // Configurações padrão baseadas no tipo de dispositivo
    const defaultSettings = {
      timeout: 60,
      keepalive: 30,
      maxRetries: 3,
      connectionPoolSize: 2
    };

    if (deviceType) {
      switch (deviceType.toUpperCase()) {
        case 'HUAWEI':
          return { ...defaultSettings, timeout: 90, keepalive: 10 };
        case 'MIKROTIK':
          return { ...defaultSettings, timeout: 60, keepalive: 15 };
        case 'NOKIA':
          return { ...defaultSettings, timeout: 75, keepalive: 20 };
        case 'DMOS':
          return { ...defaultSettings, timeout: 60, keepalive: 25 };
      }
    }

    return defaultSettings;
  }

  /**
   * Obtém estatísticas de performance
   */
  getPerformanceStats(): Array<{
    serverId: string;
    averageConnectionTime: number;
    averageExecutionTime: number;
    successRate: number;
    recommendedSettings: any;
    optimizationStatus: 'optimal' | 'needs_optimization' | 'problematic';
  }> {
    const stats: Array<any> = [];

    this.performanceMetrics.forEach((metrics, serverId) => {
      let optimizationStatus: 'optimal' | 'needs_optimization' | 'problematic' = 'optimal';

      if (metrics.successRate < 0.7 || metrics.averageExecutionTime > 60000) {
        optimizationStatus = 'problematic';
      } else if (metrics.successRate < 0.9 || metrics.averageExecutionTime > 30000) {
        optimizationStatus = 'needs_optimization';
      }

      stats.push({
        serverId,
        averageConnectionTime: Math.round(metrics.averageConnectionTime),
        averageExecutionTime: Math.round(metrics.averageExecutionTime),
        successRate: Math.round(metrics.successRate * 100) / 100,
        recommendedSettings: metrics.recommendedSettings,
        optimizationStatus
      });
    });

    return stats.sort((a, b) => {
      // Ordenar por status (problemáticos primeiro)
      const statusOrder = { 'problematic': 0, 'needs_optimization': 1, 'optimal': 2 };
      return statusOrder[a.optimizationStatus] - statusOrder[b.optimizationStatus];
    });
  }

  /**
   * Força otimização para todos os servidores
   */
  optimizeAllServers(): void {
    Logger.log('Iniciando otimização forçada para todos os servidores');
    
    this.performanceMetrics.forEach((metrics, serverId) => {
      this.optimizeServerSettings(serverId);
    });
    
    Logger.log(`Otimização concluída para ${this.performanceMetrics.size} servidores`);
  }

  /**
   * Remove métricas antigas
   */
  private cleanup(): void {
    const now = Date.now();
    const maxAge = 7 * 24 * 3600000; // 7 dias

    this.performanceMetrics.forEach((metrics, serverId) => {
      if (now - metrics.lastOptimized > maxAge) {
        this.performanceMetrics.delete(serverId);
        Logger.log(`Métricas antigas removidas para servidor ${serverId}`);
      }
    });
  }

  /**
   * Inicia timer de limpeza
   */
  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 3600000); // 1 hora
  }

  /**
   * Para o timer de limpeza
   */
  stopCleanupTimer(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * Obtém recomendações de otimização
   */
  getOptimizationRecommendations(): Array<{
    serverId: string;
    priority: 'high' | 'medium' | 'low';
    issue: string;
    recommendation: string;
    expectedImprovement: string;
  }> {
    const recommendations: Array<any> = [];

    this.performanceMetrics.forEach((metrics, serverId) => {
      if (metrics.successRate < 0.7) {
        recommendations.push({
          serverId,
          priority: 'high',
          issue: `Taxa de sucesso muito baixa (${(metrics.successRate * 100).toFixed(1)}%)`,
          recommendation: 'Verificar conectividade e credenciais. Considerar aumentar timeout e tentativas.',
          expectedImprovement: 'Aumento significativo na confiabilidade'
        });
      }

      if (metrics.averageExecutionTime > 60000) {
        recommendations.push({
          serverId,
          priority: 'high',
          issue: `Tempo de execução muito alto (${(metrics.averageExecutionTime / 1000).toFixed(1)}s)`,
          recommendation: 'Otimizar comandos ou considerar usar cache para comandos frequentes.',
          expectedImprovement: 'Redução de 30-50% no tempo de resposta'
        });
      }

      if (metrics.averageConnectionTime > 20000) {
        recommendations.push({
          serverId,
          priority: 'medium',
          issue: `Tempo de conexão alto (${(metrics.averageConnectionTime / 1000).toFixed(1)}s)`,
          recommendation: 'Implementar pool de conexões ou verificar latência de rede.',
          expectedImprovement: 'Redução de 50-70% no tempo de conexão'
        });
      }
    });

    return recommendations.sort((a, b) => {
      const priorityOrder = { 'high': 0, 'medium': 1, 'low': 2 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });
  }
}

import { useState, useEffect } from 'react';
import { useTheme, Breakpoint } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';

export interface ScreenSize {
  width: number;
  height: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isSmall: boolean;
  isMedium: boolean;
  isLarge: boolean;
  isXLarge: boolean;
}

export const useResponsive = (): ScreenSize => {
  const theme = useTheme();
  
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const isTablet = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
  
  const isSmall = useMediaQuery(theme.breakpoints.down('sm'));
  const isMedium = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const isLarge = useMediaQuery(theme.breakpoints.between('md', 'lg'));
  const isXLarge = useMediaQuery(theme.breakpoints.up('xl'));

  const [screenSize, setScreenSize] = useState<ScreenSize>({
    width: window.innerWidth,
    height: window.innerHeight,
    isMobile,
    isTablet,
    isDesktop,
    isSmall,
    isMedium,
    isLarge,
    isXLarge
  });

  useEffect(() => {
    const handleResize = () => {
      setScreenSize(prev => ({
        ...prev,
        width: window.innerWidth,
        height: window.innerHeight
      }));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    setScreenSize(prev => ({
      ...prev,
      isMobile,
      isTablet,
      isDesktop,
      isSmall,
      isMedium,
      isLarge,
      isXLarge
    }));
  }, [isMobile, isTablet, isDesktop, isSmall, isMedium, isLarge, isXLarge]);

  return screenSize;
};

// Hook para breakpoints específicos
export const useBreakpoint = (breakpoint: Breakpoint, direction: 'up' | 'down' | 'only' = 'up') => {
  const theme = useTheme();
  
  const query = direction === 'up' 
    ? theme.breakpoints.up(breakpoint)
    : direction === 'down'
    ? theme.breakpoints.down(breakpoint)
    : theme.breakpoints.only(breakpoint);
    
  return useMediaQuery(query);
};

// Hook para orientação da tela
export const useOrientation = () => {
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>(() => {
    return window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
  });

  useEffect(() => {
    const handleOrientationChange = () => {
      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');
    };

    window.addEventListener('resize', handleOrientationChange);
    window.addEventListener('orientationchange', handleOrientationChange);
    
    return () => {
      window.removeEventListener('resize', handleOrientationChange);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  return orientation;
};

// Hook para detectar se é um dispositivo touch
export const useTouch = () => {
  const [isTouch, setIsTouch] = useState(() => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  });

  useEffect(() => {
    const checkTouch = () => {
      setIsTouch('ontouchstart' in window || navigator.maxTouchPoints > 0);
    };

    // Verificar novamente após um tempo para dispositivos híbridos
    const timer = setTimeout(checkTouch, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  return isTouch;
};

// Hook para container responsivo
export const useResponsiveContainer = () => {
  const { isMobile, isTablet, isDesktop } = useResponsive();

  const getContainerProps = () => {
    if (isMobile) {
      return {
        maxWidth: 'sm' as const,
        padding: 1,
        spacing: 2
      };
    }
    
    if (isTablet) {
      return {
        maxWidth: 'md' as const,
        padding: 2,
        spacing: 3
      };
    }
    
    return {
      maxWidth: 'lg' as const,
      padding: 3,
      spacing: 4
    };
  };

  const getGridProps = () => {
    if (isMobile) {
      return {
        xs: 12,
        sm: 12,
        md: 6,
        spacing: 2
      };
    }
    
    if (isTablet) {
      return {
        xs: 12,
        sm: 6,
        md: 4,
        spacing: 3
      };
    }
    
    return {
      xs: 12,
      sm: 6,
      md: 4,
      lg: 3,
      spacing: 4
    };
  };

  const getDialogProps = () => {
    if (isMobile) {
      return {
        fullScreen: true,
        maxWidth: false as const
      };
    }
    
    return {
      fullScreen: false,
      maxWidth: 'md' as const
    };
  };

  return {
    isMobile,
    isTablet,
    isDesktop,
    getContainerProps,
    getGridProps,
    getDialogProps
  };
};

// Utilitários para classes CSS responsivas
export const responsiveClasses = {
  // Padding responsivo
  padding: {
    mobile: 'p-2',
    tablet: 'p-4',
    desktop: 'p-6'
  },
  
  // Margin responsivo
  margin: {
    mobile: 'm-2',
    tablet: 'm-4',
    desktop: 'm-6'
  },
  
  // Grid responsivo
  grid: {
    mobile: 'grid-cols-1',
    tablet: 'grid-cols-2',
    desktop: 'grid-cols-3'
  },
  
  // Texto responsivo
  text: {
    mobile: 'text-sm',
    tablet: 'text-base',
    desktop: 'text-lg'
  },
  
  // Altura responsiva
  height: {
    mobile: 'h-48',
    tablet: 'h-64',
    desktop: 'h-80'
  }
};

export default useResponsive;

import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  TextField,
  Paper,
  Typography,
  Chip,
  Autocomplete,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Code as CodeIcon,
  History as HistoryIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  ContentCopy as CopyIcon,
  FormatSize as FormatIcon
} from '@mui/icons-material';
import { useTheme } from '../contexts/ThemeContext';

interface CommandEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  deviceType?: string;
  multiline?: boolean;
  minRows?: number;
  maxRows?: number;
  showSuggestions?: boolean;
  showHistory?: boolean;
  commandHistory?: string[];
  favorites?: string[];
  onAddToFavorites?: (command: string) => void;
  onRemoveFromFavorites?: (command: string) => void;
}

// Comandos comuns por tipo de dispositivo
const COMMAND_SUGGESTIONS = {
  MIKROTIK: [
    '/system resource print',
    '/interface print',
    '/ip address print',
    '/ip route print',
    '/system identity print',
    '/system clock print',
    '/log print',
    '/interface wireless print',
    '/queue simple print',
    '/firewall filter print'
  ],
  HUAWEI: [
    'display version',
    'display interface brief',
    'display ip interface brief',
    'display current-configuration',
    'display device',
    'display alarm active',
    'display cpu-usage',
    'display memory-usage',
    'display temperature',
    'display fan'
  ],
  NOKIA: [
    'show version',
    'show port',
    'show system',
    'show log',
    'show chassis',
    'show card',
    'show service',
    'admin display-config',
    'show router interface',
    'show router route-table'
  ],
  DMOS: [
    'show version',
    'show interface',
    'show system',
    'show log',
    'show status',
    'show config',
    'show users',
    'show processes',
    'show memory',
    'show cpu'
  ],
  GENERIC: [
    'ls -la',
    'ps aux',
    'df -h',
    'free -h',
    'top',
    'netstat -tulpn',
    'systemctl status',
    'journalctl -f',
    'cat /proc/version',
    'uname -a'
  ]
};

// Palavras-chave para syntax highlighting
const KEYWORDS = {
  MIKROTIK: ['print', 'add', 'set', 'remove', 'enable', 'disable', 'export', 'import'],
  HUAWEI: ['display', 'show', 'configure', 'commit', 'save', 'undo', 'quit'],
  NOKIA: ['show', 'configure', 'admin', 'exit', 'commit', 'compare'],
  DMOS: ['show', 'configure', 'set', 'delete', 'commit', 'save'],
  GENERIC: ['ls', 'cd', 'pwd', 'cat', 'grep', 'awk', 'sed', 'ps', 'top', 'df', 'free']
};

export const CommandEditor: React.FC<CommandEditorProps> = ({
  value,
  onChange,
  placeholder = 'Digite seu comando...',
  deviceType = 'GENERIC',
  multiline = true,
  minRows = 3,
  maxRows = 10,
  showSuggestions = true,
  showHistory = true,
  commandHistory = [],
  favorites = [],
  onAddToFavorites,
  onRemoveFromFavorites
}) => {
  const { mode } = useTheme();
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSyntaxHighlight, setShowSyntaxHighlight] = useState(true);
  const [fontSize, setFontSize] = useState(14);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const textFieldRef = useRef<HTMLTextAreaElement>(null);

  const deviceSuggestions = COMMAND_SUGGESTIONS[deviceType as keyof typeof COMMAND_SUGGESTIONS] || COMMAND_SUGGESTIONS.GENERIC;
  const keywords = KEYWORDS[deviceType as keyof typeof KEYWORDS] || KEYWORDS.GENERIC;

  // Combinar sugestões de comandos, histórico e favoritos
  useEffect(() => {
    const allSuggestions = [
      ...new Set([
        ...deviceSuggestions,
        ...commandHistory.slice(0, 10), // Últimos 10 comandos do histórico
        ...favorites
      ])
    ];
    setSuggestions(allSuggestions);
  }, [deviceType, commandHistory, favorites]);

  const handleCopy = () => {
    navigator.clipboard.writeText(value);
  };

  const handleAddToFavorites = () => {
    if (value.trim() && onAddToFavorites) {
      onAddToFavorites(value.trim());
    }
  };

  const handleRemoveFromFavorites = () => {
    if (value.trim() && onRemoveFromFavorites) {
      onRemoveFromFavorites(value.trim());
    }
  };

  const isFavorite = favorites.includes(value.trim());

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleFontSizeChange = (newSize: number) => {
    setFontSize(newSize);
    handleMenuClose();
  };

  // Aplicar syntax highlighting (simulado com estilos)
  const getHighlightedText = (text: string) => {
    if (!showSyntaxHighlight) return text;

    let highlightedText = text;
    
    // Destacar palavras-chave
    keywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
      highlightedText = highlightedText.replace(regex, `<span style="color: #2196f3; font-weight: bold;">${keyword}</span>`);
    });

    // Destacar strings entre aspas
    highlightedText = highlightedText.replace(/"([^"]*)"/g, '<span style="color: #4caf50;">"$1"</span>');
    
    // Destacar números
    highlightedText = highlightedText.replace(/\b\d+\b/g, '<span style="color: #ff9800;">$&</span>');

    return highlightedText;
  };

  return (
    <Box>
      {/* Barra de ferramentas */}
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        gap: 1, 
        mb: 1,
        p: 1,
        backgroundColor: mode === 'dark' ? '#2a2a2a' : '#f5f5f5',
        borderRadius: 1
      }}>
        <Chip 
          icon={<CodeIcon />} 
          label={deviceType} 
          size="small" 
          color="primary" 
        />
        
        <Box sx={{ flexGrow: 1 }} />
        
        <Tooltip title="Copiar comando">
          <IconButton size="small" onClick={handleCopy}>
            <CopyIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        
        {isFavorite ? (
          <Tooltip title="Remover dos favoritos">
            <IconButton size="small" onClick={handleRemoveFromFavorites} color="warning">
              <StarIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        ) : (
          <Tooltip title="Adicionar aos favoritos">
            <IconButton size="small" onClick={handleAddToFavorites}>
              <StarBorderIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
        
        <Tooltip title="Opções do editor">
          <IconButton size="small" onClick={handleMenuClick}>
            <FormatIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Menu de opções */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => setShowSyntaxHighlight(!showSyntaxHighlight)}>
          <ListItemIcon>
            <CodeIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>
            {showSyntaxHighlight ? 'Desabilitar' : 'Habilitar'} Syntax Highlighting
          </ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => handleFontSizeChange(12)}>
          <ListItemText>Fonte Pequena (12px)</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => handleFontSizeChange(14)}>
          <ListItemText>Fonte Média (14px)</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => handleFontSizeChange(16)}>
          <ListItemText>Fonte Grande (16px)</ListItemText>
        </MenuItem>
      </Menu>

      {/* Editor principal */}
      {showSuggestions ? (
        <Autocomplete
          freeSolo
          multiple={false}
          options={suggestions}
          value={value}
          onInputChange={(_, newValue) => onChange(newValue || '')}
          renderInput={(params) => (
            <TextField
              {...params}
              ref={textFieldRef}
              multiline={multiline}
              minRows={minRows}
              maxRows={maxRows}
              placeholder={placeholder}
              variant="outlined"
              fullWidth
              InputProps={{
                ...params.InputProps,
                style: {
                  fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                  fontSize: `${fontSize}px`,
                  lineHeight: 1.5
                }
              }}
            />
          )}
          renderOption={(props, option) => (
            <Box component="li" {...props}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                {favorites.includes(option) && <StarIcon fontSize="small" color="warning" />}
                {commandHistory.includes(option) && <HistoryIcon fontSize="small" color="action" />}
                <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                  {option}
                </Typography>
              </Box>
            </Box>
          )}
        />
      ) : (
        <TextField
          ref={textFieldRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          multiline={multiline}
          minRows={minRows}
          maxRows={maxRows}
          placeholder={placeholder}
          variant="outlined"
          fullWidth
          InputProps={{
            style: {
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              fontSize: `${fontSize}px`,
              lineHeight: 1.5
            }
          }}
        />
      )}

      {/* Preview com syntax highlighting */}
      {showSyntaxHighlight && value && (
        <Paper 
          sx={{ 
            mt: 1, 
            p: 2, 
            backgroundColor: mode === 'dark' ? '#1e1e1e' : '#fafafa',
            border: `1px solid ${mode === 'dark' ? '#333' : '#e0e0e0'}`
          }}
        >
          <Typography variant="caption" color="text.secondary" gutterBottom>
            Preview com Syntax Highlighting:
          </Typography>
          <Box
            sx={{
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              fontSize: `${fontSize}px`,
              lineHeight: 1.5,
              whiteSpace: 'pre-wrap'
            }}
            dangerouslySetInnerHTML={{ __html: getHighlightedText(value) }}
          />
        </Paper>
      )}
    </Box>
  );
};

export default CommandEditor;

import React from 'react'
import { Browser<PERSON>outer, Routes, Route, Navigate } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import { ThemeProvider } from './contexts/ThemeContext'
import { Login } from './pages/Login'
import { Register } from './pages/Register'
import { Servers } from './pages/Servers'
import { CommandHistory } from './pages/CommandHistory'
import { CommandTemplates } from './pages/CommandTemplates'
import { PrivateLayout } from './components/PrivateLayout'
import { Toaster } from 'react-hot-toast'
import { UsersPage } from './pages/Users'
import { ProfilePage } from './pages/Profile'
import { Monitoring } from './pages/Monitoring'
import Audit from './pages/Audit'
import { CacheManagement } from './pages/CacheManagement'
import Marketplace from './pages/Marketplace'
import Organization from './pages/Organization'
import { OrganizationProvider } from './hooks/useOrganization'

const queryClient = new QueryClient()

// Componente para verificar se o usuário é administrador
function AdminRoute({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()

  if (user?.role !== 'ADMIN') {
    return <Navigate to="/" replace />
  }

  return <>{children}</>
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <BrowserRouter>
          <AuthProvider>
            <OrganizationProvider>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/" element={<PrivateLayout />}>
              <Route index element={<Servers />} />
              <Route path="command-history" element={<CommandHistory />} />
              <Route path="command-history/page/:page" element={<CommandHistory />} />
              <Route
                path="command-templates"
                element={
                  <AdminRoute>
                    <CommandTemplates />
                  </AdminRoute>
                }
              />
              <Route
                path="users"
                element={
                  <AdminRoute>
                    <UsersPage />
                  </AdminRoute>
                }
              />
              <Route
                path="monitoring"
                element={
                  <AdminRoute>
                    <Monitoring />
                  </AdminRoute>
                }
              />
              <Route
                path="audit"
                element={
                  <AdminRoute>
                    <Audit />
                  </AdminRoute>
                }
              />
              <Route
                path="cache-management"
                element={
                  <AdminRoute>
                    <CacheManagement />
                  </AdminRoute>
                }
              />
              <Route path="marketplace" element={<Marketplace />} />
              <Route path="organization" element={<Organization />} />
              <Route path="profile" element={<ProfilePage />} />
            </Route>
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
            </OrganizationProvider>
          </AuthProvider>
        </BrowserRouter>
        <Toaster position="top-right" />
      </ThemeProvider>
    </QueryClientProvider>
  )
}

export default App
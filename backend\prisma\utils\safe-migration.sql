-- Exemplo de como tornar operações de migração mais seguras
-- Copie e adapte estes snippets para suas migrações futuras

-- Remover índice de forma segura
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'nome_do_indice') THEN
        DROP INDEX "nome_do_indice";
    END IF;
END
$$;

-- Remover coluna de forma segura
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'nome_da_tabela' AND column_name = 'nome_da_coluna'
    ) THEN
        ALTER TABLE "nome_da_tabela" DROP COLUMN "nome_da_coluna";
    END IF;
END
$$;

-- Adicionar coluna de forma segura
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'nome_da_tabela' AND column_name = 'nome_da_coluna'
    ) THEN
        ALTER TABLE "nome_da_tabela" ADD COLUMN "nome_da_coluna" TIPO DEFAULT valor;
    END IF;
END
$$;

-- Criar índice de forma segura
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'nome_do_indice') THEN
        CREATE INDEX "nome_do_indice" ON "nome_da_tabela"("nome_da_coluna");
    END IF;
END
$$;

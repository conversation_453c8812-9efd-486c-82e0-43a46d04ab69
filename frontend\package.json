{"name": "remoteops-frontend", "private": true, "version": "2.0.0", "description": "RemoteOps - Frontend da plataforma de gerenciamento de infraestrutura remota", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@hookform/resolvers": "^4.1.2", "@phosphor-icons/react": "^2.1.7", "@tanstack/react-query": "^5.66.9", "@types/react-beautiful-dnd": "^13.1.8", "axios": "^1.6.7", "lucide-react": "^0.475.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.29.0", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.17.19", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.7.3", "vite": "^5.1.0"}}
export interface ServerGroup {
  id: string
  name: string
  description?: string
  color?: string
  userId: string
  createdAt: string
  updatedAt: string
  members?: ServerGroupMember[]
  _count?: {
    members: number
  }
}

export interface ServerGroupMember {
  id: string
  groupId: string
  serverId: string
  createdAt: string
  updatedAt: string
  group?: ServerGroup
  server?: {
    id: string
    name: string
    host: string
    port: number
    deviceType: string
  }
}

export interface CreateServerGroupDTO {
  name: string
  description?: string
  color?: string
}

export interface UpdateServerGroupDTO {
  name?: string
  description?: string
  color?: string
}

export interface AddServerToGroupDTO {
  serverId: string
}

export interface RemoveServerFromGroupDTO {
  serverId: string
}

export interface ServerGroupWithServers extends ServerGroup {
  members: (ServerGroupMember & {
    server: {
      id: string
      name: string
      host: string
      port: number
      deviceType: string
    }
  })[]
}

import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  LinearProgress,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Tooltip,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Sync as SyncIcon,
  PlayArrow as TestIcon,
  Dashboard as DashboardIcon,
  Warning as AlertIcon,
  CheckCircle as ConnectedIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
  Download as ExportIcon,
  Upload as ImportIcon,
  Visibility as ViewIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { useExternalMonitoring, ExternalMonitoringProvider, MonitoringIntegration, PROVIDER_TEMPLATES } from '../hooks/useExternalMonitoring';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
};

const ExternalMonitoring: React.FC = () => {
  const {
    providers,
    integrations,
    isLoading,
    addProvider,
    updateProvider,
    deleteProvider,
    testConnection,
    syncProvider,
    createIntegration,
    updateIntegration,
    deleteIntegration,
    exportConfiguration,
    importConfiguration
  } = useExternalMonitoring();

  const [activeTab, setActiveTab] = useState(0);
  const [showAddProviderDialog, setShowAddProviderDialog] = useState(false);
  const [showAddIntegrationDialog, setShowAddIntegrationDialog] = useState(false);
  const [showSyncResult, setShowSyncResult] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<ExternalMonitoringProvider | null>(null);
  const [syncResult, setSyncResult] = useState<any>(null);

  // Formulário de novo provider
  const [newProviderData, setNewProviderData] = useState({
    name: '',
    type: 'prometheus' as ExternalMonitoringProvider['type'],
    url: '',
    apiKey: '',
    username: '',
    password: '',
    isActive: true,
    config: {},
    metrics: [] as string[]
  });

  // Formulário de nova integração
  const [newIntegrationData, setNewIntegrationData] = useState({
    providerId: '',
    serverIds: [] as string[],
    metrics: [] as string[],
    syncInterval: 5,
    isActive: true,
    config: {
      autoCreateAlerts: true,
      alertThresholds: {},
      customLabels: {}
    }
  });

  const getStatusIcon = (status: ExternalMonitoringProvider['status']) => {
    switch (status) {
      case 'connected':
        return <ConnectedIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'syncing':
        return <SyncIcon color="info" />;
      default:
        return <ScheduleIcon color="action" />;
    }
  };

  const getStatusColor = (status: ExternalMonitoringProvider['status']) => {
    switch (status) {
      case 'connected':
        return 'success';
      case 'error':
        return 'error';
      case 'syncing':
        return 'info';
      default:
        return 'default';
    }
  };

  const handleAddProvider = async () => {
    try {
      const template = PROVIDER_TEMPLATES[newProviderData.type];
      await addProvider({
        ...newProviderData,
        config: { ...template.config, ...newProviderData.config },
        metrics: template.metrics
      });
      setShowAddProviderDialog(false);
      resetProviderForm();
    } catch (error) {
      console.error('Erro ao adicionar provider:', error);
    }
  };

  const handleTestConnection = async (providerId: string) => {
    try {
      const success = await testConnection(providerId);
      alert(success ? 'Conexão bem-sucedida!' : 'Falha na conexão');
    } catch (error) {
      console.error('Erro ao testar conexão:', error);
    }
  };

  const handleSyncProvider = async (providerId: string) => {
    try {
      const result = await syncProvider(providerId);
      setSyncResult(result);
      setShowSyncResult(true);
    } catch (error) {
      console.error('Erro ao sincronizar:', error);
    }
  };

  const handleAddIntegration = async () => {
    try {
      await createIntegration(newIntegrationData);
      setShowAddIntegrationDialog(false);
      resetIntegrationForm();
    } catch (error) {
      console.error('Erro ao criar integração:', error);
    }
  };

  const handleImportConfig = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        const success = await importConfiguration(file);
        alert(success ? 'Configuração importada com sucesso!' : 'Erro ao importar configuração');
      } catch (error) {
        console.error('Erro ao importar:', error);
      }
    }
  };

  const resetProviderForm = () => {
    setNewProviderData({
      name: '',
      type: 'prometheus',
      url: '',
      apiKey: '',
      username: '',
      password: '',
      isActive: true,
      config: {},
      metrics: []
    });
  };

  const resetIntegrationForm = () => {
    setNewIntegrationData({
      providerId: '',
      serverIds: [],
      metrics: [],
      syncInterval: 5,
      isActive: true,
      config: {
        autoCreateAlerts: true,
        alertThresholds: {},
        customLabels: {}
      }
    });
  };

  const connectedProviders = providers.filter(p => p.status === 'connected').length;
  const totalAlerts = providers.reduce((sum, p) => sum + p.alerts.length, 0);
  const activeIntegrations = integrations.filter(i => i.isActive).length;

  return (
    <Container maxWidth="xl">
      {/* Cabeçalho */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Monitoramento Externo
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Integre com sistemas de monitoramento externos como Prometheus, Grafana, Datadog e outros
        </Typography>
      </Box>

      {/* Estatísticas */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <DashboardIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" color="primary">
                {providers.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Providers
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ConnectedIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" color="success.main">
                {connectedProviders}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Conectados
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <AlertIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" color="warning.main">
                {totalAlerts}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Alertas Ativos
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <SettingsIcon sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" color="info.main">
                {activeIntegrations}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Integrações Ativas
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Loading */}
      {isLoading && <LinearProgress sx={{ mb: 3 }} />}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="Providers" />
          <Tab label="Integrações" />
          <Tab label="Dashboards" />
          <Tab label="Alertas" />
        </Tabs>
      </Box>

      {/* Aba Providers */}
      <TabPanel value={activeTab} index={0}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">
            Providers de Monitoramento ({providers.length})
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <input
              type="file"
              accept=".json"
              onChange={handleImportConfig}
              style={{ display: 'none' }}
              id="import-config"
            />
            <label htmlFor="import-config">
              <Button
                component="span"
                startIcon={<ImportIcon />}
                variant="outlined"
              >
                Importar
              </Button>
            </label>
            <Button
              startIcon={<ExportIcon />}
              onClick={exportConfiguration}
              variant="outlined"
            >
              Exportar
            </Button>
            <Button
              startIcon={<AddIcon />}
              onClick={() => setShowAddProviderDialog(true)}
              variant="contained"
            >
              Adicionar Provider
            </Button>
          </Box>
        </Box>

        <Grid container spacing={3}>
          {providers.map((provider) => (
            <Grid item xs={12} md={6} lg={4} key={provider.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6">
                      {provider.name}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getStatusIcon(provider.status)}
                      <Chip
                        label={provider.status}
                        color={getStatusColor(provider.status) as any}
                        size="small"
                      />
                    </Box>
                  </Box>

                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {provider.type.toUpperCase()}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {provider.url}
                  </Typography>

                  <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mb: 2 }}>
                    <Chip label={`${provider.metrics.length} métricas`} size="small" variant="outlined" />
                    <Chip label={`${provider.dashboards.length} dashboards`} size="small" variant="outlined" />
                    <Chip label={`${provider.alerts.length} alertas`} size="small" variant="outlined" />
                  </Box>

                  {provider.lastSync && (
                    <Typography variant="caption" color="text.secondary">
                      Última sync: {format(provider.lastSync, 'dd/MM HH:mm', { locale: ptBR })}
                    </Typography>
                  )}
                </CardContent>

                <CardActions>
                  <Button
                    size="small"
                    startIcon={<TestIcon />}
                    onClick={() => handleTestConnection(provider.id)}
                    disabled={isLoading}
                  >
                    Testar
                  </Button>
                  <Button
                    size="small"
                    startIcon={<SyncIcon />}
                    onClick={() => handleSyncProvider(provider.id)}
                    disabled={isLoading}
                  >
                    Sync
                  </Button>
                  <Button
                    size="small"
                    startIcon={<EditIcon />}
                    onClick={() => {
                      setSelectedProvider(provider);
                      // Abrir dialog de edição
                    }}
                  >
                    Editar
                  </Button>
                  <Button
                    size="small"
                    startIcon={<DeleteIcon />}
                    onClick={() => {
                      if (window.confirm('Tem certeza que deseja excluir este provider?')) {
                        deleteProvider(provider.id);
                      }
                    }}
                    color="error"
                  >
                    Excluir
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      {/* Aba Integrações */}
      <TabPanel value={activeTab} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">
            Integrações ({integrations.length})
          </Typography>
          <Button
            startIcon={<AddIcon />}
            onClick={() => setShowAddIntegrationDialog(true)}
            variant="contained"
          >
            Nova Integração
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Provider</TableCell>
                <TableCell>Servidores</TableCell>
                <TableCell>Métricas</TableCell>
                <TableCell>Intervalo</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Última Sync</TableCell>
                <TableCell>Ações</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {integrations.map((integration) => {
                const provider = providers.find(p => p.id === integration.providerId);
                return (
                  <TableRow key={integration.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {provider?.name || 'Provider não encontrado'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {provider?.type}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={`${integration.serverIds.length} servidores`}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={`${integration.metrics.length} métricas`}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      {integration.syncInterval} min
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={integration.isActive ? 'Ativa' : 'Inativa'}
                        color={integration.isActive ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {integration.lastSync ? (
                        format(integration.lastSync, 'dd/MM HH:mm')
                      ) : (
                        'Nunca'
                      )}
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <Tooltip title="Editar">
                          <IconButton
                            size="small"
                            onClick={() => {
                              // Abrir dialog de edição
                            }}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Excluir">
                          <IconButton
                            size="small"
                            onClick={() => {
                              if (window.confirm('Tem certeza que deseja excluir esta integração?')) {
                                deleteIntegration(integration.id);
                              }
                            }}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Aba Dashboards */}
      <TabPanel value={activeTab} index={2}>
        <Typography variant="h6" gutterBottom>
          Dashboards Externos
        </Typography>
        
        <Grid container spacing={3}>
          {providers.flatMap(provider => 
            provider.dashboards.map(dashboard => (
              <Grid item xs={12} md={6} key={`${provider.id}-${dashboard.id}`}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      {dashboard.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {dashboard.description}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Provider: {provider.name}
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mt: 1 }}>
                      {dashboard.tags.map(tag => (
                        <Chip key={tag} label={tag} size="small" variant="outlined" />
                      ))}
                    </Box>
                  </CardContent>
                  <CardActions>
                    <Button
                      size="small"
                      startIcon={<ViewIcon />}
                      onClick={() => window.open(dashboard.url, '_blank')}
                    >
                      Visualizar
                    </Button>
                    {dashboard.isEmbeddable && (
                      <Button
                        size="small"
                        startIcon={<DashboardIcon />}
                      >
                        Incorporar
                      </Button>
                    )}
                  </CardActions>
                </Card>
              </Grid>
            ))
          )}
        </Grid>
      </TabPanel>

      {/* Aba Alertas */}
      <TabPanel value={activeTab} index={3}>
        <Typography variant="h6" gutterBottom>
          Alertas Externos
        </Typography>
        
        <List>
          {providers.flatMap(provider => 
            provider.alerts.map(alert => (
              <ListItem key={`${provider.id}-${alert.id}`} divider>
                <ListItemIcon>
                  <AlertIcon color={alert.severity === 'critical' ? 'error' : 'warning'} />
                </ListItemIcon>
                <ListItemText
                  primary={alert.name}
                  secondary={
                    <Box>
                      <Typography variant="body2">
                        {alert.message}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {provider.name} • {format(alert.timestamp, 'dd/MM/yyyy HH:mm')}
                      </Typography>
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <Chip
                    label={alert.severity}
                    color={alert.severity === 'critical' ? 'error' : 'warning'}
                    size="small"
                  />
                </ListItemSecondaryAction>
              </ListItem>
            ))
          )}
        </List>
      </TabPanel>

      {/* Dialog para adicionar provider */}
      <Dialog open={showAddProviderDialog} onClose={() => setShowAddProviderDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Adicionar Provider de Monitoramento</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Nome"
                  value={newProviderData.name}
                  onChange={(e) => setNewProviderData(prev => ({ ...prev, name: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Tipo</InputLabel>
                  <Select
                    value={newProviderData.type}
                    onChange={(e) => setNewProviderData(prev => ({ 
                      ...prev, 
                      type: e.target.value as ExternalMonitoringProvider['type']
                    }))}
                  >
                    <MenuItem value="prometheus">Prometheus</MenuItem>
                    <MenuItem value="grafana">Grafana</MenuItem>
                    <MenuItem value="datadog">Datadog</MenuItem>
                    <MenuItem value="newrelic">New Relic</MenuItem>
                    <MenuItem value="elastic">Elasticsearch</MenuItem>
                    <MenuItem value="zabbix">Zabbix</MenuItem>
                    <MenuItem value="nagios">Nagios</MenuItem>
                    <MenuItem value="custom">Custom</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="URL"
                  value={newProviderData.url}
                  onChange={(e) => setNewProviderData(prev => ({ ...prev, url: e.target.value }))}
                  placeholder="http://prometheus.example.com:9090"
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="API Key (opcional)"
                  value={newProviderData.apiKey}
                  onChange={(e) => setNewProviderData(prev => ({ ...prev, apiKey: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Username (opcional)"
                  value={newProviderData.username}
                  onChange={(e) => setNewProviderData(prev => ({ ...prev, username: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={newProviderData.isActive}
                      onChange={(e) => setNewProviderData(prev => ({ ...prev, isActive: e.target.checked }))}
                    />
                  }
                  label="Ativo"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddProviderDialog(false)}>Cancelar</Button>
          <Button onClick={handleAddProvider} variant="contained">Adicionar</Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para adicionar integração */}
      <Dialog open={showAddIntegrationDialog} onClose={() => setShowAddIntegrationDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Nova Integração</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Provider</InputLabel>
                  <Select
                    value={newIntegrationData.providerId}
                    onChange={(e) => setNewIntegrationData(prev => ({ ...prev, providerId: e.target.value }))}
                  >
                    {providers.map(provider => (
                      <MenuItem key={provider.id} value={provider.id}>
                        {provider.name} ({provider.type})
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  type="number"
                  label="Intervalo de Sync (minutos)"
                  value={newIntegrationData.syncInterval}
                  onChange={(e) => setNewIntegrationData(prev => ({ 
                    ...prev, 
                    syncInterval: parseInt(e.target.value) 
                  }))}
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={newIntegrationData.isActive}
                      onChange={(e) => setNewIntegrationData(prev => ({ ...prev, isActive: e.target.checked }))}
                    />
                  }
                  label="Ativo"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddIntegrationDialog(false)}>Cancelar</Button>
          <Button onClick={handleAddIntegration} variant="contained">Criar</Button>
        </DialogActions>
      </Dialog>

      {/* Dialog de resultado de sync */}
      <Dialog open={showSyncResult} onClose={() => setShowSyncResult(false)}>
        <DialogTitle>Resultado da Sincronização</DialogTitle>
        <DialogContent>
          {syncResult && (
            <Box>
              <Alert severity={syncResult.success ? 'success' : 'error'} sx={{ mb: 2 }}>
                {syncResult.success ? 'Sincronização concluída com sucesso!' : 'Falha na sincronização'}
              </Alert>
              
              <Typography variant="body2" gutterBottom>
                Métricas sincronizadas: {syncResult.metricsCount}
              </Typography>
              <Typography variant="body2" gutterBottom>
                Alertas sincronizados: {syncResult.alertsCount}
              </Typography>
              <Typography variant="body2" gutterBottom>
                Dashboards sincronizados: {syncResult.dashboardsCount}
              </Typography>
              <Typography variant="body2" gutterBottom>
                Duração: {syncResult.duration}ms
              </Typography>
              
              {syncResult.errors && syncResult.errors.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" color="error">
                    Erros:
                  </Typography>
                  {syncResult.errors.map((error: string, index: number) => (
                    <Typography key={index} variant="body2" color="error">
                      • {error}
                    </Typography>
                  ))}
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSyncResult(false)}>Fechar</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default ExternalMonitoring;

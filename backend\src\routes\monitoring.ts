import { FastifyInstance } from 'fastify';
import { SSHService } from '../services/ssh/SSHService';
import { AlertService } from '../services/monitoring/AlertService';
import { Logger } from '../utils/logger';

// Instâncias globais dos serviços (em produção, usar injeção de dependência)
const sshService = new SSHService();
const alertService = new AlertService();

/**
 * Rotas para monitoramento e métricas do sistema
 */
export async function monitoringRoutes(fastify: FastifyInstance) {

  // Middleware de autenticação para rotas de monitoramento
  fastify.addHook('preHandler', async (request, reply) => {
    try {
      await request.jwtVerify();
    } catch (err) {
      reply.send(err);
    }
  });

  /**
   * GET /api/monitoring/metrics/devices
   * Obtém métricas por tipo de dispositivo
   */
  fastify.get('/metrics/devices', async (request, reply) => {
    try {
      const metricsService = sshService.getMetricsService();
      const deviceMetrics = metricsService.getDeviceMetrics();

      return {
        success: true,
        data: deviceMetrics,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter métricas de dispositivos:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/metrics/services
   * Obtém métricas por serviço (Node.js vs Python)
   */
  fastify.get('/metrics/services', async (request, reply) => {
    try {
      const metricsService = sshService.getMetricsService();
      const serviceMetrics = metricsService.getServiceMetrics();

      return {
        success: true,
        data: serviceMetrics,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter métricas de serviços:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/health
   * Obtém saúde geral do sistema
   */
  fastify.get('/health', async (request, reply) => {
    try {
      const metricsService = sshService.getMetricsService();
      const systemHealth = await metricsService.getSystemHealth();

      // Definir status HTTP baseado na saúde do sistema
      const statusCode = systemHealth.status === 'critical' ? 503 :
                        systemHealth.status === 'degraded' ? 206 : 200;

      reply.status(statusCode).send({
        success: true,
        data: systemHealth,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      Logger.error('Erro ao obter saúde do sistema:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/performance
   * Obtém métricas de performance em tempo real
   */
  fastify.get('/performance', async (request, reply) => {
    try {
      const { timeRange } = request.query as { timeRange?: '1h' | '24h' | '7d' };
      const metricsService = sshService.getMetricsService();
      const performanceMetrics = metricsService.getPerformanceMetrics(timeRange || '24h');

      return {
        success: true,
        data: performanceMetrics,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter métricas de performance:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/alerts
   * Obtém alertas ativos
   */
  fastify.get('/alerts', async (request, reply) => {
    try {
      const activeAlerts = alertService.getActiveAlerts();

      return {
        success: true,
        data: activeAlerts,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter alertas:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/alerts/history
   * Obtém histórico de alertas
   */
  fastify.get('/alerts/history', async (request, reply) => {
    try {
      const { limit } = request.query as { limit?: string };
      const alertHistory = alertService.getAlertHistory(
        limit ? parseInt(limit) : 100
      );

      return {
        success: true,
        data: alertHistory,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter histórico de alertas:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * POST /api/monitoring/alerts/:id/resolve
   * Resolve um alerta manualmente
   */
  fastify.post('/alerts/:id/resolve', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const resolved = alertService.resolveAlert(id);

      if (resolved) {
        return {
          success: true,
          message: 'Alerta resolvido com sucesso',
          timestamp: new Date().toISOString()
        };
      } else {
        reply.status(404).send({
          success: false,
          error: 'Alerta não encontrado ou já resolvido'
        });
      }
    } catch (error) {
      Logger.error('Erro ao resolver alerta:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/alerts/rules
   * Obtém regras de alerta configuradas
   */
  fastify.get('/alerts/rules', async (request, reply) => {
    try {
      const rules = alertService.getRules();

      return {
        success: true,
        data: rules,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter regras de alerta:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * POST /api/monitoring/alerts/rules
   * Adiciona uma nova regra de alerta
   */
  fastify.post('/alerts/rules', async (request, reply) => {
    try {
      const ruleData = request.body as any;

      // Validação básica
      if (!ruleData.name || !ruleData.condition) {
        reply.status(400).send({
          success: false,
          error: 'Dados inválidos: nome e condição são obrigatórios'
        });
        return;
      }

      const ruleId = alertService.addRule(ruleData);

      return {
        success: true,
        data: { id: ruleId },
        message: 'Regra de alerta criada com sucesso',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao criar regra de alerta:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * PUT /api/monitoring/alerts/rules/:id
   * Atualiza uma regra de alerta
   */
  fastify.put('/alerts/rules/:id', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const updates = request.body as any;

      const updated = alertService.updateRule(id, updates);

      if (updated) {
        return {
          success: true,
          message: 'Regra de alerta atualizada com sucesso',
          timestamp: new Date().toISOString()
        };
      } else {
        reply.status(404).send({
          success: false,
          error: 'Regra de alerta não encontrada'
        });
      }
    } catch (error) {
      Logger.error('Erro ao atualizar regra de alerta:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * DELETE /api/monitoring/alerts/rules/:id
   * Remove uma regra de alerta
   */
  fastify.delete('/alerts/rules/:id', async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const removed = alertService.removeRule(id);

      if (removed) {
        return {
          success: true,
          message: 'Regra de alerta removida com sucesso',
          timestamp: new Date().toISOString()
        };
      } else {
        reply.status(404).send({
          success: false,
          error: 'Regra de alerta não encontrada'
        });
      }
    } catch (error) {
      Logger.error('Erro ao remover regra de alerta:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/dashboard
   * Obtém dados consolidados para dashboard
   */
  fastify.get('/dashboard', async (request, reply) => {
    try {
      const metricsService = sshService.getMetricsService();
      const cacheService = sshService.getCacheService();

      const [
        deviceMetrics,
        serviceMetrics,
        systemHealth,
        performanceMetrics,
        activeAlerts,
        cacheStats
      ] = await Promise.all([
        metricsService.getDeviceMetrics(),
        metricsService.getServiceMetrics(),
        metricsService.getSystemHealth(),
        metricsService.getPerformanceMetrics('24h'),
        alertService.getActiveAlerts(),
        cacheService.getStats()
      ]);

      return {
        success: true,
        data: {
          devices: deviceMetrics,
          services: serviceMetrics,
          health: systemHealth,
          performance: performanceMetrics,
          alerts: activeAlerts,
          cache: cacheStats
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter dados do dashboard:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/cache/stats
   * Obtém estatísticas do cache
   */
  fastify.get('/cache/stats', async (request, reply) => {
    try {
      const cacheService = sshService.getCacheService();
      const stats = await cacheService.getStats();

      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter estatísticas do cache:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/cache/popular
   * Obtém comandos mais populares do cache
   */
  fastify.get('/cache/popular', async (request, reply) => {
    try {
      const { limit } = request.query as { limit?: string };
      const cacheService = sshService.getCacheService();
      const popularCommands = await cacheService.getPopularCommands(
        limit ? parseInt(limit) : 10
      );

      return {
        success: true,
        data: popularCommands,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter comandos populares:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * POST /api/monitoring/cache/clear
   * Limpa todo o cache
   */
  fastify.post('/cache/clear', async (request, reply) => {
    try {
      const cacheService = sshService.getCacheService();
      await cacheService.clear();

      return {
        success: true,
        message: 'Cache limpo com sucesso',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao limpar cache:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * POST /api/monitoring/cache/invalidate/:serverId
   * Invalida cache para um servidor específico
   */
  fastify.post('/cache/invalidate/:serverId', async (request, reply) => {
    try {
      const { serverId } = request.params as { serverId: string };
      const cacheService = sshService.getCacheService();
      await cacheService.invalidateServer(serverId);

      return {
        success: true,
        message: `Cache invalidado para servidor ${serverId}`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao invalidar cache do servidor:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/performance/stats
   * Obtém estatísticas de performance dos servidores
   */
  fastify.get('/performance/stats', async (request, reply) => {
    try {
      const performanceOptimizer = sshService.getPerformanceOptimizer();
      const stats = performanceOptimizer.getPerformanceStats();

      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter estatísticas de performance:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/performance/recommendations
   * Obtém recomendações de otimização
   */
  fastify.get('/performance/recommendations', async (request, reply) => {
    try {
      const performanceOptimizer = sshService.getPerformanceOptimizer();
      const recommendations = performanceOptimizer.getOptimizationRecommendations();

      return {
        success: true,
        data: recommendations,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter recomendações de otimização:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * POST /api/monitoring/performance/optimize
   * Força otimização para todos os servidores
   */
  fastify.post('/performance/optimize', async (request, reply) => {
    try {
      const performanceOptimizer = sshService.getPerformanceOptimizer();
      performanceOptimizer.optimizeAllServers();

      return {
        success: true,
        message: 'Otimização forçada executada para todos os servidores',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao executar otimização forçada:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/performance/settings/:serverId
   * Obtém configurações otimizadas para um servidor específico
   */
  fastify.get('/performance/settings/:serverId', async (request, reply) => {
    try {
      const { serverId } = request.params as { serverId: string };
      const { deviceType } = request.query as { deviceType?: string };

      const performanceOptimizer = sshService.getPerformanceOptimizer();
      const settings = performanceOptimizer.getOptimizedSettings(serverId, deviceType);

      return {
        success: true,
        data: settings,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter configurações otimizadas:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * POST /api/monitoring/cache/invalidate-pattern
   * Invalida cache por padrão de comando
   */
  fastify.post('/cache/invalidate-pattern', async (request, reply) => {
    try {
      const { pattern } = request.body as { pattern: string };
      const cacheService = sshService.getCacheService();
      await cacheService.invalidatePattern(pattern);

      return {
        success: true,
        message: `Cache invalidado para padrão: ${pattern}`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao invalidar cache por padrão:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/cache/config
   * Obtém configuração atual do cache
   */
  fastify.get('/cache/config', async (request, reply) => {
    try {
      const cacheService = sshService.getCacheService();
      const config = await cacheService.getConfig();

      return {
        success: true,
        data: config,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter configuração do cache:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * PUT /api/monitoring/cache/config
   * Atualiza configuração do cache
   */
  fastify.put('/cache/config', async (request, reply) => {
    try {
      const config = request.body as any;
      const cacheService = sshService.getCacheService();
      await cacheService.updateConfig(config);

      return {
        success: true,
        message: 'Configuração do cache atualizada',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao atualizar configuração do cache:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/cache/entries
   * Obtém entradas do cache com paginação
   */
  fastify.get('/cache/entries', async (request, reply) => {
    try {
      const { page = 1, limit = 50 } = request.query as { page?: number; limit?: number };
      const cacheService = sshService.getCacheService();
      const entries = await cacheService.getCacheEntries(page, limit);

      return {
        success: true,
        data: entries,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter entradas do cache:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * DELETE /api/monitoring/cache/entries/:key
   * Remove uma entrada específica do cache
   */
  fastify.delete('/cache/entries/:key', async (request, reply) => {
    try {
      const { key } = request.params as { key: string };
      const cacheService = sshService.getCacheService();
      await cacheService.removeEntry(decodeURIComponent(key));

      return {
        success: true,
        message: 'Entrada removida do cache',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao remover entrada do cache:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/cache/metrics
   * Obtém métricas detalhadas do cache
   */
  fastify.get('/cache/metrics', async (request, reply) => {
    try {
      const { timeRange = '24h' } = request.query as { timeRange?: string };
      const cacheService = sshService.getCacheService();
      const metrics = await cacheService.getMetrics(timeRange);

      return {
        success: true,
        data: metrics,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter métricas do cache:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * POST /api/monitoring/cache/refresh/:serverId
   * Força refresh do cache para um servidor
   */
  fastify.post('/cache/refresh/:serverId', async (request, reply) => {
    try {
      const { serverId } = request.params as { serverId: string };
      const cacheService = sshService.getCacheService();
      await cacheService.refreshServer(serverId);

      return {
        success: true,
        message: `Cache atualizado para servidor ${serverId}`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao atualizar cache do servidor:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/cache/redis-health
   * Obtém informações de saúde do Redis
   */
  fastify.get('/cache/redis-health', async (request, reply) => {
    try {
      const cacheService = sshService.getCacheService();
      const health = await cacheService.getRedisHealth();

      return {
        success: true,
        data: health,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter saúde do Redis:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * POST /api/monitoring/cache/redis-command
   * Executa comando Redis personalizado (apenas para admins)
   */
  fastify.post('/cache/redis-command', async (request, reply) => {
    try {
      const { command } = request.body as { command: string };
      const cacheService = sshService.getCacheService();
      const result = await cacheService.executeRedisCommand(command);

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao executar comando Redis:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/cache/server-performance
   * Obtém estatísticas de performance por servidor
   */
  fastify.get('/cache/server-performance', async (request, reply) => {
    try {
      const cacheService = sshService.getCacheService();
      const performance = await cacheService.getServerPerformance();

      return {
        success: true,
        data: performance,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter performance por servidor:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/cache/export-config
   * Exporta configurações do cache
   */
  fastify.get('/cache/export-config', async (request, reply) => {
    try {
      const cacheService = sshService.getCacheService();
      const config = await cacheService.exportConfig();

      reply.header('Content-Type', 'application/json');
      reply.header('Content-Disposition', `attachment; filename="cache-config-${new Date().toISOString().split('T')[0]}.json"`);

      return config;
    } catch (error) {
      Logger.error('Erro ao exportar configuração do cache:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * POST /api/monitoring/cache/import-config
   * Importa configurações do cache
   */
  fastify.post('/cache/import-config', async (request, reply) => {
    try {
      // Aqui seria implementado o upload de arquivo
      // Por simplicidade, vamos aceitar JSON no body
      const config = request.body as any;
      const cacheService = sshService.getCacheService();
      await cacheService.importConfig(config);

      return {
        success: true,
        message: 'Configuração importada com sucesso',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao importar configuração do cache:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * GET /api/monitoring/cache/recommendations
   * Obtém recomendações de otimização do cache
   */
  fastify.get('/cache/recommendations', async (request, reply) => {
    try {
      const cacheService = sshService.getCacheService();
      const recommendations = await cacheService.getOptimizationRecommendations();

      return {
        success: true,
        data: recommendations,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao obter recomendações de otimização:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });

  /**
   * POST /api/monitoring/cache/optimize
   * Aplica otimizações automáticas
   */
  fastify.post('/cache/optimize', async (request, reply) => {
    try {
      const cacheService = sshService.getCacheService();
      const result = await cacheService.applyOptimizations();

      return {
        success: true,
        data: result,
        message: `${result.applied} otimizações aplicadas`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      Logger.error('Erro ao aplicar otimizações:', error);
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  });
}

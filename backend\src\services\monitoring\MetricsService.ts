import { Logger } from '../../utils/logger';

interface CommandMetric {
  id: string;
  serverId: string;
  serverName: string;
  deviceType: string;
  command: string;
  executionTime: number;
  success: boolean;
  errorType?: string;
  timestamp: Date;
  service: 'nodejs' | 'python';
}

interface DeviceMetrics {
  deviceType: string;
  totalCommands: number;
  successfulCommands: number;
  failedCommands: number;
  averageExecutionTime: number;
  successRate: number;
  lastFailure?: Date;
  commonErrors: { [error: string]: number };
}

interface ServiceMetrics {
  service: 'nodejs' | 'python';
  totalCommands: number;
  successfulCommands: number;
  failedCommands: number;
  averageExecutionTime: number;
  successRate: number;
  uptime: number;
  memoryUsage?: number;
  cpuUsage?: number;
}

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'critical';
  services: {
    nodejs: 'up' | 'down' | 'degraded';
    python: 'up' | 'down' | 'degraded';
    database: 'up' | 'down' | 'degraded';
    redis: 'up' | 'down' | 'degraded';
  };
  alerts: Alert[];
  lastCheck: Date;
}

interface Alert {
  id: string;
  type: 'error' | 'warning' | 'info';
  message: string;
  timestamp: Date;
  resolved: boolean;
  service?: string;
  deviceType?: string;
}

/**
 * Serviço de métricas e monitoramento
 * Coleta, armazena e analisa métricas de performance do sistema
 */
export class MetricsService {
  private metrics: CommandMetric[] = [];
  private alerts: Alert[] = [];
  private startTime: Date = new Date();
  private maxMetricsHistory = 10000; // Manter últimas 10k métricas em memória
  private alertThresholds = {
    successRate: 0.85, // Alertar se taxa de sucesso < 85%
    averageExecutionTime: 60000, // Alertar se tempo médio > 60s
    errorRate: 0.15, // Alertar se taxa de erro > 15%
    consecutiveFailures: 5 // Alertar após 5 falhas consecutivas
  };

  constructor() {
    Logger.log('MetricsService inicializado');

    // Limpar métricas antigas periodicamente (a cada hora)
    setInterval(() => {
      this.cleanOldMetrics();
    }, 3600000); // 1 hora

    // Verificar alertas periodicamente (a cada 5 minutos)
    setInterval(() => {
      this.checkAlerts();
    }, 300000); // 5 minutos
  }

  /**
   * Registra uma métrica de execução de comando
   */
  recordCommandExecution(
    serverId: string,
    serverName: string,
    deviceType: string,
    command: string,
    executionTime: number,
    success: boolean,
    service: 'nodejs' | 'python',
    errorType?: string
  ): void {
    const metric: CommandMetric = {
      id: this.generateId(),
      serverId,
      serverName,
      deviceType,
      command: this.sanitizeCommand(command),
      executionTime,
      success,
      errorType,
      timestamp: new Date(),
      service
    };

    this.metrics.push(metric);

    // Manter apenas as métricas mais recentes
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory);
    }

    Logger.log(`Métrica registrada: ${service} - ${deviceType} - ${success ? 'SUCCESS' : 'FAIL'} - ${executionTime}ms`);
  }

  /**
   * Obtém métricas por tipo de dispositivo
   */
  getDeviceMetrics(): DeviceMetrics[] {
    const deviceGroups = this.groupMetricsByDevice();

    return Object.entries(deviceGroups).map(([deviceType, metrics]) => {
      const totalCommands = metrics.length;
      const successfulCommands = metrics.filter(m => m.success).length;
      const failedCommands = totalCommands - successfulCommands;
      const averageExecutionTime = metrics.reduce((sum, m) => sum + m.executionTime, 0) / totalCommands;
      const successRate = totalCommands > 0 ? successfulCommands / totalCommands : 0;

      // Agrupar erros comuns
      const commonErrors: { [error: string]: number } = {};
      metrics.filter(m => !m.success && m.errorType).forEach(m => {
        commonErrors[m.errorType!] = (commonErrors[m.errorType!] || 0) + 1;
      });

      // Última falha
      const failures = metrics.filter(m => !m.success).sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      const lastFailure = failures.length > 0 ? failures[0].timestamp : undefined;

      return {
        deviceType,
        totalCommands,
        successfulCommands,
        failedCommands,
        averageExecutionTime,
        successRate,
        lastFailure,
        commonErrors
      };
    });
  }

  /**
   * Obtém métricas por serviço (Node.js vs Python)
   */
  getServiceMetrics(): ServiceMetrics[] {
    const serviceGroups = this.groupMetricsByService();

    return Object.entries(serviceGroups).map(([service, metrics]) => {
      const totalCommands = metrics.length;
      const successfulCommands = metrics.filter(m => m.success).length;
      const failedCommands = totalCommands - successfulCommands;
      const averageExecutionTime = metrics.reduce((sum, m) => sum + m.executionTime, 0) / totalCommands;
      const successRate = totalCommands > 0 ? successfulCommands / totalCommands : 0;
      const uptime = Date.now() - this.startTime.getTime();

      return {
        service: service as 'nodejs' | 'python',
        totalCommands,
        successfulCommands,
        failedCommands,
        averageExecutionTime,
        successRate,
        uptime
      };
    });
  }

  /**
   * Obtém saúde geral do sistema
   */
  async getSystemHealth(): Promise<SystemHealth> {
    const deviceMetrics = this.getDeviceMetrics();
    const serviceMetrics = this.getServiceMetrics();

    // Determinar status geral
    let status: 'healthy' | 'degraded' | 'critical' = 'healthy';

    // Verificar se há problemas críticos
    const criticalIssues = this.alerts.filter(a => !a.resolved && a.type === 'error').length;
    const warningIssues = this.alerts.filter(a => !a.resolved && a.type === 'warning').length;

    if (criticalIssues > 0) {
      status = 'critical';
    } else if (warningIssues > 2) {
      status = 'degraded';
    }

    // Verificar taxa de sucesso geral
    const overallSuccessRate = this.getOverallSuccessRate();
    if (overallSuccessRate < 0.7) {
      status = 'critical';
    } else if (overallSuccessRate < 0.85) {
      status = 'degraded';
    }

    return {
      status,
      services: {
        nodejs: await this.checkNodeJSHealth(),
        python: await this.checkPythonHealth(),
        database: await this.checkDatabaseHealth(),
        redis: await this.checkRedisHealth()
      },
      alerts: this.alerts.filter(a => !a.resolved).slice(-10), // Últimos 10 alertas não resolvidos
      lastCheck: new Date()
    };
  }

  /**
   * Obtém métricas de performance em tempo real
   */
  getPerformanceMetrics(timeRange: '1h' | '24h' | '7d' = '24h') {
    const now = new Date();
    const timeRangeMs = {
      '1h': 3600000,
      '24h': 86400000,
      '7d': *********
    }[timeRange];

    const cutoffTime = new Date(now.getTime() - timeRangeMs);
    const recentMetrics = this.metrics.filter(m => m.timestamp >= cutoffTime);

    // Agrupar por intervalos de tempo
    const intervals = this.groupMetricsByTimeInterval(recentMetrics, timeRange);

    return {
      timeRange,
      intervals,
      summary: {
        totalCommands: recentMetrics.length,
        successRate: this.calculateSuccessRate(recentMetrics),
        averageExecutionTime: this.calculateAverageExecutionTime(recentMetrics),
        topErrors: this.getTopErrors(recentMetrics),
        deviceDistribution: this.getDeviceDistribution(recentMetrics),
        serviceDistribution: this.getServiceDistribution(recentMetrics)
      }
    };
  }

  /**
   * Cria um alerta
   */
  private createAlert(
    type: 'error' | 'warning' | 'info',
    message: string,
    service?: string,
    deviceType?: string
  ): void {
    const alert: Alert = {
      id: this.generateId(),
      type,
      message,
      timestamp: new Date(),
      resolved: false,
      service,
      deviceType
    };

    this.alerts.push(alert);
    Logger.log(`Alerta criado: [${type.toUpperCase()}] ${message}`);

    // Manter apenas os últimos 1000 alertas
    if (this.alerts.length > 1000) {
      this.alerts = this.alerts.slice(-1000);
    }
  }

  /**
   * Resolve um alerta
   */
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      Logger.log(`Alerta resolvido: ${alertId}`);
    }
  }

  /**
   * Verifica e cria alertas baseado nas métricas
   */
  private checkAlerts(): void {
    const deviceMetrics = this.getDeviceMetrics();
    const serviceMetrics = this.getServiceMetrics();

    // Verificar taxa de sucesso por dispositivo
    deviceMetrics.forEach(device => {
      if (device.totalCommands >= 10 && device.successRate < this.alertThresholds.successRate) {
        this.createAlert(
          'warning',
          `Taxa de sucesso baixa para ${device.deviceType}: ${(device.successRate * 100).toFixed(1)}%`,
          undefined,
          device.deviceType
        );
      }

      if (device.averageExecutionTime > this.alertThresholds.averageExecutionTime) {
        this.createAlert(
          'warning',
          `Tempo de execução alto para ${device.deviceType}: ${(device.averageExecutionTime / 1000).toFixed(1)}s`,
          undefined,
          device.deviceType
        );
      }
    });

    // Verificar serviços
    serviceMetrics.forEach(service => {
      if (service.totalCommands >= 10 && service.successRate < this.alertThresholds.successRate) {
        this.createAlert(
          'error',
          `Taxa de sucesso baixa para serviço ${service.service}: ${(service.successRate * 100).toFixed(1)}%`,
          service.service
        );
      }
    });
  }

  // Métodos auxiliares privados
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private sanitizeCommand(command: string): string {
    // Remover informações sensíveis dos comandos para logs
    return command.replace(/password\s+\S+/gi, 'password ***')
                 .replace(/secret\s+\S+/gi, 'secret ***')
                 .substring(0, 200); // Limitar tamanho
  }

  private groupMetricsByDevice(): { [deviceType: string]: CommandMetric[] } {
    return this.metrics.reduce((groups, metric) => {
      const key = metric.deviceType || 'UNKNOWN';
      groups[key] = groups[key] || [];
      groups[key].push(metric);
      return groups;
    }, {} as { [deviceType: string]: CommandMetric[] });
  }

  private groupMetricsByService(): { [service: string]: CommandMetric[] } {
    return this.metrics.reduce((groups, metric) => {
      groups[metric.service] = groups[metric.service] || [];
      groups[metric.service].push(metric);
      return groups;
    }, {} as { [service: string]: CommandMetric[] });
  }

  private cleanOldMetrics(): void {
    const cutoffTime = new Date(Date.now() - 7 * 24 * 3600000); // 7 dias
    const initialCount = this.metrics.length;
    this.metrics = this.metrics.filter(m => m.timestamp >= cutoffTime);

    if (this.metrics.length < initialCount) {
      Logger.log(`Limpeza de métricas: removidas ${initialCount - this.metrics.length} métricas antigas`);
    }
  }

  private getOverallSuccessRate(): number {
    if (this.metrics.length === 0) return 1;
    const successfulCommands = this.metrics.filter(m => m.success).length;
    return successfulCommands / this.metrics.length;
  }

  private async checkNodeJSHealth(): Promise<'up' | 'down' | 'degraded'> {
    // Verificar se o serviço Node.js está respondendo
    try {
      const recentMetrics = this.metrics.filter(m =>
        m.service === 'nodejs' &&
        m.timestamp >= new Date(Date.now() - 300000) // Últimos 5 minutos
      );

      if (recentMetrics.length === 0) return 'up'; // Sem atividade recente

      const successRate = recentMetrics.filter(m => m.success).length / recentMetrics.length;
      return successRate >= 0.8 ? 'up' : 'degraded';
    } catch {
      return 'down';
    }
  }

  private async checkPythonHealth(): Promise<'up' | 'down' | 'degraded'> {
    try {
      // Verificar se o serviço Python está respondendo via HTTP
      const axios = require('axios');
      const pythonServiceUrl = process.env.PYTHON_SERVICE_URL || 'http://localhost:8000';

      try {
        const response = await axios.get(`${pythonServiceUrl}/health`, { timeout: 5000 });
        if (response.status === 200) {
          // Verificar métricas recentes para determinar se está degraded
          const recentMetrics = this.metrics.filter(m =>
            m.service === 'python' &&
            m.timestamp >= new Date(Date.now() - 300000) // Últimos 5 minutos
          );

          if (recentMetrics.length === 0) return 'up'; // Sem atividade recente

          const successRate = recentMetrics.filter(m => m.success).length / recentMetrics.length;
          return successRate >= 0.8 ? 'up' : 'degraded';
        }
      } catch (httpError) {
        Logger.warn('Serviço Python não está respondendo via HTTP');
        return 'down';
      }

      return 'up';
    } catch (error) {
      Logger.error('Erro ao verificar saúde do serviço Python:', error);
      return 'down';
    }
  }

  private async checkDatabaseHealth(): Promise<'up' | 'down' | 'degraded'> {
    try {
      // Verificar conexão com o banco de dados usando Prisma
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();

      try {
        // Fazer uma query simples para testar a conexão
        await prisma.$queryRaw`SELECT 1`;

        // Verificar latência da conexão
        const start = Date.now();
        await prisma.$queryRaw`SELECT 1`;
        const latency = Date.now() - start;

        await prisma.$disconnect();

        // Se a latência for muito alta, considerar degraded
        if (latency > 1000) { // 1 segundo
          return 'degraded';
        }

        return 'up';
      } catch (dbError) {
        Logger.error('Erro na conexão com o banco de dados:', dbError);
        await prisma.$disconnect();
        return 'down';
      }
    } catch (error) {
      Logger.error('Erro ao verificar saúde do banco de dados:', error);
      return 'down';
    }
  }

  private async checkRedisHealth(): Promise<'up' | 'down' | 'degraded'> {
    try {
      // Verificar conexão com Redis
      const Redis = require('ioredis');
      const redis = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        connectTimeout: 5000,
        lazyConnect: true
      });

      try {
        // Testar conexão
        await redis.connect();

        // Verificar latência
        const start = Date.now();
        await redis.ping();
        const latency = Date.now() - start;

        // Testar operações básicas
        await redis.set('health_check', 'ok', 'EX', 10);
        const result = await redis.get('health_check');

        await redis.disconnect();

        if (result !== 'ok') {
          return 'degraded';
        }

        // Se a latência for muito alta, considerar degraded
        if (latency > 500) { // 500ms
          return 'degraded';
        }

        return 'up';
      } catch (redisError) {
        Logger.error('Erro na conexão com Redis:', redisError);
        await redis.disconnect();
        return 'down';
      }
    } catch (error) {
      Logger.error('Erro ao verificar saúde do Redis:', error);
      return 'down';
    }
  }

  private groupMetricsByTimeInterval(metrics: CommandMetric[], timeRange: string) {
    if (metrics.length === 0) return [];

    // Definir intervalos baseados no timeRange
    const intervalMs = {
      '1h': 5 * 60 * 1000,    // 5 minutos
      '24h': 60 * 60 * 1000,  // 1 hora
      '7d': 6 * 60 * 60 * 1000 // 6 horas
    }[timeRange] || 60 * 60 * 1000;

    // Encontrar o timestamp mais antigo e mais recente
    const timestamps = metrics.map(m => m.timestamp.getTime());
    const minTime = Math.min(...timestamps);
    const maxTime = Math.max(...timestamps);

    // Criar buckets de tempo
    const buckets: { [key: number]: CommandMetric[] } = {};

    // Inicializar buckets
    for (let time = minTime; time <= maxTime; time += intervalMs) {
      const bucketKey = Math.floor(time / intervalMs) * intervalMs;
      buckets[bucketKey] = [];
    }

    // Agrupar métricas nos buckets
    metrics.forEach(metric => {
      const bucketKey = Math.floor(metric.timestamp.getTime() / intervalMs) * intervalMs;
      if (!buckets[bucketKey]) {
        buckets[bucketKey] = [];
      }
      buckets[bucketKey].push(metric);
    });

    // Converter para array de resultados
    return Object.entries(buckets)
      .map(([timestamp, bucketMetrics]) => ({
        timestamp: new Date(parseInt(timestamp)),
        metrics: bucketMetrics,
        successRate: bucketMetrics.length > 0
          ? bucketMetrics.filter(m => m.success).length / bucketMetrics.length
          : 0,
        averageExecutionTime: bucketMetrics.length > 0
          ? bucketMetrics.reduce((sum, m) => sum + m.executionTime, 0) / bucketMetrics.length
          : 0,
        commandCount: bucketMetrics.length
      }))
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  private calculateSuccessRate(metrics: CommandMetric[]): number {
    if (metrics.length === 0) return 1;
    return metrics.filter(m => m.success).length / metrics.length;
  }

  private calculateAverageExecutionTime(metrics: CommandMetric[]): number {
    if (metrics.length === 0) return 0;
    return metrics.reduce((sum, m) => sum + m.executionTime, 0) / metrics.length;
  }

  private getTopErrors(metrics: CommandMetric[]) {
    const errors: { [error: string]: number } = {};
    metrics.filter(m => !m.success && m.errorType).forEach(m => {
      errors[m.errorType!] = (errors[m.errorType!] || 0) + 1;
    });

    return Object.entries(errors)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([error, count]) => ({ error, count }));
  }

  private getDeviceDistribution(metrics: CommandMetric[]) {
    const distribution: { [device: string]: number } = {};
    metrics.forEach(m => {
      distribution[m.deviceType] = (distribution[m.deviceType] || 0) + 1;
    });
    return distribution;
  }

  private getServiceDistribution(metrics: CommandMetric[]) {
    const distribution: { [service: string]: number } = {};
    metrics.forEach(m => {
      distribution[m.service] = (distribution[m.service] || 0) + 1;
    });
    return distribution;
  }
}

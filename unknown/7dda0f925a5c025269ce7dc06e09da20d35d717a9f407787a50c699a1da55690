import logging
import time
import async<PERSON>
from typing import Dict, Any, Optional
from netmiko import ConnectHandler
from netmiko.ssh_exception import (
    NetMikoTimeoutException, 
    NetMikoAuthenticationException,
    SSHException
)
from ..models import SSHCommandRequest, CommandResult, DeviceType
from ..config import settings

logger = logging.getLogger(__name__)

class SSHService:
    def __init__(self):
        self.active_connections: Dict[str, Any] = {}
        self.stats = {
            'total_commands': 0,
            'successful_commands': 0,
            'failed_commands': 0,
            'total_execution_time': 0.0
        }
        
    def _get_connection_key(self, request: SSHCommandRequest) -> str:
        """Gera uma chave única para a conexão"""
        return f"{request.host}:{request.port}:{request.username}"
    
    def _get_device_config(self, request: SSHCommandRequest) -> Dict[str, Any]:
        """Configura parâmetros específicos do dispositivo para Netmiko"""
        base_config = {
            'host': request.host,
            'username': request.username,
            'port': request.port,
            'timeout': request.timeout,
            'session_log': f"logs/{request.host}_{int(time.time())}.log",
            'verbose': settings.debug,
        }
        
        # Adicionar credenciais
        if request.password:
            base_config['password'] = request.password
        elif request.private_key:
            base_config['key_file'] = request.private_key
        else:
            raise ValueError("Nenhum método de autenticação fornecido")
        
        # Configurações específicas por tipo de dispositivo
        device_configs = {
            DeviceType.HUAWEI: {
                'device_type': 'huawei',
                'timeout': settings.huawei_timeout,
                'keepalive': settings.huawei_keepalive,
                'global_delay_factor': 2,  # Mais tempo entre comandos
                'conn_timeout': 30,  # Timeout de conexão
            },
            DeviceType.HUAWEI_VRP: {
                'device_type': 'huawei_vrp',
                'timeout': settings.huawei_timeout,
                'keepalive': settings.huawei_keepalive,
                'global_delay_factor': 2,
                'conn_timeout': 30,
            },
            DeviceType.NOKIA: {
                'device_type': 'nokia_sros',
                'timeout': 90,
                'global_delay_factor': 1.5,
                'conn_timeout': 20,
            },
            DeviceType.MIKROTIK: {
                'device_type': 'mikrotik_routeros',
                'timeout': 60,
                'global_delay_factor': 1,
                'conn_timeout': 15,
            },
            DeviceType.CISCO: {
                'device_type': 'cisco_ios',
                'timeout': 60,
                'global_delay_factor': 1,
                'conn_timeout': 15,
            },
            DeviceType.LINUX: {
                'device_type': 'linux',
                'timeout': request.timeout,
                'global_delay_factor': 1,
                'conn_timeout': 10,
            },
            DeviceType.WINDOWS: {
                'device_type': 'windows',
                'timeout': request.timeout,
                'global_delay_factor': 1,
                'conn_timeout': 10,
            },
            DeviceType.GENERIC: {
                'device_type': 'terminal_server',
                'timeout': request.timeout,
                'global_delay_factor': 1,
                'conn_timeout': 10,
            }
        }
        
        # Aplicar configurações específicas do dispositivo
        if request.device_type in device_configs:
            base_config.update(device_configs[request.device_type])
        
        return base_config
    
    def _detect_device_info(self, output: str, device_type: DeviceType) -> Dict[str, Any]:
        """Detecta informações do dispositivo baseado na saída"""
        info = {
            'device_type': device_type.value,
            'detected_os': 'unknown'
        }
        
        # Detecção baseada em padrões na saída
        if 'VRP' in output or 'Huawei' in output:
            info['detected_os'] = 'VRP'
        elif 'RouterOS' in output or 'MikroTik' in output:
            info['detected_os'] = 'RouterOS'
        elif 'Nokia' in output or 'SR OS' in output:
            info['detected_os'] = 'SR OS'
        elif 'Linux' in output:
            info['detected_os'] = 'Linux'
        elif 'Windows' in output:
            info['detected_os'] = 'Windows'
            
        return info
    
    async def connect_and_execute(self, request: SSHCommandRequest) -> CommandResult:
        """
        Conecta ao dispositivo remoto e executa o comando especificado
        """
        start_time = time.time()
        self.stats['total_commands'] += 1
        
        try:
            logger.info(f"Conectando a {request.host} como {request.username} (tipo: {request.device_type})")
            
            # Configuração para o Netmiko
            device_config = self._get_device_config(request)
            
            # Conectar e executar comando
            with ConnectHandler(**device_config) as conn:
                logger.info(f"Conexão estabelecida com {request.host}")
                
                # Para dispositivos Huawei, usar estratégia especial
                if request.device_type in [DeviceType.HUAWEI, DeviceType.HUAWEI_VRP]:
                    output = await self._execute_huawei_command(conn, request.command)
                else:
                    # Netmiko lida automaticamente com paginação e prompts interativos
                    output = conn.send_command(
                        request.command,
                        expect_string=None,
                        delay_factor=2 if request.device_type == DeviceType.NOKIA else 1
                    )
                
                execution_time = time.time() - start_time
                self.stats['successful_commands'] += 1
                self.stats['total_execution_time'] += execution_time
                
                # Detectar informações do dispositivo
                device_info = self._detect_device_info(output, request.device_type)
                
                logger.info(f"Comando executado com sucesso em {request.host} ({execution_time:.2f}s)")
                
                return CommandResult(
                    stdout=output,
                    code=0,
                    execution_time=execution_time,
                    device_info=device_info
                )
                
        except NetMikoTimeoutException as e:
            execution_time = time.time() - start_time
            error_msg = f"Timeout ao conectar a {request.host}: {str(e)}"
            logger.error(error_msg)
            self.stats['failed_commands'] += 1
            return CommandResult(
                stderr=error_msg, 
                code=1, 
                execution_time=execution_time
            )
            
        except NetMikoAuthenticationException as e:
            execution_time = time.time() - start_time
            error_msg = f"Falha de autenticação ao conectar a {request.host}: {str(e)}"
            logger.error(error_msg)
            self.stats['failed_commands'] += 1
            return CommandResult(
                stderr=error_msg, 
                code=1, 
                execution_time=execution_time
            )
            
        except SSHException as e:
            execution_time = time.time() - start_time
            error_msg = f"Erro SSH ao conectar a {request.host}: {str(e)}"
            logger.error(error_msg)
            self.stats['failed_commands'] += 1
            return CommandResult(
                stderr=error_msg, 
                code=1, 
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Erro inesperado ao executar comando: {str(e)}"
            logger.error(error_msg)
            self.stats['failed_commands'] += 1
            return CommandResult(
                stderr=error_msg, 
                code=1, 
                execution_time=execution_time
            )
    
    async def _execute_huawei_command(self, conn, command: str) -> str:
        """
        Execução especializada para dispositivos Huawei/HarmonyOS
        Implementa estratégias específicas para lidar com prompts e timeouts
        """
        logger.info(f"Executando comando Huawei: {command}")
        
        try:
            # Para comandos Huawei, usar configurações mais conservadoras
            output = conn.send_command(
                command,
                expect_string=None,
                delay_factor=3,  # Mais tempo entre comandos
                max_loops=500,   # Mais loops para comandos longos
                strip_prompt=True,
                strip_command=True
            )
            
            logger.info("Comando Huawei executado com sucesso")
            return output
            
        except Exception as e:
            logger.error(f"Erro ao executar comando Huawei: {str(e)}")
            # Tentar uma abordagem mais simples
            try:
                output = conn.send_command_timing(
                    command,
                    delay_factor=4,
                    max_loops=300
                )
                return output
            except Exception as e2:
                logger.error(f"Falha na abordagem alternativa Huawei: {str(e2)}")
                raise e
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do serviço"""
        avg_time = 0
        if self.stats['successful_commands'] > 0:
            avg_time = self.stats['total_execution_time'] / self.stats['successful_commands']
            
        return {
            'total_commands': self.stats['total_commands'],
            'successful_commands': self.stats['successful_commands'],
            'failed_commands': self.stats['failed_commands'],
            'success_rate': (self.stats['successful_commands'] / max(self.stats['total_commands'], 1)) * 100,
            'average_execution_time': avg_time,
            'active_connections': len(self.active_connections)
        }

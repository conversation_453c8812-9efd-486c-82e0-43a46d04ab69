#!/bin/bash

# Script de inicialização do serviço Python SSH

echo "🐍 Iniciando Python SSH Service..."

# Verificar se o Python está instalado
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 não encontrado. Por favor, instale o Python 3.10 ou superior."
    exit 1
fi

# Criar diretório de logs se não existir
mkdir -p logs

# Verificar se o arquivo .env existe
if [ ! -f .env ]; then
    echo "📝 Criando arquivo .env a partir do exemplo..."
    cp .env.example .env
    echo "✅ Arquivo .env criado. Configure as variáveis conforme necessário."
fi

# Verificar se o ambiente virtual existe
if [ ! -d "venv" ]; then
    echo "🔧 Criando ambiente virtual Python..."
    python3 -m venv venv
fi

# Ativar ambiente virtual
echo "🔄 Ativando ambiente virtual..."
source venv/bin/activate

# Instalar dependências
echo "📦 Instalando dependências..."
pip install --upgrade pip
pip install -r requirements.txt

# Verificar se todas as dependências foram instaladas
echo "🔍 Verificando instalação das dependências..."
python -c "
import fastapi
import uvicorn
import netmiko
import pydantic
print('✅ Todas as dependências principais estão instaladas')
"

if [ $? -ne 0 ]; then
    echo "❌ Erro na instalação das dependências"
    exit 1
fi

# Executar testes se solicitado
if [ "$1" = "test" ]; then
    echo "🧪 Executando testes..."
    python -m pytest tests/ -v
    exit $?
fi

# Iniciar o serviço
echo "🚀 Iniciando o serviço SSH..."
echo "📍 Serviço estará disponível em: http://localhost:8000"
echo "📚 Documentação da API: http://localhost:8000/docs"
echo "🔍 Health check: http://localhost:8000/health"
echo ""
echo "Para parar o serviço, pressione Ctrl+C"
echo ""

# Iniciar com uvicorn
uvicorn main:app --host 0.0.0.0 --port 8000 --reload

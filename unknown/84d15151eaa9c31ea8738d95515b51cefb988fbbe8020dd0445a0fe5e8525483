import { api } from './api';

export interface DeviceMetrics {
  deviceType: string;
  totalCommands: number;
  successfulCommands: number;
  failedCommands: number;
  averageExecutionTime: number;
  successRate: number;
  lastFailure?: string;
  commonErrors: { [error: string]: number };
}

export interface ServiceMetrics {
  service: 'nodejs' | 'python';
  totalCommands: number;
  successfulCommands: number;
  failedCommands: number;
  averageExecutionTime: number;
  successRate: number;
  uptime: number;
  memoryUsage?: number;
  cpuUsage?: number;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'critical';
  services: {
    nodejs: 'up' | 'down' | 'degraded';
    python: 'up' | 'down' | 'degraded';
    database: 'up' | 'down' | 'degraded';
    redis: 'up' | 'down' | 'degraded';
  };
  alerts: Alert[];
  lastCheck: string;
}

export interface Alert {
  id: string;
  ruleId: string;
  ruleName: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  resolved: boolean;
  resolvedAt?: string;
  metadata: any;
}

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  condition: {
    type: 'success_rate' | 'execution_time' | 'error_count' | 'consecutive_failures' | 'service_down';
    threshold: number;
    timeWindowMinutes: number;
    deviceType?: string;
    service?: 'nodejs' | 'python';
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  cooldownMinutes: number;
  actions: Array<{
    type: 'log' | 'email' | 'webhook' | 'slack';
    config: any;
  }>;
}

export interface PerformanceMetrics {
  timeRange: '1h' | '24h' | '7d';
  intervals: any[];
  summary: {
    totalCommands: number;
    successRate: number;
    averageExecutionTime: number;
    topErrors: Array<{ error: string; count: number }>;
    deviceDistribution: { [device: string]: number };
    serviceDistribution: { [service: string]: number };
  };
}

export interface DashboardData {
  devices: DeviceMetrics[];
  services: ServiceMetrics[];
  health: SystemHealth;
  performance: PerformanceMetrics;
  alerts: Alert[];
}

/**
 * Serviço para comunicação com a API de monitoramento
 */
export class MonitoringService {
  
  /**
   * Obtém métricas por tipo de dispositivo
   */
  static async getDeviceMetrics(): Promise<DeviceMetrics[]> {
    const response = await api.get('/monitoring/metrics/devices');
    return response.data.data;
  }

  /**
   * Obtém métricas por serviço (Node.js vs Python)
   */
  static async getServiceMetrics(): Promise<ServiceMetrics[]> {
    const response = await api.get('/monitoring/metrics/services');
    return response.data.data;
  }

  /**
   * Obtém saúde geral do sistema
   */
  static async getSystemHealth(): Promise<SystemHealth> {
    const response = await api.get('/monitoring/health');
    return response.data.data;
  }

  /**
   * Obtém métricas de performance
   */
  static async getPerformanceMetrics(timeRange: '1h' | '24h' | '7d' = '24h'): Promise<PerformanceMetrics> {
    const response = await api.get(`/monitoring/performance?timeRange=${timeRange}`);
    return response.data.data;
  }

  /**
   * Obtém alertas ativos
   */
  static async getActiveAlerts(): Promise<Alert[]> {
    const response = await api.get('/monitoring/alerts');
    return response.data.data;
  }

  /**
   * Obtém histórico de alertas
   */
  static async getAlertHistory(limit: number = 100): Promise<Alert[]> {
    const response = await api.get(`/monitoring/alerts/history?limit=${limit}`);
    return response.data.data;
  }

  /**
   * Resolve um alerta manualmente
   */
  static async resolveAlert(alertId: string): Promise<void> {
    await api.post(`/monitoring/alerts/${alertId}/resolve`);
  }

  /**
   * Obtém regras de alerta configuradas
   */
  static async getAlertRules(): Promise<AlertRule[]> {
    const response = await api.get('/monitoring/alerts/rules');
    return response.data.data;
  }

  /**
   * Adiciona uma nova regra de alerta
   */
  static async addAlertRule(rule: Omit<AlertRule, 'id'>): Promise<string> {
    const response = await api.post('/monitoring/alerts/rules', rule);
    return response.data.data.id;
  }

  /**
   * Atualiza uma regra de alerta
   */
  static async updateAlertRule(ruleId: string, updates: Partial<AlertRule>): Promise<void> {
    await api.put(`/monitoring/alerts/rules/${ruleId}`, updates);
  }

  /**
   * Remove uma regra de alerta
   */
  static async removeAlertRule(ruleId: string): Promise<void> {
    await api.delete(`/monitoring/alerts/rules/${ruleId}`);
  }

  /**
   * Obtém dados consolidados para dashboard
   */
  static async getDashboardData(): Promise<DashboardData> {
    const response = await api.get('/monitoring/dashboard');
    return response.data.data;
  }

  /**
   * Obtém estatísticas resumidas para exibição rápida
   */
  static async getQuickStats(): Promise<{
    totalCommands: number;
    overallSuccessRate: number;
    activeAlerts: number;
    systemStatus: 'healthy' | 'degraded' | 'critical';
  }> {
    try {
      const [health, devices] = await Promise.all([
        this.getSystemHealth(),
        this.getDeviceMetrics()
      ]);

      const totalCommands = devices.reduce((sum, device) => sum + device.totalCommands, 0);
      const totalSuccessful = devices.reduce((sum, device) => sum + device.successfulCommands, 0);
      const overallSuccessRate = totalCommands > 0 ? totalSuccessful / totalCommands : 1;

      return {
        totalCommands,
        overallSuccessRate,
        activeAlerts: health.alerts.length,
        systemStatus: health.status
      };
    } catch (error) {
      console.error('Erro ao obter estatísticas rápidas:', error);
      return {
        totalCommands: 0,
        overallSuccessRate: 0,
        activeAlerts: 0,
        systemStatus: 'critical'
      };
    }
  }

  /**
   * Verifica se há problemas críticos no sistema
   */
  static async checkCriticalIssues(): Promise<{
    hasCriticalIssues: boolean;
    issues: string[];
  }> {
    try {
      const [health, alerts] = await Promise.all([
        this.getSystemHealth(),
        this.getActiveAlerts()
      ]);

      const issues: string[] = [];

      // Verificar status do sistema
      if (health.status === 'critical') {
        issues.push('Sistema em estado crítico');
      }

      // Verificar serviços down
      Object.entries(health.services).forEach(([service, status]) => {
        if (status === 'down') {
          issues.push(`Serviço ${service} está inativo`);
        }
      });

      // Verificar alertas críticos
      const criticalAlerts = alerts.filter(alert => alert.severity === 'critical');
      if (criticalAlerts.length > 0) {
        issues.push(`${criticalAlerts.length} alerta(s) crítico(s) ativo(s)`);
      }

      return {
        hasCriticalIssues: issues.length > 0,
        issues
      };
    } catch (error) {
      console.error('Erro ao verificar problemas críticos:', error);
      return {
        hasCriticalIssues: true,
        issues: ['Erro ao verificar status do sistema']
      };
    }
  }

  /**
   * Obtém recomendações baseadas nas métricas atuais
   */
  static async getRecommendations(): Promise<Array<{
    type: 'performance' | 'reliability' | 'maintenance';
    priority: 'low' | 'medium' | 'high';
    title: string;
    description: string;
    action?: string;
  }>> {
    try {
      const [devices, services] = await Promise.all([
        this.getDeviceMetrics(),
        this.getServiceMetrics()
      ]);

      const recommendations: Array<{
        type: 'performance' | 'reliability' | 'maintenance';
        priority: 'low' | 'medium' | 'high';
        title: string;
        description: string;
        action?: string;
      }> = [];

      // Analisar dispositivos com baixa taxa de sucesso
      devices.forEach(device => {
        if (device.successRate < 0.8 && device.totalCommands > 10) {
          recommendations.push({
            type: 'reliability',
            priority: device.successRate < 0.6 ? 'high' : 'medium',
            title: `Baixa confiabilidade - ${device.deviceType}`,
            description: `Taxa de sucesso de ${(device.successRate * 100).toFixed(1)}% para dispositivos ${device.deviceType}`,
            action: 'Verificar configurações e logs de erro'
          });
        }

        if (device.averageExecutionTime > 30000) { // > 30 segundos
          recommendations.push({
            type: 'performance',
            priority: 'medium',
            title: `Performance lenta - ${device.deviceType}`,
            description: `Tempo médio de execução de ${(device.averageExecutionTime / 1000).toFixed(1)}s`,
            action: 'Otimizar timeouts e configurações'
          });
        }
      });

      // Analisar balanceamento entre serviços
      const nodejsService = services.find(s => s.service === 'nodejs');
      const pythonService = services.find(s => s.service === 'python');

      if (nodejsService && pythonService) {
        const totalCommands = nodejsService.totalCommands + pythonService.totalCommands;
        const pythonRatio = pythonService.totalCommands / totalCommands;

        if (pythonRatio > 0.7) {
          recommendations.push({
            type: 'maintenance',
            priority: 'low',
            title: 'Alto uso do serviço Python',
            description: `${(pythonRatio * 100).toFixed(1)}% dos comandos estão sendo roteados para Python`,
            action: 'Considerar otimizações no serviço Node.js'
          });
        }
      }

      return recommendations;
    } catch (error) {
      console.error('Erro ao gerar recomendações:', error);
      return [];
    }
  }
}

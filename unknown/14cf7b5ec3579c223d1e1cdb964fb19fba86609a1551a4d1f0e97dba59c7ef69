import logging
import uvicorn
import os
from datetime import datetime
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from app.routers import ssh
from app.config import settings

# Configurar logging
log_level = getattr(logging, settings.log_level.upper(), logging.INFO)
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("logs/app.log")
    ]
)

logger = logging.getLogger(__name__)

# Criar diretório de logs se não existir
os.makedirs("logs", exist_ok=True)

app = FastAPI(
    title="SSH Service API",
    description="""
    API especializada para conexão SSH com dispositivos de rede problemáticos.
    
    Este microserviço foi desenvolvido especificamente para resolver problemas
    de conectividade e estabilidade com dispositivos HarmonyOS, Nokia, Mikrotik
    e outros equipamentos que apresentam dificuldades com implementações Node.js.
    
    ## Funcionalidades
    
    - **Execução de comandos SSH** otimizada para diferentes tipos de dispositivos
    - **Suporte especializado** para Huawei/HarmonyOS com timeouts estendidos
    - **Detecção automática** de informações do dispositivo
    - **Execução em lote** para múltiplos comandos
    - **Teste de conectividade** sem execução de comandos
    - **Estatísticas** de uso e performance
    
    ## Tipos de Dispositivos Suportados
    
    - Huawei/HarmonyOS (VRP)
    - Nokia (SR OS)
    - Mikrotik (RouterOS)
    - Cisco (IOS)
    - Linux
    - Windows
    - Generic (terminal server)
    """,
    version="1.0.0",
    debug=settings.debug,
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Em produção, especificar origins específicos
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Middleware para logging de requisições
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = datetime.now()
    
    # Log da requisição
    logger.info(f"Requisição: {request.method} {request.url}")
    
    response = await call_next(request)
    
    # Log da resposta
    process_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"Resposta: {response.status_code} - {process_time:.3f}s")
    
    return response

# Registrar routers
app.include_router(ssh.router)

@app.get("/", tags=["root"])
async def read_root():
    """
    Endpoint raiz com informações básicas do serviço
    """
    return {
        "message": "SSH Service API está funcionando",
        "service": "Python SSH Service",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health", tags=["health"])
async def health_check():
    """
    Health check detalhado do serviço
    """
    return {
        "status": "healthy",
        "service": "Python SSH Service",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "config": {
            "debug": settings.debug,
            "log_level": settings.log_level,
            "default_timeout": settings.default_timeout,
            "max_concurrent_connections": settings.max_concurrent_connections
        }
    }

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """
    Handler global para exceções não tratadas
    """
    logger.error(f"Erro não tratado: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Erro interno do servidor",
            "error": str(exc) if settings.debug else "Erro interno",
            "timestamp": datetime.now().isoformat()
        }
    )

if __name__ == "__main__":
    logger.info("Iniciando SSH Service API...")
    logger.info(f"Debug mode: {settings.debug}")
    logger.info(f"Log level: {settings.log_level}")
    
    uvicorn.run(
        "main:app", 
        host="0.0.0.0", 
        port=8000, 
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )

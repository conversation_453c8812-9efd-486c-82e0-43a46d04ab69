import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../ui/tabs';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Server, 
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';

interface DeviceMetrics {
  deviceType: string;
  totalCommands: number;
  successfulCommands: number;
  failedCommands: number;
  averageExecutionTime: number;
  successRate: number;
  lastFailure?: string;
  commonErrors: { [error: string]: number };
}

interface ServiceMetrics {
  service: 'nodejs' | 'python';
  totalCommands: number;
  successfulCommands: number;
  failedCommands: number;
  averageExecutionTime: number;
  successRate: number;
  uptime: number;
}

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'critical';
  services: {
    nodejs: 'up' | 'down' | 'degraded';
    python: 'up' | 'down' | 'degraded';
    database: 'up' | 'down' | 'degraded';
    redis: 'up' | 'down' | 'degraded';
  };
  alerts: Alert[];
  lastCheck: string;
}

interface Alert {
  id: string;
  ruleId: string;
  ruleName: string;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  resolved: boolean;
  metadata: any;
}

interface DashboardData {
  devices: DeviceMetrics[];
  services: ServiceMetrics[];
  health: SystemHealth;
  alerts: Alert[];
}

export const MonitoringDashboard: React.FC = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/monitoring/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Erro ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success) {
        setData(result.data);
        setError(null);
        setLastUpdate(new Date());
      } else {
        throw new Error(result.message || 'Erro ao carregar dados');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, []);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchDashboardData, 30000); // 30 segundos
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'up':
        return 'text-green-600 bg-green-100';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-100';
      case 'critical':
      case 'down':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low':
        return 'text-blue-600 bg-blue-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'high':
        return 'text-orange-600 bg-orange-100';
      case 'critical':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatExecutionTime = (ms: number) => {
    return ms < 1000 ? `${ms.toFixed(0)}ms` : `${(ms / 1000).toFixed(1)}s`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Carregando dados de monitoramento...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
          <span className="text-red-800">Erro ao carregar monitoramento: {error}</span>
        </div>
        <Button 
          onClick={fetchDashboardData} 
          className="mt-2"
          variant="outline"
          size="sm"
        >
          Tentar novamente
        </Button>
      </div>
    );
  }

  if (!data) {
    return <div>Nenhum dado disponível</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Monitoramento do Sistema</h1>
          <p className="text-gray-600">
            Última atualização: {lastUpdate?.toLocaleTimeString() || 'Nunca'}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={() => setAutoRefresh(!autoRefresh)}
            variant="outline"
            size="sm"
          >
            {autoRefresh ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
            Auto-refresh {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          <Button onClick={fetchDashboardData} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-1" />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Status Geral */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            Status Geral do Sistema
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <Badge className={getStatusColor(data.health.status)}>
                {data.health.status.toUpperCase()}
              </Badge>
              <p className="text-sm text-gray-600 mt-1">Sistema</p>
            </div>
            <div className="text-center">
              <Badge className={getStatusColor(data.health.services.nodejs)}>
                {data.health.services.nodejs.toUpperCase()}
              </Badge>
              <p className="text-sm text-gray-600 mt-1">Node.js</p>
            </div>
            <div className="text-center">
              <Badge className={getStatusColor(data.health.services.python)}>
                {data.health.services.python.toUpperCase()}
              </Badge>
              <p className="text-sm text-gray-600 mt-1">Python</p>
            </div>
            <div className="text-center">
              <Badge className={getStatusColor(data.health.services.database)}>
                {data.health.services.database.toUpperCase()}
              </Badge>
              <p className="text-sm text-gray-600 mt-1">Database</p>
            </div>
            <div className="text-center">
              <Badge className={getStatusColor(data.health.services.redis)}>
                {data.health.services.redis.toUpperCase()}
              </Badge>
              <p className="text-sm text-gray-600 mt-1">Redis</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alertas Ativos */}
      {data.alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
              Alertas Ativos ({data.alerts.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {data.alerts.slice(0, 5).map((alert) => (
                <div key={alert.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Badge className={getSeverityColor(alert.severity)}>
                      {alert.severity.toUpperCase()}
                    </Badge>
                    <div>
                      <p className="font-medium">{alert.ruleName}</p>
                      <p className="text-sm text-gray-600">{alert.message}</p>
                    </div>
                  </div>
                  <div className="text-sm text-gray-500">
                    {new Date(alert.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tabs para diferentes visualizações */}
      <Tabs defaultValue="services" className="w-full">
        <TabsList>
          <TabsTrigger value="services">Serviços</TabsTrigger>
          <TabsTrigger value="devices">Dispositivos</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        {/* Tab Serviços */}
        <TabsContent value="services">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {data.services.map((service) => (
              <Card key={service.service}>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Server className="h-5 w-5 mr-2" />
                    Serviço {service.service === 'nodejs' ? 'Node.js' : 'Python'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Taxa de Sucesso</p>
                      <p className="text-2xl font-bold text-green-600">
                        {(service.successRate * 100).toFixed(1)}%
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Tempo Médio</p>
                      <p className="text-2xl font-bold">
                        {formatExecutionTime(service.averageExecutionTime)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Total de Comandos</p>
                      <p className="text-lg font-semibold">{service.totalCommands}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Uptime</p>
                      <p className="text-lg font-semibold">{formatUptime(service.uptime)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Tab Dispositivos */}
        <TabsContent value="devices">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {data.devices.map((device) => (
              <Card key={device.deviceType}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{device.deviceType}</span>
                    <Badge className={device.successRate >= 0.9 ? 'bg-green-100 text-green-800' : 
                                    device.successRate >= 0.7 ? 'bg-yellow-100 text-yellow-800' : 
                                    'bg-red-100 text-red-800'}>
                      {(device.successRate * 100).toFixed(1)}%
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Comandos:</span>
                      <span className="font-medium">{device.totalCommands}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Sucessos:</span>
                      <span className="font-medium text-green-600">{device.successfulCommands}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Falhas:</span>
                      <span className="font-medium text-red-600">{device.failedCommands}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Tempo Médio:</span>
                      <span className="font-medium">{formatExecutionTime(device.averageExecutionTime)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Tab Performance */}
        <TabsContent value="performance">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Métricas de Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Clock className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600">Gráficos de performance em desenvolvimento</p>
                <p className="text-sm text-gray-500 mt-2">
                  Em breve: gráficos de tempo real, tendências e análises detalhadas
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

import axios, { AxiosResponse } from 'axios';
import { SSHServer, CommandResult } from '../../types/server';
import { Logger } from '../../utils/logger';

interface PythonSSHRequest {
  host: string;
  port: number;
  username: string;
  password?: string;
  private_key?: string;
  command: string;
  device_type: string;
  timeout: number;
}

interface PythonSSHResponse {
  stdout?: string;
  stderr?: string;
  code?: number;
  execution_time?: number;
  device_info?: {
    device_type: string;
    detected_os: string;
  };
}

/**
 * Cliente para comunicação com o microserviço Python SSH
 * Usado para dispositivos problemáticos que apresentam dificuldades com Node.js
 */
export class PythonSSHService {
  private baseURL: string;
  private timeout: number;

  constructor() {
    this.baseURL = process.env.PYTHON_SSH_SERVICE_URL || 'http://python-ssh:8000';
    this.timeout = parseInt(process.env.PYTHON_SSH_TIMEOUT || '120000'); // 2 minutos
  }

  /**
   * Determina se deve usar o serviço Python baseado no tipo de servidor
   */
  shouldUsePythonService(server: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>): boolean {
    // Usar Python para dispositivos problemáticos
    const problematicDevices = ['HUAWEI', 'NOKIA', 'DMOS'];
    
    // Verificar por tipo de dispositivo
    if (server.deviceType && problematicDevices.includes(server.deviceType)) {
      Logger.log(`Usando serviço Python para dispositivo ${server.deviceType}`);
      return true;
    }

    // Verificar por nome do servidor (fallback)
    const serverName = server.name.toLowerCase();
    if (serverName.includes('harmony') || 
        serverName.includes('huawei') || 
        serverName.includes('nokia') ||
        serverName.includes('rtr-pe-rbo')) {
      Logger.log(`Usando serviço Python para servidor ${server.name}`);
      return true;
    }

    // Verificar por configuração forçada
    if (process.env.FORCE_PYTHON_SSH === 'true') {
      Logger.log('Forçando uso do serviço Python via configuração');
      return true;
    }

    return false;
  }

  /**
   * Mapeia o tipo de dispositivo do sistema para o formato do Python
   */
  private mapDeviceType(deviceType?: string, serverName?: string): string {
    if (!deviceType) {
      // Tentar detectar pelo nome do servidor
      const name = (serverName || '').toLowerCase();
      if (name.includes('huawei') || name.includes('harmony')) return 'huawei';
      if (name.includes('nokia')) return 'nokia';
      if (name.includes('mikrotik')) return 'mikrotik';
      return 'generic';
    }

    const typeMap: { [key: string]: string } = {
      'HUAWEI': 'huawei',
      'NOKIA': 'nokia',
      'MIKROTIK': 'mikrotik',
      'DMOS': 'generic',
      'GENERIC': 'generic'
    };

    return typeMap[deviceType] || 'generic';
  }

  /**
   * Executa um comando usando o serviço Python
   */
  async executeCommand(
    server: Omit<SSHServer, 'commands' | 'userId' | 'createdAt' | 'updatedAt'>,
    command: string
  ): Promise<CommandResult> {
    try {
      Logger.log(`Executando comando via serviço Python: ${server.host}`);

      // Preparar requisição para o serviço Python
      const pythonRequest: PythonSSHRequest = {
        host: server.host,
        port: server.port,
        username: server.username,
        command: command.trim(),
        device_type: this.mapDeviceType(server.deviceType, server.name),
        timeout: this.determineTimeout(server.deviceType, command)
      };

      // Adicionar credenciais
      if (server.password) {
        pythonRequest.password = server.password;
      } else if (server.privateKey) {
        pythonRequest.private_key = server.privateKey;
      } else {
        throw new Error('Nenhum método de autenticação fornecido');
      }

      Logger.log(`Enviando requisição para ${this.baseURL}/ssh/execute`);

      // Fazer requisição para o serviço Python
      const response: AxiosResponse<PythonSSHResponse> = await axios.post(
        `${this.baseURL}/ssh/execute`,
        pythonRequest,
        {
          timeout: this.timeout,
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'NodeJS-SSH-Service/1.0'
          }
        }
      );

      Logger.log(`Comando executado com sucesso via Python em ${response.data.execution_time?.toFixed(2)}s`);

      // Converter resposta para o formato esperado
      return {
        stdout: response.data.stdout || '',
        stderr: response.data.stderr || '',
        code: response.data.code || 0
      };

    } catch (error) {
      Logger.error('Erro ao executar comando via serviço Python:', error);

      // Tratar diferentes tipos de erro
      if (axios.isAxiosError(error)) {
        if (error.response) {
          // Erro HTTP do serviço Python
          const errorData = error.response.data;
          const errorMessage = errorData?.detail || errorData?.message || 'Erro desconhecido do serviço Python';
          
          Logger.error(`Erro HTTP ${error.response.status}: ${errorMessage}`);
          
          return {
            stderr: `Erro do serviço Python (${error.response.status}): ${errorMessage}`,
            code: error.response.status
          };
        } else if (error.request) {
          // Erro de rede/conexão
          Logger.error('Erro de conexão com o serviço Python');
          return {
            stderr: 'Falha ao conectar ao serviço Python: Serviço indisponível',
            code: 503
          };
        }
      }

      // Erro genérico
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      return {
        stderr: `Falha no serviço Python: ${errorMessage}`,
        code: 500
      };
    }
  }

  /**
   * Determina o timeout apropriado baseado no tipo de dispositivo e comando
   */
  private determineTimeout(deviceType?: string, command?: string): number {
    // Timeouts base por tipo de dispositivo
    const baseTimeouts: { [key: string]: number } = {
      'HUAWEI': 90,
      'NOKIA': 75,
      'MIKROTIK': 60,
      'DMOS': 60,
      'GENERIC': 60
    };

    let timeout = baseTimeouts[deviceType || 'GENERIC'] || 60;

    // Ajustar timeout baseado na complexidade do comando
    if (command) {
      const commandLower = command.toLowerCase();
      
      // Comandos que tipicamente demoram mais
      if (commandLower.includes('backup') || 
          commandLower.includes('export') ||
          commandLower.includes('display current-configuration') ||
          commandLower.includes('show running-config')) {
        timeout += 60; // +1 minuto para comandos de configuração
      }

      // Comandos com múltiplas linhas
      if (command.includes('\n')) {
        const lineCount = command.split('\n').length;
        timeout += Math.min(lineCount * 5, 120); // +5s por linha, máximo +2 minutos
      }
    }

    // Limitar timeout máximo
    return Math.min(timeout, 300); // Máximo 5 minutos
  }

  /**
   * Testa a conectividade com o serviço Python
   */
  async testConnection(): Promise<boolean> {
    try {
      Logger.log('Testando conectividade com o serviço Python');
      
      const response = await axios.get(`${this.baseURL}/health`, {
        timeout: 5000 // 5 segundos para teste
      });

      const isHealthy = response.status === 200 && response.data?.status === 'healthy';
      
      if (isHealthy) {
        Logger.log('Serviço Python está saudável');
      } else {
        Logger.warn('Serviço Python respondeu mas não está saudável');
      }

      return isHealthy;
    } catch (error) {
      Logger.error('Falha ao conectar ao serviço Python:', error);
      return false;
    }
  }

  /**
   * Obtém estatísticas do serviço Python
   */
  async getStats(): Promise<any> {
    try {
      const response = await axios.get(`${this.baseURL}/ssh/stats`, {
        timeout: 5000
      });
      return response.data;
    } catch (error) {
      Logger.error('Erro ao obter estatísticas do serviço Python:', error);
      return null;
    }
  }
}

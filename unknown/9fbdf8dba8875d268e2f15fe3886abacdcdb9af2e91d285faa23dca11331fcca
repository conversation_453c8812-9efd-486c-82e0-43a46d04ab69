version: '3.8'

services:
  # Banco de dados PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: sem-fronteiras-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-sem_fronteiras}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups/postgres:/backups
    ports:
      - "5432:5432"
    networks:
      - sem-fronteiras-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis para cache
  redis:
    image: redis:7-alpine
    container_name: sem-fronteiras-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - sem-fronteiras-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Node.js
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: sem-fronteiras-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${POSTGRES_USER:-postgres}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-sem_fronteiras}
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      PORT: 3001
      PYTHON_SERVICE_URL: http://python-service:8000
      BACKUP_DIR: /app/backups
      AUTO_BACKUP_INTERVAL: 86400000
      LOG_LEVEL: info
    volumes:
      - ./backups:/app/backups
      - ./logs:/app/logs
    ports:
      - "3001:3001"
    networks:
      - sem-fronteiras-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Microserviço Python
  python-service:
    build:
      context: ./python-service
      dockerfile: Dockerfile.prod
    container_name: sem-fronteiras-python
    restart: unless-stopped
    environment:
      ENVIRONMENT: production
      LOG_LEVEL: INFO
      MAX_WORKERS: 4
      TIMEOUT: 300
    volumes:
      - ./logs:/app/logs
    ports:
      - "8000:8000"
    networks:
      - sem-fronteiras-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
      args:
        VITE_API_URL: ${VITE_API_URL:-http://localhost:3001}
    container_name: sem-fronteiras-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - sem-fronteiras-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (opcional, se não usar o frontend nginx)
  nginx:
    image: nginx:alpine
    container_name: sem-fronteiras-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx/nginx-proxy.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    networks:
      - sem-fronteiras-network
    depends_on:
      - backend
      - frontend
    profiles:
      - proxy

  # Monitoramento com Prometheus (opcional)
  prometheus:
    image: prom/prometheus:latest
    container_name: sem-fronteiras-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - sem-fronteiras-network
    profiles:
      - monitoring

  # Grafana para visualização (opcional)
  grafana:
    image: grafana/grafana:latest
    container_name: sem-fronteiras-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - sem-fronteiras-network
    profiles:
      - monitoring

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  sem-fronteiras-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

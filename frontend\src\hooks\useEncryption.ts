import { useState, useEffect, useCallback } from 'react';

export interface EncryptionConfig {
  algorithm: 'AES-256-GCM' | 'AES-256-CBC' | 'ChaCha20-Poly1305';
  keyDerivation: 'PBKDF2' | 'Argon2id' | 'scrypt';
  iterations: number;
  saltLength: number;
  enabled: boolean;
  rotationInterval: number; // dias
  lastRotation?: Date;
}

export interface EncryptedData {
  data: string; // Base64 encoded encrypted data
  iv: string; // Base64 encoded initialization vector
  salt: string; // Base64 encoded salt
  tag?: string; // Base64 encoded authentication tag (for GCM)
  algorithm: string;
  keyDerivation: string;
  iterations: number;
  timestamp: Date;
}

export interface EncryptionStats {
  totalEncryptedItems: number;
  encryptedCredentials: number;
  encryptedBackups: number;
  encryptedLogs: number;
  lastKeyRotation?: Date;
  nextKeyRotation?: Date;
  encryptionStrength: 'weak' | 'medium' | 'strong' | 'very-strong';
}

interface UseEncryptionReturn {
  config: EncryptionConfig;
  stats: EncryptionStats;
  isLoading: boolean;
  updateConfig: (newConfig: Partial<EncryptionConfig>) => Promise<boolean>;
  encryptData: (data: string, password?: string) => Promise<EncryptedData>;
  decryptData: (encryptedData: EncryptedData, password?: string) => Promise<string>;
  encryptCredentials: (credentials: Record<string, any>) => Promise<EncryptedData>;
  decryptCredentials: (encryptedData: EncryptedData) => Promise<Record<string, any>>;
  rotateEncryptionKey: (currentPassword: string, newPassword?: string) => Promise<boolean>;
  validateEncryption: (encryptedData: EncryptedData) => Promise<boolean>;
  exportEncryptedData: () => Promise<void>;
  importEncryptedData: (file: File, password: string) => Promise<boolean>;
  generateSecurePassword: (length?: number) => string;
  checkPasswordStrength: (password: string) => { score: number; feedback: string[] };
}

const DEFAULT_CONFIG: EncryptionConfig = {
  algorithm: 'AES-256-GCM',
  keyDerivation: 'PBKDF2',
  iterations: 100000,
  saltLength: 32,
  enabled: false,
  rotationInterval: 90 // 90 dias
};

const STORAGE_KEY = 'sem-fronteiras-encryption-config';

export const useEncryption = (): UseEncryptionReturn => {
  const [config, setConfig] = useState<EncryptionConfig>(DEFAULT_CONFIG);
  const [isLoading, setIsLoading] = useState(false);

  // Carregar configuração
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        setConfig({
          ...DEFAULT_CONFIG,
          ...parsed,
          lastRotation: parsed.lastRotation ? new Date(parsed.lastRotation) : undefined
        });
      }
    } catch (error) {
      console.error('Erro ao carregar configuração de criptografia:', error);
    }
  }, []);

  // Salvar configuração
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(config));
    } catch (error) {
      console.error('Erro ao salvar configuração de criptografia:', error);
    }
  }, [config]);

  // Funções de criptografia (simuladas - em produção usaria Web Crypto API)
  const generateSalt = (length: number): string => {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  };

  const generateIV = (): string => {
    const array = new Uint8Array(16); // 128 bits para AES
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  };

  const deriveKey = async (password: string, salt: string): Promise<string> => {
    // Simulação de derivação de chave
    // Em produção, usaria PBKDF2, Argon2 ou scrypt
    const encoder = new TextEncoder();
    const data = encoder.encode(password + salt);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = new Uint8Array(hashBuffer);
    return btoa(String.fromCharCode(...hashArray));
  };

  const encryptData = useCallback(async (data: string, password?: string): Promise<EncryptedData> => {
    if (!config.enabled) {
      throw new Error('Criptografia não está habilitada');
    }

    try {
      setIsLoading(true);
      
      const salt = generateSalt(config.saltLength);
      const iv = generateIV();
      const key = await deriveKey(password || 'default-key', salt);
      
      // Simulação de criptografia AES-256-GCM
      // Em produção, usaria Web Crypto API
      const encoder = new TextEncoder();
      const dataBytes = encoder.encode(data);
      
      // Simular criptografia
      const encryptedBytes = new Uint8Array(dataBytes.length);
      for (let i = 0; i < dataBytes.length; i++) {
        encryptedBytes[i] = dataBytes[i] ^ (key.charCodeAt(i % key.length));
      }
      
      const encryptedData = btoa(String.fromCharCode(...encryptedBytes));
      const tag = config.algorithm === 'AES-256-GCM' ? generateIV() : undefined;
      
      return {
        data: encryptedData,
        iv,
        salt,
        tag,
        algorithm: config.algorithm,
        keyDerivation: config.keyDerivation,
        iterations: config.iterations,
        timestamp: new Date()
      };
    } catch (error) {
      console.error('Erro ao criptografar dados:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [config]);

  const decryptData = useCallback(async (encryptedData: EncryptedData, password?: string): Promise<string> => {
    try {
      setIsLoading(true);
      
      const key = await deriveKey(password || 'default-key', encryptedData.salt);
      
      // Simulação de descriptografia
      const encryptedBytes = new Uint8Array(
        atob(encryptedData.data).split('').map(char => char.charCodeAt(0))
      );
      
      const decryptedBytes = new Uint8Array(encryptedBytes.length);
      for (let i = 0; i < encryptedBytes.length; i++) {
        decryptedBytes[i] = encryptedBytes[i] ^ (key.charCodeAt(i % key.length));
      }
      
      const decoder = new TextDecoder();
      return decoder.decode(decryptedBytes);
    } catch (error) {
      console.error('Erro ao descriptografar dados:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const encryptCredentials = useCallback(async (credentials: Record<string, any>): Promise<EncryptedData> => {
    const jsonData = JSON.stringify(credentials);
    return await encryptData(jsonData);
  }, [encryptData]);

  const decryptCredentials = useCallback(async (encryptedData: EncryptedData): Promise<Record<string, any>> => {
    const jsonData = await decryptData(encryptedData);
    return JSON.parse(jsonData);
  }, [decryptData]);

  const updateConfig = useCallback(async (newConfig: Partial<EncryptionConfig>): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // Simular validação da configuração
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setConfig(prev => ({ ...prev, ...newConfig }));
      return true;
    } catch (error) {
      console.error('Erro ao atualizar configuração:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const rotateEncryptionKey = useCallback(async (currentPassword: string, newPassword?: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      // Simular rotação de chave
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      setConfig(prev => ({
        ...prev,
        lastRotation: new Date()
      }));
      
      return true;
    } catch (error) {
      console.error('Erro ao rotacionar chave:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const validateEncryption = useCallback(async (encryptedData: EncryptedData): Promise<boolean> => {
    try {
      // Validar estrutura dos dados criptografados
      if (!encryptedData.data || !encryptedData.iv || !encryptedData.salt) {
        return false;
      }
      
      // Validar algoritmo suportado
      const supportedAlgorithms = ['AES-256-GCM', 'AES-256-CBC', 'ChaCha20-Poly1305'];
      if (!supportedAlgorithms.includes(encryptedData.algorithm)) {
        return false;
      }
      
      // Validar timestamp
      if (!encryptedData.timestamp || isNaN(new Date(encryptedData.timestamp).getTime())) {
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Erro ao validar criptografia:', error);
      return false;
    }
  }, []);

  const exportEncryptedData = useCallback(async (): Promise<void> => {
    try {
      // Simular coleta de dados criptografados
      const exportData = {
        config,
        exportedAt: new Date().toISOString(),
        version: '1.0.0',
        data: {
          credentials: 'encrypted_credentials_data',
          backups: 'encrypted_backups_data',
          logs: 'encrypted_logs_data'
        }
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `encrypted-data-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao exportar dados criptografados:', error);
      throw error;
    }
  }, [config]);

  const importEncryptedData = useCallback(async (file: File, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      
      const text = await file.text();
      const data = JSON.parse(text);
      
      // Validar estrutura do arquivo
      if (!data.config || !data.data) {
        throw new Error('Formato de arquivo inválido');
      }
      
      // Simular importação
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      return true;
    } catch (error) {
      console.error('Erro ao importar dados criptografados:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const generateSecurePassword = useCallback((length: number = 32): string => {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    
    return Array.from(array, byte => charset[byte % charset.length]).join('');
  }, []);

  const checkPasswordStrength = useCallback((password: string): { score: number; feedback: string[] } => {
    const feedback: string[] = [];
    let score = 0;
    
    // Comprimento
    if (password.length >= 12) score += 2;
    else if (password.length >= 8) score += 1;
    else feedback.push('Use pelo menos 8 caracteres');
    
    // Maiúsculas
    if (/[A-Z]/.test(password)) score += 1;
    else feedback.push('Inclua letras maiúsculas');
    
    // Minúsculas
    if (/[a-z]/.test(password)) score += 1;
    else feedback.push('Inclua letras minúsculas');
    
    // Números
    if (/\d/.test(password)) score += 1;
    else feedback.push('Inclua números');
    
    // Símbolos
    if (/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) score += 1;
    else feedback.push('Inclua símbolos especiais');
    
    // Padrões comuns
    if (!/(.)\1{2,}/.test(password)) score += 1;
    else feedback.push('Evite repetir caracteres');
    
    return { score: Math.min(score, 5), feedback };
  }, []);

  // Calcular estatísticas
  const stats: EncryptionStats = {
    totalEncryptedItems: config.enabled ? 150 : 0, // Mock data
    encryptedCredentials: config.enabled ? 45 : 0,
    encryptedBackups: config.enabled ? 30 : 0,
    encryptedLogs: config.enabled ? 75 : 0,
    lastKeyRotation: config.lastRotation,
    nextKeyRotation: config.lastRotation 
      ? new Date(config.lastRotation.getTime() + config.rotationInterval * 24 * 60 * 60 * 1000)
      : undefined,
    encryptionStrength: config.enabled 
      ? config.algorithm === 'AES-256-GCM' && config.iterations >= 100000 
        ? 'very-strong' 
        : 'strong'
      : 'weak'
  };

  return {
    config,
    stats,
    isLoading,
    updateConfig,
    encryptData,
    decryptData,
    encryptCredentials,
    decryptCredentials,
    rotateEncryptionKey,
    validateEncryption,
    exportEncryptedData,
    importEncryptedData,
    generateSecurePassword,
    checkPasswordStrength
  };
};

export default useEncryption;

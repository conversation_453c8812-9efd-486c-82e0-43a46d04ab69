import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
  Divider
} from '@mui/material';
import {
  Terminal as CommandIcon,
  Person as UserIcon,
  Computer as ServerIcon,
  Warning as AlertIcon,
  Backup as BackupIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { DashboardWidget } from '../../hooks/useDashboard';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface Activity {
  id: string;
  type: 'command' | 'user' | 'server' | 'alert' | 'backup' | 'system';
  title: string;
  description: string;
  user?: string;
  server?: string;
  timestamp: Date;
  status: 'success' | 'error' | 'warning' | 'info';
  details?: Record<string, any>;
}

interface ActivityWidgetProps {
  widget: DashboardWidget;
}

const ActivityWidget: React.FC<ActivityWidgetProps> = ({ widget }) => {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(false);

  // Mock data - em produção viria da API
  const generateMockActivities = (): Activity[] => [
    {
      id: '1',
      type: 'command',
      title: 'Comando executado',
      description: '/interface print',
      user: 'João Silva',
      server: 'Router Principal',
      timestamp: new Date(Date.now() - 120000),
      status: 'success',
      details: { duration: '2.3s', output: '15 interfaces encontradas' }
    },
    {
      id: '2',
      type: 'user',
      title: 'Usuário logado',
      description: 'Maria Santos fez login no sistema',
      user: 'Maria Santos',
      timestamp: new Date(Date.now() - 300000),
      status: 'info'
    },
    {
      id: '3',
      type: 'alert',
      title: 'Alerta gerado',
      description: 'CPU alta detectada no Switch Core',
      server: 'Switch Core',
      timestamp: new Date(Date.now() - 450000),
      status: 'warning',
      details: { severity: 'medium', value: '85%' }
    },
    {
      id: '4',
      type: 'command',
      title: 'Comando falhou',
      description: 'display version',
      user: 'João Silva',
      server: 'OLT Fibra',
      timestamp: new Date(Date.now() - 600000),
      status: 'error',
      details: { error: 'Timeout na conexão' }
    },
    {
      id: '5',
      type: 'backup',
      title: 'Backup concluído',
      description: 'Backup automático realizado com sucesso',
      timestamp: new Date(Date.now() - 900000),
      status: 'success',
      details: { size: '2.3 MB', duration: '45s' }
    },
    {
      id: '6',
      type: 'server',
      title: 'Servidor reconectado',
      description: 'Backup Router voltou online',
      server: 'Backup Router',
      timestamp: new Date(Date.now() - 1200000),
      status: 'success'
    },
    {
      id: '7',
      type: 'system',
      title: 'Configuração alterada',
      description: 'Timeout de comandos atualizado para 30s',
      user: 'Admin',
      timestamp: new Date(Date.now() - 1800000),
      status: 'info'
    },
    {
      id: '8',
      type: 'command',
      title: 'Template executado',
      description: 'Backup Configuração aplicado',
      user: 'Maria Santos',
      server: 'Router Principal',
      timestamp: new Date(Date.now() - 2400000),
      status: 'success',
      details: { template: 'Backup Configuração', commands: 3 }
    }
  ];

  useEffect(() => {
    loadActivities();
    
    const interval = setInterval(() => {
      loadActivities();
    }, widget.refreshInterval || 30000);

    return () => clearInterval(interval);
  }, [widget.refreshInterval]);

  const loadActivities = async () => {
    setLoading(true);
    try {
      // Simular carregamento
      await new Promise(resolve => setTimeout(resolve, 300));
      
      let newActivities = generateMockActivities();
      
      // Ordenar por timestamp (mais recente primeiro)
      newActivities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      
      // Aplicar limite de itens
      if (widget.config.maxItems) {
        newActivities = newActivities.slice(0, widget.config.maxItems);
      }
      
      setActivities(newActivities);
    } catch (error) {
      console.error('Erro ao carregar atividades:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'command':
        return <CommandIcon />;
      case 'user':
        return <UserIcon />;
      case 'server':
        return <ServerIcon />;
      case 'alert':
        return <AlertIcon />;
      case 'backup':
        return <BackupIcon />;
      case 'system':
        return <SettingsIcon />;
      default:
        return <CommandIcon />;
    }
  };

  const getActivityColor = (type: Activity['type']) => {
    switch (type) {
      case 'command':
        return '#1976d2';
      case 'user':
        return '#388e3c';
      case 'server':
        return '#f57c00';
      case 'alert':
        return '#d32f2f';
      case 'backup':
        return '#7b1fa2';
      case 'system':
        return '#455a64';
      default:
        return '#757575';
    }
  };

  const getStatusIcon = (status: Activity['status']) => {
    switch (status) {
      case 'success':
        return <SuccessIcon color="success" fontSize="small" />;
      case 'error':
        return <ErrorIcon color="error" fontSize="small" />;
      case 'warning':
        return <AlertIcon color="warning" fontSize="small" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: Activity['status']) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
        return 'info';
      default:
        return 'default';
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Agora';
    if (minutes < 60) return `${minutes}m atrás`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h atrás`;
    
    const days = Math.floor(hours / 24);
    return `${days}d atrás`;
  };

  const getTypeText = (type: Activity['type']) => {
    switch (type) {
      case 'command':
        return 'Comando';
      case 'user':
        return 'Usuário';
      case 'server':
        return 'Servidor';
      case 'alert':
        return 'Alerta';
      case 'backup':
        return 'Backup';
      case 'system':
        return 'Sistema';
      default:
        return 'Atividade';
    }
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Cabeçalho */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="subtitle2" color="text.secondary">
          {activities.length} atividade(s) recente(s)
        </Typography>
        <Tooltip title="Atualizar">
          <IconButton size="small" onClick={loadActivities} disabled={loading}>
            <RefreshIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Loading */}
      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {/* Lista de atividades */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {activities.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <CommandIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              Nenhuma atividade recente
            </Typography>
          </Box>
        ) : (
          <List dense>
            {activities.map((activity, index) => (
              <React.Fragment key={activity.id}>
                <ListItem alignItems="flex-start">
                  <ListItemIcon>
                    <Avatar
                      sx={{
                        width: 32,
                        height: 32,
                        bgcolor: getActivityColor(activity.type),
                        fontSize: '0.875rem'
                      }}
                    >
                      {getActivityIcon(activity.type)}
                    </Avatar>
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                        <Typography variant="body2" fontWeight="medium">
                          {activity.title}
                        </Typography>
                        {getStatusIcon(activity.status)}
                        <Chip
                          label={getTypeText(activity.type)}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.6rem', height: 20 }}
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                          {activity.description}
                        </Typography>
                        
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                          <Typography variant="caption" color="text.secondary">
                            {formatTimeAgo(activity.timestamp)}
                          </Typography>
                          
                          {activity.user && (
                            <Chip
                              label={activity.user}
                              size="small"
                              color="primary"
                              variant="outlined"
                              sx={{ fontSize: '0.6rem', height: 18 }}
                            />
                          )}
                          
                          {activity.server && (
                            <Chip
                              label={activity.server}
                              size="small"
                              color="secondary"
                              variant="outlined"
                              sx={{ fontSize: '0.6rem', height: 18 }}
                            />
                          )}
                        </Box>
                        
                        {activity.details && widget.config.showDetails && (
                          <Box sx={{ mt: 0.5 }}>
                            {Object.entries(activity.details).map(([key, value]) => (
                              <Typography key={key} variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                                {key}: {value}
                              </Typography>
                            ))}
                          </Box>
                        )}
                      </Box>
                    }
                  />
                </ListItem>
                {index < activities.length - 1 && <Divider variant="inset" component="li" />}
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>

      {/* Estatísticas resumidas */}
      <Box sx={{ mt: 'auto', pt: 2, borderTop: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-around', textAlign: 'center' }}>
          <Box>
            <Typography variant="h6" color="success.main">
              {activities.filter(a => a.status === 'success').length}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Sucessos
            </Typography>
          </Box>
          <Box>
            <Typography variant="h6" color="warning.main">
              {activities.filter(a => a.status === 'warning').length}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Avisos
            </Typography>
          </Box>
          <Box>
            <Typography variant="h6" color="error.main">
              {activities.filter(a => a.status === 'error').length}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Erros
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ActivityWidget;

export interface ServerGroup {
  id: string
  name: string
  description?: string
  color?: string
  userId: string
  createdAt: string
  updatedAt: string
  members?: ServerGroupMember[]
  _count?: {
    members: number
  }
}

export interface ServerGroupMember {
  id: string
  groupId: string
  serverId: string
  createdAt: string
  updatedAt: string
  group?: {
    id: string
    name: string
    color?: string
  }
  server?: {
    id: string
    name: string
    host: string
    port: number
    deviceType: string
  }
}

export interface CreateServerGroupDTO {
  name: string
  description?: string
  color?: string
}

export interface UpdateServerGroupDTO {
  name?: string
  description?: string
  color?: string
}

export interface AddServerToGroupDTO {
  serverId: string
}

export interface ServerGroupWithServers extends ServerGroup {
  members: (ServerGroupMember & {
    server: {
      id: string
      name: string
      host: string
      port: number
      deviceType: string
    }
  })[]
}

// Cores predefinidas para os grupos
export const GROUP_COLORS = [
  '#3B82F6', // Blue
  '#10B981', // Green
  '#F59E0B', // Yellow
  '#EF4444', // Red
  '#8B5CF6', // Purple
  '#06B6D4', // Cyan
  '#F97316', // Orange
  '#84CC16', // Lime
  '#EC4899', // Pink
  '#6B7280', // Gray
] as const

export type GroupColor = typeof GROUP_COLORS[number]

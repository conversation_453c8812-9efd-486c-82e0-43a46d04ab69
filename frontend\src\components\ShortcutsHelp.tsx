import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  Divider,
  Grid,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  IconButton
} from '@mui/material';
import {
  Keyboard as KeyboardIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { 
  useGlobalShortcuts, 
  formatShortcut, 
  groupShortcutsByCategory,
  KeyboardShortcut 
} from '../hooks/useKeyboardShortcuts';

interface ShortcutsHelpProps {
  open?: boolean;
  onClose?: () => void;
  additionalShortcuts?: KeyboardShortcut[];
}

export const ShortcutsHelp: React.FC<ShortcutsHelpProps> = ({
  open: controlledOpen,
  onClose: controlledOnClose,
  additionalShortcuts = []
}) => {
  const [internalOpen, setInternalOpen] = useState(false);
  const globalShortcuts = useGlobalShortcuts();

  const isControlled = controlledOpen !== undefined;
  const open = isControlled ? controlledOpen : internalOpen;
  const onClose = isControlled ? controlledOnClose : () => setInternalOpen(false);

  // Escutar evento global para mostrar ajuda
  useEffect(() => {
    const handleShowHelp = () => {
      if (!isControlled) {
        setInternalOpen(true);
      }
    };

    window.addEventListener('show-shortcuts-help', handleShowHelp);
    return () => {
      window.removeEventListener('show-shortcuts-help', handleShowHelp);
    };
  }, [isControlled]);

  // Combinar atalhos globais com atalhos adicionais
  const allShortcuts = [...globalShortcuts, ...additionalShortcuts];
  const groupedShortcuts = groupShortcutsByCategory(allShortcuts);

  const categoryIcons: Record<string, string> = {
    'Geral': '⚙️',
    'Navegação': '🧭',
    'Comandos': '💻',
    'Outros': '📋'
  };

  const categoryColors: Record<string, 'primary' | 'secondary' | 'success' | 'warning'> = {
    'Geral': 'primary',
    'Navegação': 'secondary',
    'Comandos': 'success',
    'Outros': 'warning'
  };

  return (
    <Dialog 
      open={open || false} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { minHeight: '60vh' }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <KeyboardIcon color="primary" />
            <Typography variant="h6">Atalhos de Teclado</Typography>
          </Box>
          {onClose && (
            <IconButton onClick={onClose} size="small">
              <CloseIcon />
            </IconButton>
          )}
        </Box>
      </DialogTitle>

      <DialogContent>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Use estes atalhos para navegar mais rapidamente pela aplicação.
        </Typography>

        <Grid container spacing={3}>
          {Object.entries(groupedShortcuts).map(([category, shortcuts]) => (
            <Grid item xs={12} md={6} key={category}>
              <Card variant="outlined">
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                    <Typography variant="h6" component="div">
                      {categoryIcons[category]} {category}
                    </Typography>
                    <Chip 
                      label={shortcuts.length} 
                      size="small" 
                      color={categoryColors[category] || 'default'}
                    />
                  </Box>
                  
                  <List dense>
                    {shortcuts.map((shortcut, index) => (
                      <ListItem key={index} sx={{ px: 0 }}>
                        <ListItemText
                          primary={
                            <Box sx={{ 
                              display: 'flex', 
                              justifyContent: 'space-between', 
                              alignItems: 'center',
                              gap: 2
                            }}>
                              <Typography variant="body2">
                                {shortcut.description}
                              </Typography>
                              <Chip
                                label={formatShortcut(shortcut)}
                                size="small"
                                variant="outlined"
                                sx={{ 
                                  fontFamily: 'monospace',
                                  fontSize: '0.75rem',
                                  minWidth: 'auto'
                                }}
                              />
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ backgroundColor: 'background.paper', p: 2, borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            💡 Dicas:
          </Typography>
          <Typography variant="body2" color="text.secondary" component="div">
            <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>
              <li>Os atalhos funcionam em qualquer lugar da aplicação</li>
              <li>Alguns atalhos podem não funcionar quando você está digitando em campos de texto</li>
              <li>Use <strong>Ctrl + H</strong> para abrir esta ajuda a qualquer momento</li>
              <li>No Mac, use <strong>Cmd</strong> no lugar de <strong>Ctrl</strong></li>
            </ul>
          </Typography>
        </Box>

        <Box sx={{ mt: 2, p: 2, backgroundColor: 'info.light', borderRadius: 1 }}>
          <Typography variant="subtitle2" color="info.contrastText" gutterBottom>
            🚀 Atalhos Específicos por Página:
          </Typography>
          <Typography variant="body2" color="info.contrastText">
            Algumas páginas têm atalhos específicos adicionais. Eles serão mostrados aqui quando você estiver nessas páginas.
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="contained">
          Entendi
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// Hook para usar o componente de ajuda
export const useShortcutsHelp = () => {
  const [open, setOpen] = useState(false);

  const showHelp = () => setOpen(true);
  const hideHelp = () => setOpen(false);

  return {
    open,
    showHelp,
    hideHelp,
    ShortcutsHelpComponent: () => (
      <ShortcutsHelp open={open} onClose={hideHelp} />
    )
  };
};

export default ShortcutsHelp;

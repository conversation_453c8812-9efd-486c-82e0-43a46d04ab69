import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Checkbox,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Tooltip,
  Menu,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Restore as RestoreIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  DeleteForever as DeleteForeverIcon,
  CleaningServices as CleanupIcon,
  Download as ExportIcon,
  MoreVert as MoreIcon,
  Visibility as ViewIcon,
  Computer as ServerIcon,
  Person as UserIcon,
  Description as TemplateIcon,
  Group as GroupIcon,
  Terminal as CommandIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ptBR } from 'date-fns/locale';
import { useTrash, TrashItem } from '../hooks/useTrash';
import { format } from 'date-fns';

const TrashManagement: React.FC = () => {
  const {
    items,
    stats,
    filter,
    isLoading,
    updateFilter,
    restoreItem,
    restoreMultiple,
    permanentlyDelete,
    permanentlyDeleteMultiple,
    emptyTrash,
    cleanupExpired,
    exportTrash
  } = useTrash();

  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [showFilters, setShowFilters] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmAction, setConfirmAction] = useState<'restore' | 'delete' | 'empty' | 'cleanup'>('restore');
  const [selectedItem, setSelectedItem] = useState<TrashItem | null>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState<{ element: HTMLElement; itemId: string } | null>(null);

  const getTypeIcon = (type: TrashItem['type']) => {
    switch (type) {
      case 'server': return <ServerIcon fontSize="small" />;
      case 'user': return <UserIcon fontSize="small" />;
      case 'template': return <TemplateIcon fontSize="small" />;
      case 'group': return <GroupIcon fontSize="small" />;
      case 'command': return <CommandIcon fontSize="small" />;
      default: return null;
    }
  };

  const getTypeColor = (type: TrashItem['type']) => {
    switch (type) {
      case 'server': return 'primary';
      case 'user': return 'secondary';
      case 'template': return 'info';
      case 'group': return 'success';
      case 'command': return 'warning';
      default: return 'default';
    }
  };

  const getTypeLabel = (type: TrashItem['type']) => {
    switch (type) {
      case 'server': return 'Servidor';
      case 'user': return 'Usuário';
      case 'template': return 'Template';
      case 'group': return 'Grupo';
      case 'command': return 'Comando';
      default: return type;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const isExpired = (item: TrashItem) => item.expiresAt < new Date();

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(items.map(item => item.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, itemId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  const handleConfirmAction = async () => {
    try {
      switch (confirmAction) {
        case 'restore':
          if (selectedItems.length > 0) {
            await restoreMultiple(selectedItems);
            setSelectedItems([]);
          }
          break;
        case 'delete':
          if (selectedItems.length > 0) {
            await permanentlyDeleteMultiple(selectedItems);
            setSelectedItems([]);
          }
          break;
        case 'empty':
          await emptyTrash();
          setSelectedItems([]);
          break;
        case 'cleanup':
          await cleanupExpired();
          break;
      }
      setShowConfirmDialog(false);
    } catch (error) {
      console.error('Erro na ação:', error);
    }
  };

  const handleViewDetails = (item: TrashItem) => {
    setSelectedItem(item);
    setShowDetailsDialog(true);
    setMenuAnchor(null);
  };

  const handleSingleRestore = async (itemId: string) => {
    await restoreItem(itemId);
    setMenuAnchor(null);
  };

  const handleSingleDelete = async (itemId: string) => {
    await permanentlyDelete(itemId);
    setMenuAnchor(null);
  };

  const paginatedItems = items.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
      <Container maxWidth="xl">
        {/* Cabeçalho */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Lixeira
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Gerencie itens excluídos e recupere dados quando necessário
          </Typography>
        </Box>

        {/* Estatísticas */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary">
                  {stats.totalItems}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total de Itens
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="error.main">
                  {stats.expiredItems}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Expirados
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main">
                  {stats.recentlyDeleted}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Últimas 24h
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="info.main">
                  {formatFileSize(stats.spaceUsed)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Espaço Utilizado
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {Object.entries(stats.itemsByType).map(([type, count]) => (
                  <Chip
                    key={type}
                    icon={getTypeIcon(type as TrashItem['type'])}
                    label={`${getTypeLabel(type as TrashItem['type'])}: ${count}`}
                    size="small"
                    color={getTypeColor(type as TrashItem['type']) as any}
                    variant="outlined"
                  />
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Controles */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Buscar na lixeira..."
                  value={filter.searchTerm}
                  onChange={(e) => updateFilter({ searchTerm: e.target.value })}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>

              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Tipo</InputLabel>
                  <Select
                    multiple
                    value={filter.types}
                    onChange={(e) => updateFilter({ types: e.target.value as TrashItem['type'][] })}
                    renderValue={(selected) => `${selected.length} selecionados`}
                  >
                    {['server', 'user', 'template', 'group', 'command'].map((type) => (
                      <MenuItem key={type} value={type}>
                        <Checkbox checked={filter.types.includes(type as TrashItem['type'])} />
                        <ListItemIcon>
                          {getTypeIcon(type as TrashItem['type'])}
                        </ListItemIcon>
                        <ListItemText primary={getTypeLabel(type as TrashItem['type'])} />
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  <Button
                    startIcon={<FilterIcon />}
                    onClick={() => setShowFilters(!showFilters)}
                    variant={showFilters ? 'contained' : 'outlined'}
                  >
                    Filtros
                  </Button>

                  <Button
                    startIcon={<RestoreIcon />}
                    onClick={() => {
                      setConfirmAction('restore');
                      setShowConfirmDialog(true);
                    }}
                    disabled={selectedItems.length === 0}
                  >
                    Restaurar ({selectedItems.length})
                  </Button>

                  <Button
                    startIcon={<DeleteForeverIcon />}
                    onClick={() => {
                      setConfirmAction('delete');
                      setShowConfirmDialog(true);
                    }}
                    disabled={selectedItems.length === 0}
                    color="error"
                  >
                    Excluir ({selectedItems.length})
                  </Button>

                  <Button
                    startIcon={<CleanupIcon />}
                    onClick={() => {
                      setConfirmAction('cleanup');
                      setShowConfirmDialog(true);
                    }}
                    disabled={stats.expiredItems === 0}
                  >
                    Limpar Expirados
                  </Button>

                  <Button
                    startIcon={<ExportIcon />}
                    onClick={exportTrash}
                  >
                    Exportar
                  </Button>

                  <Button
                    startIcon={<DeleteIcon />}
                    onClick={() => {
                      setConfirmAction('empty');
                      setShowConfirmDialog(true);
                    }}
                    color="error"
                    variant="outlined"
                  >
                    Esvaziar Lixeira
                  </Button>
                </Box>
              </Grid>
            </Grid>

            {/* Filtros avançados */}
            {showFilters && (
              <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={3}>
                    <DatePicker
                      label="Data Inicial"
                      value={filter.dateRange.start}
                      onChange={(date) => date && updateFilter({
                        dateRange: { ...filter.dateRange, start: date }
                      })}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <DatePicker
                      label="Data Final"
                      value={filter.dateRange.end}
                      onChange={(date) => date && updateFilter({
                        dateRange: { ...filter.dateRange, end: date }
                      })}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                    />
                  </Grid>

                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Excluído por"
                      value={filter.deletedBy || ''}
                      onChange={(e) => updateFilter({ deletedBy: e.target.value || undefined })}
                    />
                  </Grid>

                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <InputLabel>Mostrar Expirados</InputLabel>
                      <Select
                        value={filter.showExpired ? 'yes' : 'no'}
                        onChange={(e) => updateFilter({ showExpired: e.target.value === 'yes' })}
                      >
                        <MenuItem value="no">Não</MenuItem>
                        <MenuItem value="yes">Sim</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Tabela */}
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell padding="checkbox">
                    <Checkbox
                      checked={selectedItems.length === items.length && items.length > 0}
                      indeterminate={selectedItems.length > 0 && selectedItems.length < items.length}
                      onChange={(e) => handleSelectAll(e.target.checked)}
                    />
                  </TableCell>
                  <TableCell>Tipo</TableCell>
                  <TableCell>Nome</TableCell>
                  <TableCell>Excluído por</TableCell>
                  <TableCell>Data de Exclusão</TableCell>
                  <TableCell>Expira em</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Ações</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedItems.map((item) => (
                  <TableRow key={item.id} hover>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedItems.includes(item.id)}
                        onChange={(e) => handleSelectItem(item.id, e.target.checked)}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getTypeIcon(item.type)}
                        label={getTypeLabel(item.type)}
                        size="small"
                        color={getTypeColor(item.type) as any}
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {item.name}
                      </Typography>
                      {item.reason && (
                        <Typography variant="caption" color="text.secondary">
                          {item.reason}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>{item.deletedBy}</TableCell>
                    <TableCell>
                      {format(item.deletedAt, 'dd/MM/yyyy HH:mm')}
                    </TableCell>
                    <TableCell>
                      {format(item.expiresAt, 'dd/MM/yyyy')}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={isExpired(item) ? 'Expirado' : 'Ativo'}
                        color={isExpired(item) ? 'error' : 'success'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton
                        onClick={(e) => setMenuAnchor({ element: e.currentTarget, itemId: item.id })}
                      >
                        <MoreIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            component="div"
            count={items.length}
            page={page}
            onPageChange={(_, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(e) => setRowsPerPage(parseInt(e.target.value))}
            rowsPerPageOptions={[10, 25, 50, 100]}
            labelRowsPerPage="Itens por página:"
          />
        </Card>

        {/* Menu de ações */}
        <Menu
          anchorEl={menuAnchor?.element}
          open={Boolean(menuAnchor)}
          onClose={() => setMenuAnchor(null)}
        >
          <MenuItem onClick={() => {
            const item = items.find(i => i.id === menuAnchor?.itemId);
            if (item) handleViewDetails(item);
          }}>
            <ListItemIcon>
              <ViewIcon />
            </ListItemIcon>
            <ListItemText>Ver Detalhes</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => {
            if (menuAnchor) handleSingleRestore(menuAnchor.itemId);
          }}>
            <ListItemIcon>
              <RestoreIcon />
            </ListItemIcon>
            <ListItemText>Restaurar</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => {
            if (menuAnchor) handleSingleDelete(menuAnchor.itemId);
          }}>
            <ListItemIcon>
              <DeleteForeverIcon />
            </ListItemIcon>
            <ListItemText>Excluir Permanentemente</ListItemText>
          </MenuItem>
        </Menu>

        {/* Dialog de confirmação */}
        <Dialog open={showConfirmDialog} onClose={() => setShowConfirmDialog(false)}>
          <DialogTitle>
            Confirmar Ação
          </DialogTitle>
          <DialogContent>
            {confirmAction === 'restore' && (
              <Typography>
                Tem certeza que deseja restaurar {selectedItems.length} item(s) selecionado(s)?
              </Typography>
            )}
            {confirmAction === 'delete' && (
              <Alert severity="error">
                Esta ação irá excluir permanentemente {selectedItems.length} item(s). 
                Esta ação não pode ser desfeita.
              </Alert>
            )}
            {confirmAction === 'empty' && (
              <Alert severity="error">
                Esta ação irá esvaziar completamente a lixeira, excluindo permanentemente 
                todos os {stats.totalItems} itens. Esta ação não pode ser desfeita.
              </Alert>
            )}
            {confirmAction === 'cleanup' && (
              <Typography>
                Esta ação irá excluir permanentemente {stats.expiredItems} item(s) expirado(s).
              </Typography>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowConfirmDialog(false)}>
              Cancelar
            </Button>
            <Button
              onClick={handleConfirmAction}
              color={confirmAction === 'restore' ? 'primary' : 'error'}
              variant="contained"
            >
              Confirmar
            </Button>
          </DialogActions>
        </Dialog>

        {/* Dialog de detalhes */}
        <Dialog open={showDetailsDialog} onClose={() => setShowDetailsDialog(false)} maxWidth="md" fullWidth>
          <DialogTitle>
            Detalhes do Item
          </DialogTitle>
          <DialogContent>
            {selectedItem && (
              <Box sx={{ pt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Nome
                    </Typography>
                    <Typography variant="body2">
                      {selectedItem.name}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Tipo
                    </Typography>
                    <Chip
                      icon={getTypeIcon(selectedItem.type)}
                      label={getTypeLabel(selectedItem.type)}
                      color={getTypeColor(selectedItem.type) as any}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Excluído por
                    </Typography>
                    <Typography variant="body2">
                      {selectedItem.deletedBy}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Data de Exclusão
                    </Typography>
                    <Typography variant="body2">
                      {format(selectedItem.deletedAt, 'dd/MM/yyyy HH:mm:ss')}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>
                      Dados Originais
                    </Typography>
                    <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                      <pre style={{ margin: 0, fontSize: '0.875rem', overflow: 'auto' }}>
                        {JSON.stringify(selectedItem.data, null, 2)}
                      </pre>
                    </Paper>
                  </Grid>
                </Grid>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowDetailsDialog(false)}>
              Fechar
            </Button>
            {selectedItem && (
              <>
                <Button
                  onClick={() => {
                    handleSingleRestore(selectedItem.id);
                    setShowDetailsDialog(false);
                  }}
                  startIcon={<RestoreIcon />}
                >
                  Restaurar
                </Button>
                <Button
                  onClick={() => {
                    handleSingleDelete(selectedItem.id);
                    setShowDetailsDialog(false);
                  }}
                  startIcon={<DeleteForeverIcon />}
                  color="error"
                >
                  Excluir Permanentemente
                </Button>
              </>
            )}
          </DialogActions>
        </Dialog>
      </Container>
    </LocalizationProvider>
  );
};

export default TrashManagement;

import { CacheService } from '../../../services/cache/CacheService';
import { CommandResult } from '../../../types/server';

// Mock do Redis
const mockRedis = {
  connect: jest.fn().mockResolvedValue(undefined),
  get: jest.fn(),
  setex: jest.fn(),
  del: jest.fn(),
  keys: jest.fn(),
  flushdb: jest.fn(),
  info: jest.fn(),
  config: jest.fn(),
  quit: jest.fn(),
  on: jest.fn(),
};

jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => mockRedis);
});

describe('CacheService', () => {
  let cacheService: CacheService;

  beforeEach(() => {
    jest.clearAllMocks();
    cacheService = new CacheService();
  });

  describe('isCacheableCommand', () => {
    it('should identify cacheable read commands', async () => {
      const serverId = 'server-1';
      const readCommands = [
        'display version',
        'show version',
        'display current-configuration',
        'show running-config',
        'display interface',
        '/system identity print'
      ];

      for (const command of readCommands) {
        mockRedis.get.mockResolvedValueOnce(null);
        const result = await cacheService.get(serverId, command);
        expect(result).toBeNull(); // Não está no cache, mas é cacheável
      }
    });

    it('should not cache write commands', async () => {
      const serverId = 'server-1';
      const writeCommands = [
        'configure terminal',
        'set interface eth0',
        'add user test',
        'delete route 192.168.1.0/24',
        'modify system name'
      ];

      for (const command of writeCommands) {
        const result = await cacheService.get(serverId, command);
        expect(result).toBeNull();
        expect(mockRedis.get).not.toHaveBeenCalled();
      }
    });
  });

  describe('cache operations', () => {
    it('should cache and retrieve successful command results', async () => {
      const serverId = 'server-1';
      const command = 'display version';
      const commandResult: CommandResult = {
        stdout: 'VRP (R) software, Version 8.180',
        code: 0
      };

      // Mock Redis para simular cache miss e depois cache hit
      mockRedis.get
        .mockResolvedValueOnce(null) // Primeiro get - cache miss
        .mockResolvedValueOnce(JSON.stringify({
          result: commandResult,
          timestamp: Date.now(),
          serverId,
          command,
          hits: 1
        })); // Segundo get - cache hit

      // Primeira busca - cache miss
      const firstResult = await cacheService.get(serverId, command);
      expect(firstResult).toBeNull();

      // Armazenar no cache
      await cacheService.set(serverId, command, commandResult);
      expect(mockRedis.setex).toHaveBeenCalled();

      // Segunda busca - cache hit
      const secondResult = await cacheService.get(serverId, command);
      expect(secondResult).toEqual(commandResult);
    });

    it('should not cache failed command results', async () => {
      const serverId = 'server-1';
      const command = 'display version';
      const failedResult: CommandResult = {
        stderr: 'Connection timeout',
        code: 1
      };

      await cacheService.set(serverId, command, failedResult);
      expect(mockRedis.setex).not.toHaveBeenCalled();
    });

    it('should determine appropriate TTL based on command type', async () => {
      const serverId = 'server-1';
      const commandResult: CommandResult = {
        stdout: 'Success',
        code: 0
      };

      // Comando de versão - TTL longo (3600s)
      await cacheService.set(serverId, 'display version', commandResult);
      expect(mockRedis.setex).toHaveBeenCalledWith(
        expect.any(String),
        3600,
        expect.any(String)
      );

      mockRedis.setex.mockClear();

      // Comando de configuração - TTL médio (1800s)
      await cacheService.set(serverId, 'display current-configuration', commandResult);
      expect(mockRedis.setex).toHaveBeenCalledWith(
        expect.any(String),
        1800,
        expect.any(String)
      );

      mockRedis.setex.mockClear();

      // Comando dinâmico (ARP) - TTL curto (120s)
      await cacheService.set(serverId, 'display arp', commandResult);
      expect(mockRedis.setex).toHaveBeenCalledWith(
        expect.any(String),
        120,
        expect.any(String)
      );
    });
  });

  describe('cache invalidation', () => {
    it('should invalidate cache for specific server', async () => {
      const serverId = 'server-1';
      mockRedis.keys.mockResolvedValue(['cmd:server-1:hash1', 'cmd:server-1:hash2']);

      await cacheService.invalidateServer(serverId);

      expect(mockRedis.keys).toHaveBeenCalledWith('cmd:server-1:*');
      expect(mockRedis.del).toHaveBeenCalledWith('cmd:server-1:hash1', 'cmd:server-1:hash2');
    });

    it('should invalidate cache by pattern', async () => {
      const pattern = 'version';
      mockRedis.keys.mockResolvedValue(['cmd:server-1:version123', 'cmd:server-2:version456']);

      await cacheService.invalidateByPattern(pattern);

      expect(mockRedis.keys).toHaveBeenCalledWith('*version*');
      expect(mockRedis.del).toHaveBeenCalledWith('cmd:server-1:version123', 'cmd:server-2:version456');
    });

    it('should clear all cache', async () => {
      await cacheService.clear();
      expect(mockRedis.flushdb).toHaveBeenCalled();
    });
  });

  describe('cache statistics', () => {
    it('should return cache statistics', async () => {
      mockRedis.info
        .mockResolvedValueOnce('used_memory:1048576') // Memory info
        .mockResolvedValueOnce('db0:keys=100,expires=50'); // Keyspace info

      const stats = await cacheService.getStats();

      expect(stats).toMatchObject({
        totalKeys: 100,
        memoryUsage: 1048576,
        hitRate: expect.any(Number),
        totalHits: expect.any(Number),
        totalMisses: expect.any(Number),
        evictions: expect.any(Number)
      });
    });

    it('should return popular commands', async () => {
      const mockCacheEntries = [
        JSON.stringify({ command: 'display version', hits: 10 }),
        JSON.stringify({ command: 'show interface', hits: 5 }),
        JSON.stringify({ command: 'display arp', hits: 15 })
      ];

      mockRedis.keys.mockResolvedValue(['key1', 'key2', 'key3']);
      mockRedis.get
        .mockResolvedValueOnce(mockCacheEntries[0])
        .mockResolvedValueOnce(mockCacheEntries[1])
        .mockResolvedValueOnce(mockCacheEntries[2]);

      const popularCommands = await cacheService.getPopularCommands(2);

      expect(popularCommands).toHaveLength(2);
      expect(popularCommands[0]).toEqual({ command: 'display arp', hits: 15 });
      expect(popularCommands[1]).toEqual({ command: 'display version', hits: 10 });
    });
  });

  describe('cache key generation', () => {
    it('should generate consistent cache keys for same command', async () => {
      const serverId = 'server-1';
      const command1 = 'display version';
      const command2 = 'DISPLAY VERSION'; // Diferente case
      const command3 = 'display  version'; // Espaços extras

      // Simular que todos os comandos são normalizados para a mesma chave
      mockRedis.get.mockResolvedValue(null);

      await cacheService.get(serverId, command1);
      const firstCall = mockRedis.get.mock.calls[0][0];

      mockRedis.get.mockClear();
      await cacheService.get(serverId, command2);
      const secondCall = mockRedis.get.mock.calls[0][0];

      mockRedis.get.mockClear();
      await cacheService.get(serverId, command3);
      const thirdCall = mockRedis.get.mock.calls[0][0];

      // Todos devem gerar a mesma chave (após normalização)
      expect(firstCall).toBe(secondCall);
      expect(secondCall).toBe(thirdCall);
    });
  });

  describe('error handling', () => {
    it('should handle Redis connection errors gracefully', async () => {
      mockRedis.get.mockRejectedValue(new Error('Redis connection failed'));

      const result = await cacheService.get('server-1', 'display version');
      expect(result).toBeNull();
    });

    it('should handle Redis set errors gracefully', async () => {
      mockRedis.setex.mockRejectedValue(new Error('Redis set failed'));

      const commandResult: CommandResult = { stdout: 'Success', code: 0 };
      
      // Não deve lançar erro
      await expect(cacheService.set('server-1', 'display version', commandResult))
        .resolves.not.toThrow();
    });

    it('should return default stats when Redis fails', async () => {
      mockRedis.info.mockRejectedValue(new Error('Redis info failed'));

      const stats = await cacheService.getStats();
      expect(stats).toMatchObject({
        totalKeys: 0,
        memoryUsage: 0,
        hitRate: 0,
        totalHits: expect.any(Number),
        totalMisses: expect.any(Number),
        evictions: expect.any(Number)
      });
    });
  });

  describe('cache enabled/disabled state', () => {
    it('should return enabled state', () => {
      // Por padrão, deve estar habilitado se Redis conectar
      expect(cacheService.isEnabled()).toBe(false); // Falso no teste devido ao mock
    });

    it('should handle disconnect properly', async () => {
      await cacheService.disconnect();
      expect(mockRedis.quit).toHaveBeenCalled();
      expect(cacheService.isEnabled()).toBe(false);
    });
  });
});

# Guia Completo de Deploy para Produção

## 📋 Visão Geral

Este guia detalha o processo completo para fazer deploy do **REMOTEOPS SSH Management System** em ambiente de produção, incluindo configurações de segurança, monitoramento e manutenção.

## 🎯 Pré-requisitos

### Servidor de Produção
- **Sistema Operacional**: Ubuntu 20.04+ ou CentOS 8+
- **RAM**: <PERSON><PERSON><PERSON> 4GB (recomendado 8GB+)
- **CPU**: Mínimo 2 cores (recomendado 4+ cores)
- **Armazenamento**: Mínimo 50GB SSD
- **Rede**: Conexão estável com internet

### Software Necessário
```bash
# Docker e Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Ferramentas auxiliares
sudo apt update
sudo apt install -y git curl wget htop nginx certbot
```

## 🚀 Processo de Deploy

### 1. Preparação do Ambiente

#### Clonar o Repositório
```bash
git clone https://github.com/seu-usuario/sem-fronteiras-ssh.git
cd sem-fronteiras-ssh
```

#### Configurar Variáveis de Ambiente
```bash
# Copiar arquivo de exemplo
cp .env.production.example .env.production

# Editar configurações
nano .env.production
```

**Configurações Críticas:**
```bash
# Senhas seguras (use geradores de senha)
POSTGRES_PASSWORD=sua_senha_super_segura_aqui
JWT_SECRET=sua_chave_jwt_de_pelo_menos_32_caracteres
SESSION_SECRET=sua_chave_de_sessao_super_secreta

# URLs de produção
VITE_API_URL=https://seu-dominio.com/api
CORS_ORIGIN=https://seu-dominio.com

# Email para alertas
ALERT_EMAIL=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=sua-senha-de-app
```

### 2. Configuração de SSL/TLS

#### Usando Let's Encrypt (Recomendado)
```bash
# Instalar Certbot
sudo apt install certbot python3-certbot-nginx

# Obter certificado
sudo certbot --nginx -d seu-dominio.com

# Configurar renovação automática
sudo crontab -e
# Adicionar: 0 12 * * * /usr/bin/certbot renew --quiet
```

#### Configurar SSL no Docker
```bash
# Criar diretório SSL
mkdir -p nginx/ssl

# Copiar certificados
sudo cp /etc/letsencrypt/live/seu-dominio.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/seu-dominio.com/privkey.pem nginx/ssl/key.pem
sudo chown $USER:$USER nginx/ssl/*
```

### 3. Deploy Automatizado

#### Executar Script de Deploy
```bash
# Dar permissão de execução
chmod +x scripts/deploy.sh

# Executar deploy
./scripts/deploy.sh
```

#### Deploy Manual (se necessário)
```bash
# Build das imagens
docker-compose -f docker-compose.prod.yml build

# Iniciar serviços
docker-compose -f docker-compose.prod.yml up -d

# Verificar status
docker-compose -f docker-compose.prod.yml ps
```

### 4. Configuração Pós-Deploy

#### Criar Usuário Administrador
```bash
# Acessar container do backend
docker-compose -f docker-compose.prod.yml exec backend bash

# Executar script de criação de admin
npm run create-admin
```

#### Configurar Backup Automático
```bash
# Verificar se backup está funcionando
./scripts/manage.sh backup "Teste inicial"

# Configurar cron para backup diário
crontab -e
# Adicionar: 0 2 * * * cd /caminho/para/sem-fronteiras && ./scripts/manage.sh backup "Backup diário"
```

## 🔒 Configurações de Segurança

### Firewall
```bash
# UFW (Ubuntu)
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# Ou iptables
sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

### Fail2Ban
```bash
# Instalar Fail2Ban
sudo apt install fail2ban

# Configurar
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### Hardening do Docker
```bash
# Configurar Docker daemon
sudo tee /etc/docker/daemon.json << EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "live-restore": true,
  "userland-proxy": false
}
EOF

sudo systemctl restart docker
```

## 📊 Monitoramento

### Health Checks
```bash
# Verificar saúde dos serviços
./scripts/manage.sh health

# Monitoramento contínuo
./scripts/manage.sh monitor
```

### Logs
```bash
# Ver logs em tempo real
./scripts/manage.sh logs

# Logs específicos
./scripts/manage.sh logs backend 100
```

### Alertas por Email
Configure SMTP no `.env.production`:
```bash
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=sua-senha-de-app
ALERT_EMAIL=<EMAIL>
```

## 🔄 Manutenção

### Backup Regular
```bash
# Backup manual
./scripts/manage.sh backup "Descrição do backup"

# Listar backups
ls -la backups/

# Restaurar backup
./scripts/manage.sh restore backups/manual-20231201_120000
```

### Atualizações
```bash
# Atualizar sistema
./scripts/manage.sh update

# Ou manualmente
git pull
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d
```

### Limpeza de Recursos
```bash
# Limpar recursos não utilizados
./scripts/manage.sh cleanup

# Limpar logs antigos
find logs/ -name "*.log" -mtime +30 -delete
```

## 🚨 Troubleshooting

### Problemas Comuns

#### Serviço não inicia
```bash
# Verificar logs
docker-compose -f docker-compose.prod.yml logs [serviço]

# Verificar recursos
docker stats

# Reiniciar serviço
./scripts/manage.sh restart [serviço]
```

#### Banco de dados corrompido
```bash
# Restaurar último backup
./scripts/manage.sh restore backups/[ultimo-backup]

# Ou reparar banco
docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U postgres -d sem_fronteiras > backup_temp.sql
```

#### Problemas de conectividade SSH
```bash
# Verificar logs do backend
./scripts/manage.sh logs backend

# Verificar logs do Python service
./scripts/manage.sh logs python-service

# Testar conectividade
docker-compose -f docker-compose.prod.yml exec backend curl http://python-service:8000/health
```

### Comandos de Diagnóstico
```bash
# Status completo
./scripts/manage.sh status

# Uso de recursos
docker stats

# Espaço em disco
df -h

# Memória
free -h

# Processos
top
```

## 📈 Otimização de Performance

### Configurações do PostgreSQL
```sql
-- Conectar ao banco
docker-compose -f docker-compose.prod.yml exec postgres psql -U postgres

-- Otimizações básicas
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
SELECT pg_reload_conf();
```

### Configurações do Redis
```bash
# Editar docker-compose.prod.yml
# Ajustar maxmemory conforme disponível
command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
```

### Nginx Tuning
```nginx
# Adicionar ao nginx.conf
worker_processes auto;
worker_connections 2048;
keepalive_timeout 30;
client_max_body_size 50M;
```

## 🔐 Backup e Recuperação

### Estratégia de Backup
1. **Backup automático diário** (2:00 AM)
2. **Backup manual** antes de atualizações
3. **Backup externo** semanal (cloud storage)
4. **Retenção**: 30 dias local, 90 dias externo

### Backup Externo (AWS S3)
```bash
# Instalar AWS CLI
sudo apt install awscli

# Configurar credenciais
aws configure

# Script de backup externo
#!/bin/bash
BACKUP_DIR="backups/$(date +%Y%m%d)"
aws s3 sync $BACKUP_DIR s3://seu-bucket/sem-fronteiras-backups/
```

## 📋 Checklist de Deploy

### Pré-Deploy
- [ ] Servidor configurado com requisitos mínimos
- [ ] Docker e Docker Compose instalados
- [ ] Domínio configurado e DNS apontando
- [ ] Certificado SSL obtido
- [ ] Arquivo `.env.production` configurado
- [ ] Firewall configurado

### Deploy
- [ ] Código clonado e atualizado
- [ ] Build das imagens executado
- [ ] Serviços iniciados
- [ ] Migrações do banco executadas
- [ ] Health checks passando

### Pós-Deploy
- [ ] Usuário administrador criado
- [ ] Backup inicial criado
- [ ] Monitoramento configurado
- [ ] Alertas testados
- [ ] Documentação atualizada
- [ ] Equipe treinada

## 🎯 URLs de Acesso

Após o deploy bem-sucedido:

- **Frontend**: https://seu-dominio.com
- **API Backend**: https://seu-dominio.com/api
- **Health Check**: https://seu-dominio.com/api/health
- **Documentação**: https://seu-dominio.com/docs

## 📞 Suporte

Para suporte técnico:
1. Verificar logs: `./scripts/manage.sh logs`
2. Verificar saúde: `./scripts/manage.sh health`
3. Consultar documentação
4. Contatar equipe de desenvolvimento

---

**Status**: ✅ **Guia Completo e Pronto para Uso**

Este guia fornece todas as informações necessárias para um deploy seguro e confiável em produção.

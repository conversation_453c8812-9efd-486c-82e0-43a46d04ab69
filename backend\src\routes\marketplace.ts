import { FastifyInstance } from 'fastify'
import { PrismaClient, DeviceType } from '@prisma/client'
import { MarketplaceService } from '../services/MarketplaceService'
import { Logger } from '../utils/Logger'

const prisma = new PrismaClient()

/**
 * Rotas do Marketplace de Templates
 */
export async function marketplaceRoutes(fastify: FastifyInstance) {
  
  /**
   * GET /api/marketplace/templates
   * Busca templates no marketplace
   */
  fastify.get('/templates', async (request, reply) => {
    try {
      const {
        category,
        tags,
        deviceTypes,
        search,
        sortBy,
        page = 1,
        limit = 20
      } = request.query as {
        category?: string
        tags?: string
        deviceTypes?: string
        search?: string
        sortBy?: 'popular' | 'recent' | 'rating' | 'downloads'
        page?: number
        limit?: number
      }

      const filters = {
        category,
        tags: tags ? tags.split(',') : undefined,
        deviceTypes: deviceTypes ? deviceTypes.split(',') as DeviceType[] : undefined,
        search,
        sortBy,
        page: Number(page),
        limit: Number(limit)
      }

      const userId = (request as any).user?.id
      const result = await MarketplaceService.searchTemplates(filters, userId)

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao buscar templates no marketplace:', error)
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  })

  /**
   * GET /api/marketplace/templates/:id
   * Obtém detalhes de um template específico
   */
  fastify.get('/templates/:id', async (request, reply) => {
    try {
      const { id } = request.params as { id: string }
      const userId = (request as any).user?.id

      const template = await MarketplaceService.getTemplate(id, userId)

      if (!template) {
        reply.status(404).send({
          success: false,
          error: 'Template não encontrado'
        })
        return
      }

      return {
        success: true,
        data: template,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao obter template do marketplace:', error)
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  })

  /**
   * POST /api/marketplace/templates/:id/publish
   * Publica um template no marketplace
   */
  fastify.post('/templates/:id/publish', async (request, reply) => {
    try {
      const { id } = request.params as { id: string }
      const userId = (request as any).user.id
      const { category, tags, deviceTypes } = request.body as {
        category: string
        tags: string[]
        deviceTypes: DeviceType[]
      }

      await MarketplaceService.publishTemplate(id, userId, {
        category,
        tags,
        deviceTypes
      })

      return {
        success: true,
        message: 'Template publicado no marketplace com sucesso',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao publicar template no marketplace:', error)
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  })

  /**
   * POST /api/marketplace/templates/:id/unpublish
   * Remove template do marketplace
   */
  fastify.post('/templates/:id/unpublish', async (request, reply) => {
    try {
      const { id } = request.params as { id: string }
      const userId = (request as any).user.id

      await MarketplaceService.unpublishTemplate(id, userId)

      return {
        success: true,
        message: 'Template removido do marketplace com sucesso',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao remover template do marketplace:', error)
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  })

  /**
   * POST /api/marketplace/templates/:id/download
   * Faz download de um template
   */
  fastify.post('/templates/:id/download', async (request, reply) => {
    try {
      const { id } = request.params as { id: string }
      const userId = (request as any).user.id

      const newTemplate = await MarketplaceService.downloadTemplate(id, userId)

      return {
        success: true,
        data: newTemplate,
        message: 'Template baixado com sucesso',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao fazer download do template:', error)
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  })

  /**
   * POST /api/marketplace/templates/:id/like
   * Curte/descurte um template
   */
  fastify.post('/templates/:id/like', async (request, reply) => {
    try {
      const { id } = request.params as { id: string }
      const userId = (request as any).user.id

      // Verificar se já curtiu
      const existingLike = await prisma.templateLike.findUnique({
        where: {
          templateId_userId: {
            templateId: id,
            userId
          }
        }
      })

      if (existingLike) {
        // Remover like
        await prisma.templateLike.delete({
          where: { id: existingLike.id }
        })

        // Decrementar contador
        await prisma.commandTemplate.update({
          where: { id },
          data: {
            likes: {
              decrement: 1
            }
          }
        })

        return {
          success: true,
          data: { liked: false },
          message: 'Like removido',
          timestamp: new Date().toISOString()
        }
      } else {
        // Adicionar like
        await prisma.templateLike.create({
          data: {
            templateId: id,
            userId
          }
        })

        // Incrementar contador
        await prisma.commandTemplate.update({
          where: { id },
          data: {
            likes: {
              increment: 1
            }
          }
        })

        return {
          success: true,
          data: { liked: true },
          message: 'Template curtido',
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      Logger.error('Erro ao curtir/descurtir template:', error)
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  })

  /**
   * POST /api/marketplace/templates/:id/favorite
   * Adiciona/remove template dos favoritos
   */
  fastify.post('/templates/:id/favorite', async (request, reply) => {
    try {
      const { id } = request.params as { id: string }
      const userId = (request as any).user.id

      const existingFavorite = await prisma.templateFavorite.findUnique({
        where: {
          templateId_userId: {
            templateId: id,
            userId
          }
        }
      })

      if (existingFavorite) {
        await prisma.templateFavorite.delete({
          where: { id: existingFavorite.id }
        })

        return {
          success: true,
          data: { favorited: false },
          message: 'Template removido dos favoritos',
          timestamp: new Date().toISOString()
        }
      } else {
        await prisma.templateFavorite.create({
          data: {
            templateId: id,
            userId
          }
        })

        return {
          success: true,
          data: { favorited: true },
          message: 'Template adicionado aos favoritos',
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      Logger.error('Erro ao favoritar/desfavoritar template:', error)
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  })

  /**
   * POST /api/marketplace/templates/:id/review
   * Adiciona/atualiza review de um template
   */
  fastify.post('/templates/:id/review', async (request, reply) => {
    try {
      const { id } = request.params as { id: string }
      const userId = (request as any).user.id
      const { rating, comment } = request.body as {
        rating: number
        comment?: string
      }

      if (rating < 1 || rating > 5) {
        reply.status(400).send({
          success: false,
          error: 'Rating deve ser entre 1 e 5'
        })
        return
      }

      const existingReview = await prisma.templateReview.findUnique({
        where: {
          templateId_userId: {
            templateId: id,
            userId
          }
        }
      })

      if (existingReview) {
        // Atualizar review existente
        await prisma.templateReview.update({
          where: { id: existingReview.id },
          data: {
            rating,
            comment
          }
        })

        return {
          success: true,
          message: 'Review atualizado com sucesso',
          timestamp: new Date().toISOString()
        }
      } else {
        // Criar novo review
        await prisma.templateReview.create({
          data: {
            templateId: id,
            userId,
            rating,
            comment
          }
        })

        return {
          success: true,
          message: 'Review adicionado com sucesso',
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      Logger.error('Erro ao adicionar/atualizar review:', error)
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  })

  /**
   * GET /api/marketplace/categories
   * Obtém categorias disponíveis
   */
  fastify.get('/categories', async (request, reply) => {
    try {
      const categories = await prisma.commandTemplate.findMany({
        where: {
          isMarketplace: true,
          category: {
            not: null
          }
        },
        select: {
          category: true
        },
        distinct: ['category']
      })

      const categoryList = categories
        .map(c => c.category)
        .filter(Boolean)
        .sort()

      return {
        success: true,
        data: categoryList,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao obter categorias:', error)
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  })

  /**
   * GET /api/marketplace/featured
   * Obtém templates em destaque
   */
  fastify.get('/featured', async (request, reply) => {
    try {
      const templates = await prisma.commandTemplate.findMany({
        where: {
          isMarketplace: true,
          isFeatured: true
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          _count: {
            select: {
              likes_users: true,
              reviews: true
            }
          }
        },
        orderBy: {
          likes: 'desc'
        },
        take: 6
      })

      return {
        success: true,
        data: templates,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      Logger.error('Erro ao obter templates em destaque:', error)
      reply.status(500).send({
        success: false,
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido'
      })
    }
  })
}

export default marketplaceRoutes

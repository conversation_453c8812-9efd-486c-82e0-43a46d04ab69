import { FastifyInstance } from 'fastify';
import { auditService } from '../services/AuditService';
import { authMiddleware } from '../middlewares/auth';
import { adminMiddleware } from '../middlewares/admin';

export async function auditRoutes(fastify: FastifyInstance) {
  // Middleware de autenticação para todas as rotas
  fastify.addHook('preHandler', authMiddleware);

  // Middleware de admin para todas as rotas de auditoria
  fastify.addHook('preHandler', adminMiddleware);

  // Buscar logs de auditoria
  fastify.get('/logs', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          userId: { type: 'string' },
          action: { type: 'string' },
          resource: { type: 'string' },
          resourceId: { type: 'string' },
          startDate: { type: 'string', format: 'date-time' },
          endDate: { type: 'string', format: 'date-time' },
          limit: { type: 'number', minimum: 1, maximum: 100, default: 50 },
          offset: { type: 'number', minimum: 0, default: 0 }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const {
        userId,
        action,
        resource,
        resourceId,
        startDate,
        endDate,
        limit = 50,
        offset = 0
      } = request.query as any;

      const filter: any = {
        userId,
        action,
        resource,
        resourceId,
        limit,
        offset
      };

      if (startDate) {
        filter.startDate = new Date(startDate);
      }

      if (endDate) {
        filter.endDate = new Date(endDate);
      }

      const result = await auditService.getLogs(filter);

      reply.send({
        success: true,
        data: result
      });
    } catch (error) {
      fastify.log.error('Erro ao buscar logs de auditoria:', error);
      reply.status(500).send({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  });

  // Obter estatísticas de auditoria
  fastify.get('/statistics', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          days: { type: 'number', minimum: 1, maximum: 365, default: 30 }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { days = 30 } = request.query as any;

      const statistics = await auditService.getStatistics(days);

      reply.send({
        success: true,
        data: statistics
      });
    } catch (error) {
      fastify.log.error('Erro ao obter estatísticas de auditoria:', error);
      reply.status(500).send({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  });

  // Limpar logs antigos
  fastify.delete('/cleanup', {
    schema: {
      body: {
        type: 'object',
        properties: {
          daysToKeep: { type: 'number', minimum: 1, maximum: 365, default: 90 }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { daysToKeep = 90 } = request.body as any;

      const deletedCount = await auditService.cleanOldLogs(daysToKeep);

      reply.send({
        success: true,
        data: {
          deletedCount,
          message: `${deletedCount} logs antigos foram removidos`
        }
      });
    } catch (error) {
      fastify.log.error('Erro ao limpar logs antigos:', error);
      reply.status(500).send({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  });

  // Buscar logs por recurso específico
  fastify.get('/resource/:resource/:resourceId', {
    schema: {
      params: {
        type: 'object',
        properties: {
          resource: { type: 'string' },
          resourceId: { type: 'string' }
        },
        required: ['resource', 'resourceId']
      },
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'number', minimum: 1, maximum: 100, default: 50 },
          offset: { type: 'number', minimum: 0, default: 0 }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { resource, resourceId } = request.params as any;
      const { limit = 50, offset = 0 } = request.query as any;

      const result = await auditService.getLogs({
        resource,
        resourceId,
        limit,
        offset
      });

      reply.send({
        success: true,
        data: result
      });
    } catch (error) {
      fastify.log.error('Erro ao buscar logs por recurso:', error);
      reply.status(500).send({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  });

  // Buscar logs por usuário
  fastify.get('/user/:userId', {
    schema: {
      params: {
        type: 'object',
        properties: {
          userId: { type: 'string' }
        },
        required: ['userId']
      },
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'number', minimum: 1, maximum: 100, default: 50 },
          offset: { type: 'number', minimum: 0, default: 0 },
          startDate: { type: 'string', format: 'date-time' },
          endDate: { type: 'string', format: 'date-time' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { userId } = request.params as any;
      const { limit = 50, offset = 0, startDate, endDate } = request.query as any;

      const filter: any = {
        userId,
        limit,
        offset
      };

      if (startDate) {
        filter.startDate = new Date(startDate);
      }

      if (endDate) {
        filter.endDate = new Date(endDate);
      }

      const result = await auditService.getLogs(filter);

      reply.send({
        success: true,
        data: result
      });
    } catch (error) {
      fastify.log.error('Erro ao buscar logs por usuário:', error);
      reply.status(500).send({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  });

  // Obter resumo de atividades recentes
  fastify.get('/recent', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          hours: { type: 'number', minimum: 1, maximum: 168, default: 24 },
          limit: { type: 'number', minimum: 1, maximum: 100, default: 20 }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { hours = 24, limit = 20 } = request.query as any;

      const startDate = new Date();
      startDate.setHours(startDate.getHours() - hours);

      const result = await auditService.getLogs({
        startDate,
        limit,
        offset: 0
      });

      reply.send({
        success: true,
        data: result
      });
    } catch (error) {
      fastify.log.error('Erro ao buscar atividades recentes:', error);
      reply.status(500).send({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  });
}

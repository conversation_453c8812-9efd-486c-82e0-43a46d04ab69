#!/bin/bash

# RemoteOps Universal Launcher
# Automatically detects OS and runs appropriate setup script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect operating system
detect_os() {
    case "$(uname -s)" in
        Linux*)
            OS="Linux"
            if [ -f /etc/os-release ]; then
                . /etc/os-release
                DISTRO=$ID
            else
                DISTRO="unknown"
            fi
            ;;
        Darwin*)
            OS="macOS"
            DISTRO="macos"
            ;;
        CYGWIN*|MINGW*|MSYS*)
            OS="Windows"
            DISTRO="windows"
            ;;
        *)
            OS="Unknown"
            DISTRO="unknown"
            ;;
    esac
    
    log_info "Detected OS: $OS ($DISTRO)"
}

# Check if Docker is available
check_docker() {
    if command -v docker &> /dev/null && docker info &> /dev/null; then
        DOCKER_AVAILABLE=true
        log_success "Docker is available"
    else
        DOCKER_AVAILABLE=false
        log_warning "Docker is not available"
    fi
}

# Show banner
show_banner() {
    echo -e "${CYAN}"
    cat << 'EOF'
    ____                      __       ____            
   / __ \___  ____ ___  ____  / /____  / __ \____  _____
  / /_/ / _ \/ __ `__ \/ __ \/ __/ _ \/ / / / __ \/ ___/
 / _, _/  __/ / / / / / /_/ / /_/  __/ /_/ / /_/ (__  ) 
/_/ |_|\___/_/ /_/ /_/\____/\__/\___/\____/ .___/____/  
                                        /_/            
EOF
    echo -e "${NC}"
    echo -e "${BLUE}Universal Setup and Management Script${NC}"
    echo -e "${BLUE}=====================================${NC}"
    echo ""
}

# Show menu
show_menu() {
    echo -e "${CYAN}Choose installation method:${NC}"
    echo ""
    echo "1) 🐳 Docker (Recommended - Works on all platforms)"
    echo "2) 🖥️  Native (Install directly on your system)"
    echo "3) ⚖️  Production (Load-balanced Docker setup)"
    echo "4) ❓ Help"
    echo "5) 🚪 Exit"
    echo ""
}

# Docker installation
install_docker() {
    log_info "Starting Docker installation..."
    
    if [ ! -f "$SCRIPT_DIR/scripts/run-docker.sh" ]; then
        log_error "Docker script not found: $SCRIPT_DIR/scripts/run-docker.sh"
        exit 1
    fi
    
    chmod +x "$SCRIPT_DIR/scripts/run-docker.sh"
    "$SCRIPT_DIR/scripts/run-docker.sh" "$@"
}

# Native installation
install_native() {
    log_info "Starting native installation for $OS..."
    
    case $OS in
        "Linux")
            if [ ! -f "$SCRIPT_DIR/scripts/run-linux.sh" ]; then
                log_error "Linux script not found: $SCRIPT_DIR/scripts/run-linux.sh"
                exit 1
            fi
            chmod +x "$SCRIPT_DIR/scripts/run-linux.sh"
            "$SCRIPT_DIR/scripts/run-linux.sh" "$@"
            ;;
        "Windows")
            if [ -f "$SCRIPT_DIR/scripts/run-windows.ps1" ]; then
                log_info "Running PowerShell script..."
                powershell.exe -ExecutionPolicy Bypass -File "$SCRIPT_DIR/scripts/run-windows.ps1"
            elif [ -f "$SCRIPT_DIR/scripts/run-windows.bat" ]; then
                log_info "Running batch script..."
                cmd.exe /c "$SCRIPT_DIR/scripts/run-windows.bat"
            else
                log_error "Windows script not found"
                exit 1
            fi
            ;;
        "macOS")
            log_warning "macOS native installation not yet implemented"
            log_info "Please use Docker installation instead"
            install_docker "$@"
            ;;
        *)
            log_error "Unsupported operating system: $OS"
            log_info "Please use Docker installation instead"
            install_docker "$@"
            ;;
    esac
}

# Production installation
install_production() {
    log_info "Starting production installation..."
    
    if [ ! -f "$SCRIPT_DIR/scripts/deploy-loadbalanced.sh" ]; then
        log_error "Production script not found: $SCRIPT_DIR/scripts/deploy-loadbalanced.sh"
        exit 1
    fi
    
    chmod +x "$SCRIPT_DIR/scripts/deploy-loadbalanced.sh"
    "$SCRIPT_DIR/scripts/deploy-loadbalanced.sh" "$@"
}

# Show help
show_help() {
    echo -e "${CYAN}RemoteOps Universal Launcher Help${NC}"
    echo "================================="
    echo ""
    echo "This script automatically detects your operating system and runs"
    echo "the appropriate installation method for RemoteOps."
    echo ""
    echo -e "${YELLOW}Installation Methods:${NC}"
    echo ""
    echo -e "${GREEN}🐳 Docker (Recommended)${NC}"
    echo "   - Works on all platforms (Windows, Linux, macOS)"
    echo "   - Isolated environment with all dependencies"
    echo "   - Easy to start, stop, and manage"
    echo "   - Requires Docker to be installed"
    echo ""
    echo -e "${GREEN}🖥️  Native${NC}"
    echo "   - Installs directly on your system"
    echo "   - Better performance (no containerization overhead)"
    echo "   - Requires manual dependency management"
    echo "   - Platform-specific installation"
    echo ""
    echo -e "${GREEN}⚖️  Production${NC}"
    echo "   - Load-balanced setup with multiple instances"
    echo "   - NGINX load balancer with health checks"
    echo "   - Monitoring with Prometheus and Grafana"
    echo "   - Suitable for production deployments"
    echo ""
    echo -e "${YELLOW}Usage Examples:${NC}"
    echo ""
    echo "  $0                    # Interactive menu"
    echo "  $0 docker             # Direct Docker installation"
    echo "  $0 native             # Direct native installation"
    echo "  $0 production         # Direct production installation"
    echo "  $0 help               # Show this help"
    echo ""
    echo -e "${YELLOW}System Requirements:${NC}"
    echo ""
    echo "Docker method:"
    echo "  - Docker 20.10+ and Docker Compose 2.0+"
    echo "  - 4GB RAM minimum, 8GB recommended"
    echo "  - 10GB free disk space"
    echo ""
    echo "Native method:"
    echo "  - Node.js 18+"
    echo "  - Python 3.11+"
    echo "  - PostgreSQL 15+"
    echo "  - Redis 7+"
    echo "  - 4GB RAM minimum"
    echo ""
    echo -e "${YELLOW}Default Credentials:${NC}"
    echo "  Email: <EMAIL>"
    echo "  Password: admin123"
    echo ""
    echo -e "${YELLOW}Support:${NC}"
    echo "  Documentation: https://docs.remoteops.com"
    echo "  Issues: https://github.com/remoteops/remoteops/issues"
}

# Interactive menu
interactive_menu() {
    while true; do
        show_menu
        read -p "Please choose an option (1-5): " choice
        echo ""
        
        case $choice in
            1)
                if [ "$DOCKER_AVAILABLE" = true ]; then
                    install_docker
                    break
                else
                    log_error "Docker is not available on this system"
                    log_info "Please install Docker first: https://docs.docker.com/get-docker/"
                    echo ""
                    read -p "Press Enter to continue..."
                fi
                ;;
            2)
                install_native
                break
                ;;
            3)
                if [ "$DOCKER_AVAILABLE" = true ]; then
                    install_production
                    break
                else
                    log_error "Docker is required for production installation"
                    log_info "Please install Docker first: https://docs.docker.com/get-docker/"
                    echo ""
                    read -p "Press Enter to continue..."
                fi
                ;;
            4)
                show_help
                echo ""
                read -p "Press Enter to continue..."
                ;;
            5)
                log_info "Goodbye!"
                exit 0
                ;;
            *)
                log_error "Invalid option. Please choose 1-5."
                echo ""
                ;;
        esac
    done
}

# Main execution
main() {
    show_banner
    detect_os
    check_docker
    
    # Handle command line arguments
    case "${1:-}" in
        "docker"|"d")
            shift
            install_docker "$@"
            ;;
        "native"|"n")
            shift
            install_native "$@"
            ;;
        "production"|"prod"|"p")
            shift
            install_production "$@"
            ;;
        "help"|"h"|"-h"|"--help")
            show_help
            ;;
        "")
            # No arguments, show interactive menu
            interactive_menu
            ;;
        *)
            log_error "Unknown option: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

import { PerformanceOptimizer } from '../../../services/performance/PerformanceOptimizer';

describe('PerformanceOptimizer', () => {
  let optimizer: PerformanceOptimizer;

  beforeEach(() => {
    optimizer = new PerformanceOptimizer();
  });

  afterEach(() => {
    optimizer.stopCleanupTimer();
  });

  describe('recordPerformanceMetrics', () => {
    it('should record initial metrics for a server', () => {
      const serverId = 'server-1';
      const connectionTime = 2000;
      const executionTime = 1500;
      const success = true;

      optimizer.recordPerformanceMetrics(serverId, connectionTime, executionTime, success);

      const stats = optimizer.getPerformanceStats();
      expect(stats).toHaveLength(1);
      expect(stats[0]).toMatchObject({
        serverId: 'server-1',
        averageConnectionTime: 2000,
        averageExecutionTime: 1500,
        successRate: 1,
        optimizationStatus: 'optimal'
      });
    });

    it('should calculate moving averages for multiple metrics', () => {
      const serverId = 'server-1';

      // Primeira métrica
      optimizer.recordPerformanceMetrics(serverId, 2000, 1000, true);
      
      // Segunda métrica
      optimizer.recordPerformanceMetrics(serverId, 4000, 2000, true);

      const stats = optimizer.getPerformanceStats();
      expect(stats[0].averageConnectionTime).toBeGreaterThan(2000);
      expect(stats[0].averageConnectionTime).toBeLessThan(4000);
      expect(stats[0].averageExecutionTime).toBeGreaterThan(1000);
      expect(stats[0].averageExecutionTime).toBeLessThan(2000);
      expect(stats[0].successRate).toBe(1);
    });

    it('should handle failed executions correctly', () => {
      const serverId = 'server-1';

      // Sucesso
      optimizer.recordPerformanceMetrics(serverId, 2000, 1000, true);
      
      // Falha
      optimizer.recordPerformanceMetrics(serverId, 3000, 2000, false);

      const stats = optimizer.getPerformanceStats();
      expect(stats[0].successRate).toBeLessThan(1);
      expect(stats[0].successRate).toBeGreaterThan(0);
    });
  });

  describe('getOptimizedSettings', () => {
    it('should return default settings for unknown server', () => {
      const settings = optimizer.getOptimizedSettings('unknown-server');
      
      expect(settings).toMatchObject({
        timeout: 60,
        keepalive: 30,
        maxRetries: 3,
        connectionPoolSize: 2
      });
    });

    it('should return device-specific settings for known device types', () => {
      const huaweiSettings = optimizer.getOptimizedSettings('server-1', 'HUAWEI');
      expect(huaweiSettings).toMatchObject({
        timeout: 90,
        keepalive: 10,
        maxRetries: 3,
        connectionPoolSize: 2
      });

      const mikrotikSettings = optimizer.getOptimizedSettings('server-2', 'MIKROTIK');
      expect(mikrotikSettings).toMatchObject({
        timeout: 60,
        keepalive: 15,
        maxRetries: 3,
        connectionPoolSize: 2
      });

      const nokiaSettings = optimizer.getOptimizedSettings('server-3', 'NOKIA');
      expect(nokiaSettings).toMatchObject({
        timeout: 75,
        keepalive: 20,
        maxRetries: 3,
        connectionPoolSize: 2
      });
    });

    it('should return optimized settings for servers with metrics', () => {
      const serverId = 'server-1';
      
      // Simular servidor lento
      for (let i = 0; i < 5; i++) {
        optimizer.recordPerformanceMetrics(serverId, 5000, 35000, true);
      }

      // Forçar otimização
      optimizer.optimizeAllServers();

      const settings = optimizer.getOptimizedSettings(serverId);
      expect(settings.timeout).toBeGreaterThan(60); // Deve ter aumentado o timeout
    });
  });

  describe('optimization rules', () => {
    it('should optimize settings for slow devices', () => {
      const serverId = 'slow-server';
      
      // Simular dispositivo lento (>30s de execução)
      for (let i = 0; i < 10; i++) {
        optimizer.recordPerformanceMetrics(serverId, 2000, 35000, true);
      }

      optimizer.optimizeAllServers();

      const stats = optimizer.getPerformanceStats();
      const serverStats = stats.find(s => s.serverId === serverId);
      
      expect(serverStats?.recommendedSettings.timeout).toBeGreaterThan(60);
    });

    it('should optimize settings for unstable devices', () => {
      const serverId = 'unstable-server';
      
      // Simular dispositivo instável (baixa taxa de sucesso)
      for (let i = 0; i < 10; i++) {
        const success = i < 6; // 60% de sucesso
        optimizer.recordPerformanceMetrics(serverId, 2000, 10000, success);
      }

      optimizer.optimizeAllServers();

      const stats = optimizer.getPerformanceStats();
      const serverStats = stats.find(s => s.serverId === serverId);
      
      expect(serverStats?.recommendedSettings.maxRetries).toBeGreaterThan(3);
    });

    it('should optimize settings for fast and stable devices', () => {
      const serverId = 'fast-server';
      
      // Simular dispositivo rápido e estável
      for (let i = 0; i < 10; i++) {
        optimizer.recordPerformanceMetrics(serverId, 1000, 5000, true);
      }

      optimizer.optimizeAllServers();

      const stats = optimizer.getPerformanceStats();
      const serverStats = stats.find(s => s.serverId === serverId);
      
      expect(serverStats?.recommendedSettings.timeout).toBeLessThanOrEqual(60);
    });
  });

  describe('optimization status', () => {
    it('should classify servers as problematic', () => {
      const serverId = 'problematic-server';
      
      // Simular servidor problemático
      for (let i = 0; i < 10; i++) {
        optimizer.recordPerformanceMetrics(serverId, 2000, 70000, i < 5); // 50% sucesso, 70s execução
      }

      const stats = optimizer.getPerformanceStats();
      const serverStats = stats.find(s => s.serverId === serverId);
      
      expect(serverStats?.optimizationStatus).toBe('problematic');
    });

    it('should classify servers as needing optimization', () => {
      const serverId = 'needs-optimization-server';
      
      // Simular servidor que precisa de otimização
      for (let i = 0; i < 10; i++) {
        optimizer.recordPerformanceMetrics(serverId, 2000, 35000, i < 8); // 80% sucesso, 35s execução
      }

      const stats = optimizer.getPerformanceStats();
      const serverStats = stats.find(s => s.serverId === serverId);
      
      expect(serverStats?.optimizationStatus).toBe('needs_optimization');
    });

    it('should classify servers as optimal', () => {
      const serverId = 'optimal-server';
      
      // Simular servidor ótimo
      for (let i = 0; i < 10; i++) {
        optimizer.recordPerformanceMetrics(serverId, 1000, 8000, true); // 100% sucesso, 8s execução
      }

      const stats = optimizer.getPerformanceStats();
      const serverStats = stats.find(s => s.serverId === serverId);
      
      expect(serverStats?.optimizationStatus).toBe('optimal');
    });
  });

  describe('getOptimizationRecommendations', () => {
    it('should generate recommendations for problematic servers', () => {
      const serverId = 'problematic-server';
      
      // Simular servidor com problemas
      for (let i = 0; i < 10; i++) {
        optimizer.recordPerformanceMetrics(serverId, 2000, 70000, i < 6); // 60% sucesso, 70s execução
      }

      const recommendations = optimizer.getOptimizationRecommendations();
      
      expect(recommendations.length).toBeGreaterThan(0);
      
      const highPriorityRecs = recommendations.filter(r => r.priority === 'high');
      expect(highPriorityRecs.length).toBeGreaterThan(0);
      
      const serverRecs = recommendations.filter(r => r.serverId === serverId);
      expect(serverRecs.length).toBeGreaterThan(0);
    });

    it('should prioritize recommendations correctly', () => {
      // Servidor com problemas críticos
      for (let i = 0; i < 10; i++) {
        optimizer.recordPerformanceMetrics('critical-server', 2000, 80000, i < 5);
      }

      // Servidor com problemas médios
      for (let i = 0; i < 10; i++) {
        optimizer.recordPerformanceMetrics('medium-server', 15000, 25000, i < 9);
      }

      const recommendations = optimizer.getOptimizationRecommendations();
      
      // Recomendações devem estar ordenadas por prioridade
      const priorities = recommendations.map(r => r.priority);
      const highIndex = priorities.indexOf('high');
      const mediumIndex = priorities.indexOf('medium');
      
      if (highIndex !== -1 && mediumIndex !== -1) {
        expect(highIndex).toBeLessThan(mediumIndex);
      }
    });

    it('should not generate recommendations for optimal servers', () => {
      const serverId = 'optimal-server';
      
      // Simular servidor ótimo
      for (let i = 0; i < 10; i++) {
        optimizer.recordPerformanceMetrics(serverId, 1000, 5000, true);
      }

      const recommendations = optimizer.getOptimizationRecommendations();
      const serverRecs = recommendations.filter(r => r.serverId === serverId);
      
      expect(serverRecs.length).toBe(0);
    });
  });

  describe('performance stats sorting', () => {
    it('should sort stats by optimization status', () => {
      // Criar servidores com diferentes status
      for (let i = 0; i < 10; i++) {
        optimizer.recordPerformanceMetrics('problematic', 2000, 70000, i < 5);
        optimizer.recordPerformanceMetrics('optimal', 1000, 5000, true);
        optimizer.recordPerformanceMetrics('needs-opt', 2000, 35000, i < 8);
      }

      const stats = optimizer.getPerformanceStats();
      
      // Verificar ordenação: problemáticos primeiro, depois needs_optimization, depois optimal
      const statuses = stats.map(s => s.optimizationStatus);
      const problematicIndex = statuses.indexOf('problematic');
      const needsOptIndex = statuses.indexOf('needs_optimization');
      const optimalIndex = statuses.indexOf('optimal');

      if (problematicIndex !== -1 && needsOptIndex !== -1) {
        expect(problematicIndex).toBeLessThan(needsOptIndex);
      }
      if (needsOptIndex !== -1 && optimalIndex !== -1) {
        expect(needsOptIndex).toBeLessThan(optimalIndex);
      }
    });
  });
});

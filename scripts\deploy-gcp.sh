#!/bin/bash

# RemoteOps Google Cloud GKE Deployment Script
# Deploys RemoteOps to Google Kubernetes Engine with GCP-specific optimizations

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
PROJECT_ID="${PROJECT_ID:-remoteops-project}"
CLUSTER_NAME="remoteops-gke"
ZONE="us-central1-a"
REGION="us-central1"
NODE_COUNT=3
MACHINE_TYPE="e2-standard-2"
NAMESPACE="remoteops"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check Google Cloud SDK and prerequisites
check_prerequisites() {
    log_info "Checking GCP prerequisites..."
    
    if ! command -v gcloud &> /dev/null; then
        log_error "Google Cloud SDK is not installed"
        echo "Please install gcloud: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi
    
    if ! command -v helm &> /dev/null; then
        log_error "Helm is not installed"
        exit 1
    fi
    
    # Check gcloud authentication
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 &> /dev/null; then
        log_error "Not authenticated with Google Cloud"
        echo "Please run: gcloud auth login"
        exit 1
    fi
    
    # Set project if not set
    if [ -z "$PROJECT_ID" ]; then
        PROJECT_ID=$(gcloud config get-value project)
        if [ -z "$PROJECT_ID" ]; then
            log_error "No GCP project set"
            echo "Please run: gcloud config set project YOUR_PROJECT_ID"
            exit 1
        fi
    fi
    
    log_success "Prerequisites check passed (Project: $PROJECT_ID)"
}

# Enable required APIs
enable_apis() {
    log_info "Enabling required GCP APIs..."
    
    local apis=(
        "container.googleapis.com"
        "compute.googleapis.com"
        "monitoring.googleapis.com"
        "logging.googleapis.com"
        "cloudresourcemanager.googleapis.com"
        "cloudsql.googleapis.com"
        "redis.googleapis.com"
        "secretmanager.googleapis.com"
    )
    
    for api in "${apis[@]}"; do
        log_info "Enabling $api..."
        gcloud services enable "$api" --project="$PROJECT_ID"
    done
    
    log_success "APIs enabled"
}

# Create GKE cluster
create_cluster() {
    log_info "Creating GKE cluster: $CLUSTER_NAME"
    
    # Check if cluster already exists
    if gcloud container clusters describe "$CLUSTER_NAME" --zone="$ZONE" --project="$PROJECT_ID" &> /dev/null; then
        log_warning "Cluster $CLUSTER_NAME already exists"
        return
    fi
    
    # Create GKE cluster with advanced features
    gcloud container clusters create "$CLUSTER_NAME" \
        --project="$PROJECT_ID" \
        --zone="$ZONE" \
        --machine-type="$MACHINE_TYPE" \
        --num-nodes="$NODE_COUNT" \
        --disk-type="pd-ssd" \
        --disk-size="50GB" \
        --image-type="COS_CONTAINERD" \
        --enable-cloud-logging \
        --enable-cloud-monitoring \
        --enable-autoscaling \
        --min-nodes=2 \
        --max-nodes=10 \
        --enable-autorepair \
        --enable-autoupgrade \
        --enable-network-policy \
        --enable-ip-alias \
        --network="default" \
        --subnetwork="default" \
        --cluster-version="latest" \
        --addons=HorizontalPodAutoscaling,HttpLoadBalancing,NetworkPolicy,GcePersistentDiskCsiDriver \
        --enable-shielded-nodes \
        --enable-workload-identity \
        --workload-pool="$PROJECT_ID.svc.id.goog" \
        --security-group="gke-security-groups@$PROJECT_ID.iam.gserviceaccount.com" \
        --release-channel="regular"
    
    log_success "GKE cluster created successfully"
}

# Get GKE credentials
get_credentials() {
    log_info "Getting GKE credentials..."
    
    gcloud container clusters get-credentials "$CLUSTER_NAME" \
        --zone="$ZONE" \
        --project="$PROJECT_ID"
    
    log_success "Credentials configured"
}

# Install Google Cloud Load Balancer Controller
install_gclb_controller() {
    log_info "Configuring Google Cloud Load Balancer..."
    
    # The GCLB controller is built into GKE, just need to configure ingress class
    cat > /tmp/gce-ingress-class.yaml << EOF
apiVersion: networking.k8s.io/v1
kind: IngressClass
metadata:
  name: gce
  annotations:
    ingressclass.kubernetes.io/is-default-class: "true"
spec:
  controller: gke.io/ingress-gce
EOF

    kubectl apply -f /tmp/gce-ingress-class.yaml
    
    log_success "Google Cloud Load Balancer configured"
}

# Install cert-manager for SSL certificates
install_cert_manager() {
    log_info "Installing cert-manager..."
    
    # Add cert-manager Helm repository
    helm repo add jetstack https://charts.jetstack.io
    helm repo update
    
    # Install cert-manager
    helm upgrade --install cert-manager jetstack/cert-manager \
        --namespace cert-manager \
        --create-namespace \
        --version v1.13.0 \
        --set installCRDs=true
    
    # Wait for cert-manager to be ready
    kubectl wait --for=condition=ready pod -l app=cert-manager -n cert-manager --timeout=300s
    
    # Create ClusterIssuer for Let's Encrypt
    cat > /tmp/letsencrypt-issuer.yaml << EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: gce
EOF

    kubectl apply -f /tmp/letsencrypt-issuer.yaml
    
    log_success "cert-manager installed with Let's Encrypt"
}

# Create Cloud SQL instance (optional)
create_cloud_sql() {
    local instance_name="remoteops-postgres"
    
    log_info "Creating Cloud SQL instance (optional)..."
    
    read -p "Do you want to create a Cloud SQL PostgreSQL instance? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Skipping Cloud SQL creation"
        return
    fi
    
    # Check if instance already exists
    if gcloud sql instances describe "$instance_name" --project="$PROJECT_ID" &> /dev/null; then
        log_warning "Cloud SQL instance $instance_name already exists"
        return
    fi
    
    # Create Cloud SQL instance
    gcloud sql instances create "$instance_name" \
        --project="$PROJECT_ID" \
        --database-version=POSTGRES_15 \
        --tier=db-f1-micro \
        --region="$REGION" \
        --storage-type=SSD \
        --storage-size=20GB \
        --storage-auto-increase \
        --backup-start-time=03:00 \
        --enable-bin-log \
        --maintenance-window-day=SUN \
        --maintenance-window-hour=04 \
        --deletion-protection
    
    # Create database
    gcloud sql databases create remoteops \
        --instance="$instance_name" \
        --project="$PROJECT_ID"
    
    # Create user
    gcloud sql users create remoteops \
        --instance="$instance_name" \
        --password="$(openssl rand -base64 32)" \
        --project="$PROJECT_ID"
    
    log_success "Cloud SQL instance created"
}

# Create Memorystore Redis instance (optional)
create_memorystore() {
    local instance_name="remoteops-redis"
    
    log_info "Creating Memorystore Redis instance (optional)..."
    
    read -p "Do you want to create a Memorystore Redis instance? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Skipping Memorystore creation"
        return
    fi
    
    # Check if instance already exists
    if gcloud redis instances describe "$instance_name" --region="$REGION" --project="$PROJECT_ID" &> /dev/null; then
        log_warning "Memorystore instance $instance_name already exists"
        return
    fi
    
    # Create Memorystore instance
    gcloud redis instances create "$instance_name" \
        --project="$PROJECT_ID" \
        --region="$REGION" \
        --size=1 \
        --tier=basic \
        --redis-version=redis_6_x \
        --network="default"
    
    log_success "Memorystore Redis instance created"
}

# Create GCP-specific values file
create_gcp_values() {
    local values_file="$PROJECT_ROOT/kubernetes/helm/remoteops/values-gcp.yaml"
    
    log_info "Creating GCP-specific values file..."
    
    cat > "$values_file" << EOF
# Google Cloud GKE specific values
global:
  storageClass: "standard-rwo"

app:
  environment: production

# Backend configuration with GCP optimizations
backend:
  replicaCount: 3
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 20
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: app.kubernetes.io/component
              operator: In
              values:
              - backend
          topologyKey: topology.gke.io/zone

# Frontend configuration
frontend:
  replicaCount: 2
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10

# Python service configuration
pythonService:
  replicaCount: 2
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 15

# PostgreSQL with Persistent Disk
postgresql:
  enabled: true
  primary:
    persistence:
      enabled: true
      storageClass: "standard-rwo"
      size: 100Gi
    
    resources:
      limits:
        cpu: 2000m
        memory: 4Gi
      requests:
        cpu: 1000m
        memory: 2Gi

# Redis with Persistent Disk
redis:
  enabled: true
  master:
    persistence:
      enabled: true
      storageClass: "standard-rwo"
      size: 20Gi
    
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi

# Ingress with Google Cloud Load Balancer
ingress:
  enabled: true
  className: "gce"
  annotations:
    kubernetes.io/ingress.class: gce
    kubernetes.io/ingress.global-static-ip-name: remoteops-ip
    networking.gke.io/managed-certificates: remoteops-ssl-cert
    kubernetes.io/ingress.allow-http: "false"
    ingress.gcp.kubernetes.io/force-ssl-redirect: "true"
  
  hosts:
    - host: remoteops.example.com
      paths:
        - path: /
          pathType: Prefix
          service:
            name: frontend
            port: 80
        - path: /api
          pathType: Prefix
          service:
            name: backend
            port: 3000
        - path: /python-api
          pathType: Prefix
          service:
            name: python-service
            port: 8000

# Monitoring with Google Cloud Monitoring
monitoring:
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true
  
  grafana:
    enabled: true
    adminPassword: "admin"

# Security configurations
security:
  networkPolicy:
    enabled: true
  
  podSecurityPolicy:
    enabled: true

# Pod Disruption Budgets
podDisruptionBudget:
  enabled: true
  minAvailable: 1

# Resource quotas
resourceQuota:
  enabled: true
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
EOF

    log_success "GCP values file created: $values_file"
}

# Create managed SSL certificate
create_managed_ssl() {
    log_info "Creating managed SSL certificate..."
    
    cat > /tmp/managed-cert.yaml << EOF
apiVersion: networking.gke.io/v1
kind: ManagedCertificate
metadata:
  name: remoteops-ssl-cert
  namespace: $NAMESPACE
spec:
  domains:
    - remoteops.example.com
EOF

    kubectl apply -f /tmp/managed-cert.yaml
    
    log_success "Managed SSL certificate created"
}

# Reserve static IP
reserve_static_ip() {
    log_info "Reserving static IP address..."
    
    if gcloud compute addresses describe remoteops-ip --global --project="$PROJECT_ID" &> /dev/null; then
        log_warning "Static IP remoteops-ip already exists"
    else
        gcloud compute addresses create remoteops-ip \
            --global \
            --project="$PROJECT_ID"
        
        local ip=$(gcloud compute addresses describe remoteops-ip --global --project="$PROJECT_ID" --format="value(address)")
        log_success "Static IP reserved: $ip"
    fi
}

# Deploy to GKE
deploy_to_gke() {
    log_info "Deploying RemoteOps to GKE..."
    
    # Create namespace first
    kubectl create namespace "$NAMESPACE" --dry-run=client -o yaml | kubectl apply -f -
    
    # Create managed SSL certificate
    create_managed_ssl
    
    # Deploy using Kubernetes script with GCP values
    "$SCRIPT_DIR/deploy-kubernetes.sh" deploy gcp
    
    log_success "RemoteOps deployed to GKE successfully"
}

# Show GCP-specific information
show_gcp_info() {
    log_info "Google Cloud GKE Cluster Information:"
    
    echo -e "\n${BLUE}Cluster Details:${NC}"
    gcloud container clusters describe "$CLUSTER_NAME" --zone="$ZONE" --project="$PROJECT_ID" --format="table(name,status,currentMasterVersion,location,currentNodeCount)"
    
    echo -e "\n${BLUE}Node Pools:${NC}"
    gcloud container node-pools list --cluster="$CLUSTER_NAME" --zone="$ZONE" --project="$PROJECT_ID" --format="table(name,machineType,diskSizeGb,nodeCount,status)"
    
    echo -e "\n${BLUE}Load Balancer:${NC}"
    kubectl get ingress -n "$NAMESPACE" -o wide
    
    echo -e "\n${BLUE}Static IP:${NC}"
    gcloud compute addresses list --global --project="$PROJECT_ID" --filter="name=remoteops-ip"
    
    echo -e "\n${BLUE}Estimated Monthly Cost:${NC}"
    echo "GKE Cluster: ~\$73/month (management fee)"
    echo "3x e2-standard-2 nodes: ~\$150/month"
    echo "Persistent Disks (170GB): ~\$17/month"
    echo "Load Balancer: ~\$18/month"
    echo "Static IP: ~\$1.5/month"
    echo "Total estimated: ~\$260/month"
}

# Cleanup GCP resources
cleanup() {
    log_warning "This will delete the GKE cluster and associated resources!"
    read -p "Are you sure? Type 'DELETE' to confirm: " confirm
    
    if [ "$confirm" = "DELETE" ]; then
        log_info "Deleting GKE cluster..."
        gcloud container clusters delete "$CLUSTER_NAME" \
            --zone="$ZONE" \
            --project="$PROJECT_ID" \
            --quiet
        
        log_info "Deleting static IP..."
        gcloud compute addresses delete remoteops-ip \
            --global \
            --project="$PROJECT_ID" \
            --quiet
        
        log_success "GKE cluster and resources deleted"
    else
        log_info "Cleanup cancelled"
    fi
}

# Show help
show_help() {
    echo "RemoteOps Google Cloud GKE Deployment Script"
    echo "============================================="
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  create-cluster    Create GKE cluster"
    echo "  deploy           Deploy RemoteOps to existing cluster"
    echo "  full-deploy      Create cluster and deploy RemoteOps"
    echo "  status           Show cluster and deployment status"
    echo "  info             Show GCP-specific information"
    echo "  cleanup          Delete GKE cluster"
    echo "  help             Show this help"
    echo ""
    echo "Environment Variables:"
    echo "  PROJECT_ID       GCP project ID (required)"
    echo "  CLUSTER_NAME     GKE cluster name (default: remoteops-gke)"
    echo "  ZONE             GCP zone (default: us-central1-a)"
    echo "  MACHINE_TYPE     Node machine type (default: e2-standard-2)"
}

# Main execution
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    RemoteOps Google Cloud Deployment${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    check_prerequisites
    
    case "${1:-full-deploy}" in
        "create-cluster")
            enable_apis
            create_cluster
            get_credentials
            install_gclb_controller
            install_cert_manager
            reserve_static_ip
            create_cloud_sql
            create_memorystore
            ;;
        "deploy")
            get_credentials
            create_gcp_values
            deploy_to_gke
            ;;
        "full-deploy")
            enable_apis
            create_cluster
            get_credentials
            install_gclb_controller
            install_cert_manager
            reserve_static_ip
            create_cloud_sql
            create_memorystore
            create_gcp_values
            deploy_to_gke
            show_gcp_info
            
            echo ""
            log_success "🎉 RemoteOps deployed successfully to Google Cloud!"
            echo ""
            log_info "Next steps:"
            echo "1. Update DNS records to point to the static IP"
            echo "2. Update the domain in the managed certificate"
            echo "3. Configure Cloud Monitoring alerts"
            echo "4. Set up backup strategies for persistent disks"
            ;;
        "status")
            "$SCRIPT_DIR/deploy-kubernetes.sh" status
            ;;
        "info")
            show_gcp_info
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

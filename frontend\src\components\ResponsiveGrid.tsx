import React from 'react';
import {
  Grid,
  Container,
  Box,
  useTheme,
  useMediaQuery
} from '@mui/material';
import { useResponsive } from '../hooks/useResponsive';

interface ResponsiveGridProps {
  children: React.ReactNode;
  spacing?: number;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  disableGutters?: boolean;
  className?: string;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  spacing,
  maxWidth = 'lg',
  disableGutters = false,
  className
}) => {
  const { isMobile, isTablet } = useResponsive();
  
  const responsiveSpacing = spacing || (isMobile ? 2 : isTablet ? 3 : 4);
  
  return (
    <Container 
      maxWidth={maxWidth} 
      disableGutters={disableGutters}
      className={className}
    >
      <Grid container spacing={responsiveSpacing}>
        {children}
      </Grid>
    </Container>
  );
};

interface ResponsiveGridItemProps {
  children: React.ReactNode;
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  className?: string;
}

export const ResponsiveGridItem: React.FC<ResponsiveGridItemProps> = ({
  children,
  xs = 12,
  sm,
  md,
  lg,
  xl,
  className
}) => {
  const { isMobile, isTablet } = useResponsive();
  
  // Valores padrão responsivos
  const responsiveProps = {
    xs,
    sm: sm || (isMobile ? 12 : 6),
    md: md || (isTablet ? 6 : 4),
    lg: lg || 3,
    xl: xl || 3
  };
  
  return (
    <Grid item {...responsiveProps} className={className}>
      {children}
    </Grid>
  );
};

interface ResponsiveCardGridProps {
  children: React.ReactNode;
  minCardWidth?: number;
  maxCardWidth?: number;
  spacing?: number;
  className?: string;
}

export const ResponsiveCardGrid: React.FC<ResponsiveCardGridProps> = ({
  children,
  minCardWidth = 280,
  maxCardWidth = 400,
  spacing = 3,
  className
}) => {
  const theme = useTheme();
  const { width } = useResponsive();
  
  // Calcular quantas colunas cabem baseado na largura mínima
  const availableWidth = width - (theme.spacing(spacing) * 2); // Subtrair padding
  const columnsCount = Math.floor(availableWidth / (minCardWidth + theme.spacing(spacing)));
  const finalColumns = Math.max(1, Math.min(columnsCount, 4)); // Entre 1 e 4 colunas
  
  return (
    <Box className={className}>
      <Grid container spacing={spacing}>
        {React.Children.map(children, (child, index) => (
          <Grid 
            item 
            xs={12} 
            sm={finalColumns <= 1 ? 12 : 6}
            md={finalColumns <= 2 ? 6 : 4}
            lg={finalColumns <= 3 ? 4 : 3}
            key={index}
          >
            {child}
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

interface ResponsiveStackProps {
  children: React.ReactNode;
  direction?: 'row' | 'column';
  spacing?: number;
  breakpoint?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  children,
  direction = 'row',
  spacing = 2,
  breakpoint = 'md',
  className
}) => {
  const theme = useTheme();
  const shouldStack = useMediaQuery(theme.breakpoints.down(breakpoint));
  
  const finalDirection = shouldStack ? 'column' : direction;
  
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: finalDirection,
        gap: theme.spacing(spacing),
        alignItems: finalDirection === 'row' ? 'center' : 'stretch'
      }}
      className={className}
    >
      {children}
    </Box>
  );
};

interface ResponsiveDialogProps {
  children: React.ReactNode;
  open: boolean;
  onClose: () => void;
  title?: string;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | false;
  fullScreenBreakpoint?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

export const ResponsiveDialog: React.FC<ResponsiveDialogProps> = ({
  children,
  open,
  onClose,
  title,
  maxWidth = 'md',
  fullScreenBreakpoint = 'sm'
}) => {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down(fullScreenBreakpoint));
  
  return (
    <div>
      {/* Implementação será feita com Dialog do Material-UI */}
      {/* Por enquanto, retornando children para não quebrar */}
      {open && children}
    </div>
  );
};

interface ResponsiveTableProps {
  children: React.ReactNode;
  breakpoint?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const ResponsiveTable: React.FC<ResponsiveTableProps> = ({
  children,
  breakpoint = 'md',
  className
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(breakpoint));
  
  if (isMobile) {
    // Em mobile, converter tabela em cards
    return (
      <Box className={className}>
        {/* Implementação de cards para mobile será feita conforme necessário */}
        {children}
      </Box>
    );
  }
  
  return (
    <Box 
      sx={{ 
        overflowX: 'auto',
        '& table': {
          minWidth: 650
        }
      }}
      className={className}
    >
      {children}
    </Box>
  );
};

interface ResponsiveNavigationProps {
  children: React.ReactNode;
  breakpoint?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  mobileVariant?: 'drawer' | 'bottom' | 'tabs';
  className?: string;
}

export const ResponsiveNavigation: React.FC<ResponsiveNavigationProps> = ({
  children,
  breakpoint = 'md',
  mobileVariant = 'drawer',
  className
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(breakpoint));
  
  if (isMobile) {
    switch (mobileVariant) {
      case 'bottom':
        return (
          <Box 
            sx={{ 
              position: 'fixed',
              bottom: 0,
              left: 0,
              right: 0,
              zIndex: theme.zIndex.appBar
            }}
            className={className}
          >
            {children}
          </Box>
        );
      case 'tabs':
        return (
          <Box 
            sx={{ 
              borderBottom: 1,
              borderColor: 'divider'
            }}
            className={className}
          >
            {children}
          </Box>
        );
      default: // drawer
        return (
          <Box className={className}>
            {children}
          </Box>
        );
    }
  }
  
  return (
    <Box className={className}>
      {children}
    </Box>
  );
};

export default ResponsiveGrid;

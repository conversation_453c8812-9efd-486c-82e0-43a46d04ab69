# 🤝 Guia de Contribuição - REMOTEOPS

Obrigado por considerar contribuir para o **REMOTEOPS SSH Management System**! Este guia irá ajudá-lo a entender como contribuir efetivamente para o projeto.

## 📋 Índice

- [Código de Conduta](#código-de-conduta)
- [Como Contribuir](#como-contribuir)
- [Configuração do Ambiente](#configuração-do-ambiente)
- [Padrões de Desenvolvimento](#padrões-de-desenvolvimento)
- [Processo de Pull Request](#processo-de-pull-request)
- [Reportando Bugs](#reportando-bugs)
- [Sugerindo Melhorias](#sugerindo-melhorias)

## 📜 Código de Conduta

Este projeto adere ao [Contributor Covenant](https://www.contributor-covenant.org/). Ao participar, você deve seguir este código de conduta.

### Nossos Compromissos

- **Ser respeitoso** com todos os contribuidores
- **Ser construtivo** em feedbacks e discussões
- **Ser inclusivo** e acolhedor para novos contribuidores
- **Focar no que é melhor** para a comunidade

## 🚀 Como Contribuir

### Tipos de Contribuição

1. **🐛 Correção de Bugs** - Corrigir problemas existentes
2. **✨ Novas Funcionalidades** - Implementar recursos solicitados
3. **📚 Documentação** - Melhorar ou criar documentação
4. **🧪 Testes** - Adicionar ou melhorar testes
5. **🎨 UI/UX** - Melhorias na interface do usuário
6. **⚡ Performance** - Otimizações de performance
7. **🔒 Segurança** - Melhorias de segurança

### Processo Geral

1. **Fork** o repositório
2. **Clone** seu fork localmente
3. **Crie uma branch** para sua contribuição
4. **Faça suas alterações** seguindo os padrões
5. **Teste** suas alterações
6. **Commit** com mensagens descritivas
7. **Push** para seu fork
8. **Abra um Pull Request**

## 🛠️ Configuração do Ambiente

### Pré-requisitos

- **Node.js** 18+
- **Python** 3.11+
- **Docker** e **Docker Compose**
- **Git**

### Setup Inicial

```bash
# 1. Fork e clone o repositório
git clone https://github.com/seu-usuario/sem-fronteiras-ssh.git
cd sem-fronteiras-ssh

# 2. Configurar ambiente
cp .env.example .env
# Editar .env com suas configurações

# 3. Instalar dependências
# Backend
cd backend
npm install

# Frontend
cd ../frontend
npm install

# Python Service
cd ../python-service
pip install -r requirements.txt

# 4. Iniciar serviços de desenvolvimento
cd ..
docker-compose up -d postgres redis

# 5. Executar migrações
cd backend
npx prisma migrate dev
npx prisma db seed

# 6. Iniciar aplicação
npm run dev  # Backend
cd ../frontend && npm run dev  # Frontend
cd ../python-service && python main.py  # Python Service
```

### Verificação do Setup

```bash
# Verificar se tudo está funcionando
curl http://localhost:3001/health  # Backend
curl http://localhost:3000         # Frontend
curl http://localhost:8000/health  # Python Service
```

## 📏 Padrões de Desenvolvimento

### Estrutura de Branches

- **`main`** - Branch principal (produção)
- **`develop`** - Branch de desenvolvimento
- **`feature/nome-da-feature`** - Novas funcionalidades
- **`bugfix/nome-do-bug`** - Correções de bugs
- **`hotfix/nome-do-hotfix`** - Correções urgentes
- **`docs/nome-da-doc`** - Documentação

### Convenções de Commit

Usamos [Conventional Commits](https://www.conventionalcommits.org/):

```
tipo(escopo): descrição

[corpo opcional]

[rodapé opcional]
```

**Tipos:**
- `feat`: Nova funcionalidade
- `fix`: Correção de bug
- `docs`: Documentação
- `style`: Formatação (não afeta funcionalidade)
- `refactor`: Refatoração de código
- `test`: Adição ou correção de testes
- `chore`: Tarefas de manutenção

**Exemplos:**
```
feat(ssh): adicionar suporte para dispositivos Cisco
fix(auth): corrigir validação de token JWT
docs(api): atualizar documentação de endpoints
test(cache): adicionar testes para CacheService
```

### Padrões de Código

#### TypeScript/JavaScript
```typescript
// ✅ Bom
interface ServerConfig {
  host: string;
  port: number;
  timeout?: number;
}

const connectToServer = async (config: ServerConfig): Promise<Connection> => {
  // Implementação
};

// ❌ Evitar
function connect(h, p, t) {
  // Implementação
}
```

#### Python
```python
# ✅ Bom
from typing import Optional
from pydantic import BaseModel

class ServerConfig(BaseModel):
    host: str
    port: int
    timeout: Optional[int] = 30

async def connect_to_server(config: ServerConfig) -> Connection:
    """Conecta ao servidor SSH."""
    # Implementação

# ❌ Evitar
def connect(h, p, t=30):
    # Implementação
```

### Testes

#### Backend (Jest)
```typescript
describe('CacheService', () => {
  let cacheService: CacheService;

  beforeEach(() => {
    cacheService = new CacheService();
  });

  it('should cache command results', async () => {
    const result = await cacheService.get('server-1', 'display version');
    expect(result).toBeNull();
  });
});
```

#### Python (pytest)
```python
import pytest
from fastapi.testclient import TestClient

def test_execute_command():
    """Testa execução de comando SSH."""
    response = client.post("/execute", json={
        "host": "***********",
        "command": "show version"
    })
    assert response.status_code == 200
```

### Documentação

- **Comentários em código** para lógica complexa
- **JSDoc/docstrings** para funções públicas
- **README** atualizado para novas funcionalidades
- **Changelog** para mudanças significativas

## 🔄 Processo de Pull Request

### Antes de Abrir o PR

1. **Sincronize** com a branch principal:
```bash
git checkout main
git pull upstream main
git checkout sua-branch
git rebase main
```

2. **Execute os testes**:
```bash
# Backend
cd backend && npm test

# Frontend
cd frontend && npm test

# Python
cd python-service && pytest
```

3. **Verifique o linting**:
```bash
# Backend/Frontend
npm run lint

# Python
flake8 . && black --check .
```

### Template de PR

```markdown
## 📋 Descrição

Breve descrição das mudanças realizadas.

## 🔗 Issue Relacionada

Fixes #123

## 🧪 Testes

- [ ] Testes unitários adicionados/atualizados
- [ ] Testes de integração verificados
- [ ] Testado manualmente

## 📝 Checklist

- [ ] Código segue os padrões do projeto
- [ ] Documentação atualizada
- [ ] Testes passando
- [ ] Sem conflitos com main
- [ ] Commits seguem convenção

## 📸 Screenshots (se aplicável)

[Adicionar screenshots para mudanças de UI]
```

### Revisão de Código

Todos os PRs passam por revisão:

1. **Revisão automática** - CI/CD checks
2. **Revisão por pares** - Pelo menos 1 aprovação
3. **Testes** - Todos os testes devem passar
4. **Documentação** - Verificar se está atualizada

## 🐛 Reportando Bugs

### Template de Bug Report

```markdown
## 🐛 Descrição do Bug

Descrição clara e concisa do bug.

## 🔄 Passos para Reproduzir

1. Vá para '...'
2. Clique em '...'
3. Execute '...'
4. Veja o erro

## ✅ Comportamento Esperado

O que deveria acontecer.

## 📸 Screenshots

Se aplicável, adicione screenshots.

## 🖥️ Ambiente

- OS: [e.g. Ubuntu 20.04]
- Browser: [e.g. Chrome 91]
- Versão: [e.g. 1.0.0]

## 📝 Informações Adicionais

Qualquer outra informação relevante.
```

## 💡 Sugerindo Melhorias

### Template de Feature Request

```markdown
## 🚀 Descrição da Funcionalidade

Descrição clara da funcionalidade desejada.

## 🎯 Problema que Resolve

Qual problema esta funcionalidade resolve?

## 💭 Solução Proposta

Como você imagina que isso deveria funcionar?

## 🔄 Alternativas Consideradas

Outras soluções que você considerou?

## 📝 Informações Adicionais

Contexto adicional ou screenshots.
```

## 🏷️ Labels e Milestones

### Labels Principais

- **`bug`** - Algo não está funcionando
- **`enhancement`** - Nova funcionalidade ou melhoria
- **`documentation`** - Melhorias na documentação
- **`good first issue`** - Bom para iniciantes
- **`help wanted`** - Ajuda extra é bem-vinda
- **`priority: high`** - Alta prioridade
- **`priority: low`** - Baixa prioridade

### Milestones

- **v1.1.0** - Próxima versão minor
- **v2.0.0** - Próxima versão major
- **Backlog** - Funcionalidades futuras

## 🎯 Áreas que Precisam de Ajuda

1. **🧪 Testes** - Aumentar cobertura de testes
2. **📚 Documentação** - Melhorar documentação existente
3. **🌐 Internacionalização** - Adicionar suporte a idiomas
4. **♿ Acessibilidade** - Melhorar acessibilidade da UI
5. **📱 Mobile** - Otimizar para dispositivos móveis
6. **🔌 Integrações** - Novos tipos de dispositivos

## 📞 Contato

- **Issues**: Para bugs e feature requests
- **Discussions**: Para perguntas e discussões gerais
- **Email**: Para questões sensíveis

## 🙏 Agradecimentos

Agradecemos a todos os contribuidores que ajudam a tornar o REMOTEOPS melhor!

---

**Obrigado por contribuir! 🚀**

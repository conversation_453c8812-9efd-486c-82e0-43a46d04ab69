import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  TextField,
  Box,
  Typography,
  Chip,
  Divider,
  Alert,
  Tabs,
  Tab,
  Menu,
  MenuItem,
  ListItemIcon
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Edit as EditIcon,
  Star as StarIcon,
  Search as SearchIcon,
  Download as DownloadIcon,
  Upload as UploadIcon,
  Clear as ClearIcon,
  TrendingUp as TrendingUpIcon,
  Schedule as ScheduleIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { useFavoriteCommands, usePopularCommands, useRecentCommands } from '../hooks/useFavoriteCommands';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface FavoritesManagerProps {
  open: boolean;
  onClose: () => void;
  onSelectCommand?: (command: string) => void;
  deviceType?: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ pt: 2 }}>{children}</Box>}
    </div>
  );
};

export const FavoritesManager: React.FC<FavoritesManagerProps> = ({
  open,
  onClose,
  onSelectCommand,
  deviceType
}) => {
  const {
    favorites,
    removeFavorite,
    updateFavorite,
    searchFavorites,
    exportFavorites,
    importFavorites,
    clearFavorites,
    incrementUsage
  } = useFavoriteCommands();

  const popularCommands = usePopularCommands();
  const recentCommands = useRecentCommands();

  const [searchQuery, setSearchQuery] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editDescription, setEditDescription] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const filteredFavorites = searchQuery
    ? searchFavorites(searchQuery)
    : deviceType
      ? favorites.filter(fav => !fav.deviceType || fav.deviceType === deviceType)
      : favorites;

  // Atalhos de teclado
  useKeyboardShortcuts([
    {
      key: 'Escape',
      action: onClose,
      description: 'Fechar favoritos'
    },
    {
      key: 'f',
      ctrlKey: true,
      action: () => {
        const searchInput = document.querySelector('input[placeholder="Pesquisar comandos..."]') as HTMLInputElement;
        searchInput?.focus();
      },
      description: 'Focar na busca'
    }
  ], { enabled: open });

  const handleEdit = (id: string, currentDescription: string) => {
    setEditingId(id);
    setEditDescription(currentDescription || '');
  };

  const handleSaveEdit = () => {
    if (editingId) {
      updateFavorite(editingId, { description: editDescription });
      setEditingId(null);
      setEditDescription('');
    }
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditDescription('');
  };

  const handleSelectCommand = (command: string) => {
    incrementUsage(command);
    if (onSelectCommand) {
      onSelectCommand(command);
    }
    onClose();
  };

  const handleExport = () => {
    const data = exportFavorites();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `favoritos-comandos-${format(new Date(), 'yyyy-MM-dd')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (importFavorites(content)) {
          alert('Favoritos importados com sucesso!');
        } else {
          alert('Erro ao importar favoritos. Verifique o formato do arquivo.');
        }
      };
      reader.readAsText(file);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleClearAll = () => {
    if (window.confirm('Tem certeza que deseja remover todos os favoritos?')) {
      clearFavorites();
      handleMenuClose();
    }
  };

  const renderFavoritesList = (commandsList: typeof favorites) => (
    <List>
      {commandsList.map((favorite) => (
        <ListItem key={favorite.id} divider>
          <ListItemText
            primary={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography
                  variant="body1"
                  sx={{
                    fontFamily: 'monospace',
                    cursor: 'pointer',
                    '&:hover': { color: 'primary.main' }
                  }}
                  onClick={() => handleSelectCommand(favorite.command)}
                >
                  {favorite.command}
                </Typography>
                {favorite.deviceType && (
                  <Chip label={favorite.deviceType} size="small" />
                )}
                {favorite.usageCount > 0 && (
                  <Chip
                    label={`${favorite.usageCount}x`}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                )}
              </Box>
            }
            secondary={
              <Box>
                {editingId === favorite.id ? (
                  <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                    <TextField
                      size="small"
                      value={editDescription}
                      onChange={(e) => setEditDescription(e.target.value)}
                      placeholder="Descrição do comando..."
                      fullWidth
                    />
                    <Button size="small" onClick={handleSaveEdit}>
                      Salvar
                    </Button>
                    <Button size="small" onClick={handleCancelEdit}>
                      Cancelar
                    </Button>
                  </Box>
                ) : (
                  <Box>
                    {favorite.description && (
                      <Typography variant="body2" color="text.secondary">
                        {favorite.description}
                      </Typography>
                    )}
                    <Typography variant="caption" color="text.secondary">
                      Criado em {format(favorite.createdAt, 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                    </Typography>
                  </Box>
                )}
              </Box>
            }
          />
          <ListItemSecondaryAction>
            <IconButton
              size="small"
              onClick={() => handleEdit(favorite.id, favorite.description || '')}
            >
              <EditIcon fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              onClick={() => removeFavorite(favorite.id)}
              color="error"
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </ListItemSecondaryAction>
        </ListItem>
      ))}
      {commandsList.length === 0 && (
        <ListItem>
          <ListItemText
            primary="Nenhum comando encontrado"
            secondary="Adicione comandos aos favoritos para vê-los aqui"
          />
        </ListItem>
      )}
    </List>
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <StarIcon color="warning" />
            <Typography variant="h6">Comandos Favoritos</Typography>
          </Box>
          <IconButton onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleExport}>
          <ListItemIcon>
            <DownloadIcon fontSize="small" />
          </ListItemIcon>
          Exportar Favoritos
        </MenuItem>
        <MenuItem component="label">
          <ListItemIcon>
            <UploadIcon fontSize="small" />
          </ListItemIcon>
          Importar Favoritos
          <input
            type="file"
            accept=".json"
            hidden
            onChange={handleImport}
          />
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleClearAll} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <ClearIcon fontSize="small" color="error" />
          </ListItemIcon>
          Limpar Todos
        </MenuItem>
      </Menu>

      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            size="small"
            placeholder="Pesquisar comandos..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
          />
        </Box>

        {deviceType && (
          <Alert severity="info" sx={{ mb: 2 }}>
            Mostrando favoritos para dispositivos {deviceType}
          </Alert>
        )}

        <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
          <Tab
            label={`Todos (${filteredFavorites.length})`}
            icon={<StarIcon />}
            iconPosition="start"
          />
          <Tab
            label={`Populares (${popularCommands.length})`}
            icon={<TrendingUpIcon />}
            iconPosition="start"
          />
          <Tab
            label={`Recentes (${recentCommands.length})`}
            icon={<ScheduleIcon />}
            iconPosition="start"
          />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          {renderFavoritesList(filteredFavorites)}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {renderFavoritesList(popularCommands)}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          {renderFavoritesList(recentCommands)}
        </TabPanel>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Fechar</Button>
      </DialogActions>
    </Dialog>
  );
};

export default FavoritesManager;

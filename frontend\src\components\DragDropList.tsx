import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Paper,
  Typography,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import {
  DragIndicator as DragIcon,
  Delete as DeleteIcon,
  Edit as EditIcon
} from '@mui/icons-material';

export interface DragDropItem {
  id: string;
  content: React.ReactNode;
  data?: any;
}

interface DragDropListProps {
  items: DragDropItem[];
  onReorder: (newOrder: DragDropItem[]) => void;
  onEdit?: (item: DragDropItem) => void;
  onDelete?: (item: DragDropItem) => void;
  disabled?: boolean;
  showActions?: boolean;
  orientation?: 'vertical' | 'horizontal';
  className?: string;
}

interface DragState {
  isDragging: boolean;
  draggedItem: DragDropItem | null;
  draggedIndex: number;
  dropIndex: number;
  dragOffset: { x: number; y: number };
}

export const DragDropList: React.FC<DragDropListProps> = ({
  items,
  onReorder,
  onEdit,
  onDelete,
  disabled = false,
  showActions = true,
  orientation = 'vertical',
  className
}) => {
  const theme = useTheme();
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    draggedItem: null,
    draggedIndex: -1,
    dropIndex: -1,
    dragOffset: { x: 0, y: 0 }
  });
  
  const listRef = useRef<HTMLDivElement>(null);
  const draggedElementRef = useRef<HTMLDivElement>(null);

  const handleDragStart = (e: React.DragEvent, item: DragDropItem, index: number) => {
    if (disabled) return;
    
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', ''); // Para compatibilidade
    
    setDragState({
      isDragging: true,
      draggedItem: item,
      draggedIndex: index,
      dropIndex: index,
      dragOffset: { x: 0, y: 0 }
    });
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    
    if (dragState.isDragging && index !== dragState.dropIndex) {
      setDragState(prev => ({
        ...prev,
        dropIndex: index
      }));
    }
  };

  const handleDragEnd = () => {
    if (!dragState.isDragging || dragState.draggedIndex === dragState.dropIndex) {
      setDragState({
        isDragging: false,
        draggedItem: null,
        draggedIndex: -1,
        dropIndex: -1,
        dragOffset: { x: 0, y: 0 }
      });
      return;
    }

    // Reordenar items
    const newItems = [...items];
    const [draggedItem] = newItems.splice(dragState.draggedIndex, 1);
    newItems.splice(dragState.dropIndex, 0, draggedItem);
    
    onReorder(newItems);
    
    setDragState({
      isDragging: false,
      draggedItem: null,
      draggedIndex: -1,
      dropIndex: -1,
      dragOffset: { x: 0, y: 0 }
    });
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    handleDragEnd();
  };

  const getItemStyle = (index: number) => {
    const isDraggedItem = dragState.isDragging && index === dragState.draggedIndex;
    const isDropTarget = dragState.isDragging && index === dragState.dropIndex;
    const isAboveDropTarget = dragState.isDragging && 
      index === dragState.dropIndex - 1 && 
      dragState.draggedIndex > dragState.dropIndex;
    const isBelowDropTarget = dragState.isDragging && 
      index === dragState.dropIndex + 1 && 
      dragState.draggedIndex < dragState.dropIndex;

    return {
      opacity: isDraggedItem ? 0.5 : 1,
      transform: isDraggedItem ? 'rotate(2deg)' : 'none',
      borderTop: isAboveDropTarget ? `2px solid ${theme.palette.primary.main}` : 'none',
      borderBottom: isBelowDropTarget ? `2px solid ${theme.palette.primary.main}` : 'none',
      backgroundColor: isDropTarget ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
      transition: isDraggedItem ? 'none' : 'all 0.2s ease',
      cursor: disabled ? 'default' : 'grab',
      '&:active': {
        cursor: disabled ? 'default' : 'grabbing'
      }
    };
  };

  return (
    <Box className={className} ref={listRef}>
      <List
        sx={{
          display: orientation === 'horizontal' ? 'flex' : 'block',
          flexDirection: orientation === 'horizontal' ? 'row' : 'column',
          gap: orientation === 'horizontal' ? 2 : 0,
          overflowX: orientation === 'horizontal' ? 'auto' : 'visible',
          padding: 0
        }}
      >
        {items.map((item, index) => (
          <ListItem
            key={item.id}
            ref={dragState.draggedIndex === index ? draggedElementRef : null}
            draggable={!disabled}
            onDragStart={(e) => handleDragStart(e, item, index)}
            onDragOver={(e) => handleDragOver(e, index)}
            onDragEnd={handleDragEnd}
            onDrop={handleDrop}
            sx={{
              ...getItemStyle(index),
              minWidth: orientation === 'horizontal' ? 200 : 'auto',
              flexShrink: orientation === 'horizontal' ? 0 : 1,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 1,
              mb: orientation === 'vertical' ? 1 : 0,
              '&:hover': {
                backgroundColor: disabled ? 'transparent' : alpha(theme.palette.action.hover, 0.04)
              }
            }}
          >
            {!disabled && (
              <ListItemIcon sx={{ minWidth: 'auto', mr: 1 }}>
                <DragIcon 
                  sx={{ 
                    color: 'text.secondary',
                    cursor: 'grab',
                    '&:active': { cursor: 'grabbing' }
                  }} 
                />
              </ListItemIcon>
            )}
            
            <ListItemText
              sx={{ 
                flex: 1,
                '& .MuiListItemText-primary': {
                  userSelect: 'none'
                }
              }}
            >
              {item.content}
            </ListItemText>
            
            {showActions && (
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                {onEdit && (
                  <IconButton
                    size="small"
                    onClick={() => onEdit(item)}
                    sx={{ opacity: 0.7, '&:hover': { opacity: 1 } }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                )}
                {onDelete && (
                  <IconButton
                    size="small"
                    onClick={() => onDelete(item)}
                    color="error"
                    sx={{ opacity: 0.7, '&:hover': { opacity: 1 } }}
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                )}
              </Box>
            )}
          </ListItem>
        ))}
        
        {items.length === 0 && (
          <Paper
            sx={{
              p: 4,
              textAlign: 'center',
              backgroundColor: alpha(theme.palette.action.hover, 0.02),
              border: `2px dashed ${theme.palette.divider}`
            }}
          >
            <Typography variant="body2" color="text.secondary">
              Nenhum item para exibir
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {disabled ? '' : 'Arraste e solte itens aqui para organizá-los'}
            </Typography>
          </Paper>
        )}
      </List>
      
      {dragState.isDragging && (
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: alpha(theme.palette.background.default, 0.8),
            zIndex: theme.zIndex.modal - 1,
            pointerEvents: 'none'
          }}
        />
      )}
    </Box>
  );
};

// Hook para gerenciar estado de drag and drop
export const useDragDrop = <T extends { id: string }>(initialItems: T[]) => {
  const [items, setItems] = useState<T[]>(initialItems);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setItems(initialItems);
    setHasChanges(false);
  }, [initialItems]);

  const reorderItems = (newOrder: T[]) => {
    setItems(newOrder);
    setHasChanges(true);
  };

  const addItem = (item: T, index?: number) => {
    setItems(prev => {
      const newItems = [...prev];
      if (index !== undefined) {
        newItems.splice(index, 0, item);
      } else {
        newItems.push(item);
      }
      return newItems;
    });
    setHasChanges(true);
  };

  const removeItem = (id: string) => {
    setItems(prev => prev.filter(item => item.id !== id));
    setHasChanges(true);
  };

  const updateItem = (id: string, updates: Partial<T>) => {
    setItems(prev => prev.map(item => 
      item.id === id ? { ...item, ...updates } : item
    ));
    setHasChanges(true);
  };

  const resetChanges = () => {
    setItems(initialItems);
    setHasChanges(false);
  };

  const saveChanges = () => {
    setHasChanges(false);
    return items;
  };

  return {
    items,
    hasChanges,
    reorderItems,
    addItem,
    removeItem,
    updateItem,
    resetChanges,
    saveChanges
  };
};

export default DragDropList;

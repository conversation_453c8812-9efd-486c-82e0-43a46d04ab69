# RemoteOps Load Balanced Production Setup
# Multiple instances with NGINX load balancer

version: '3.8'

services:
  # NGINX Load Balancer
  nginx:
    image: nginx:alpine
    container_name: remoteops-nginx
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Health check port
      - "2222:2222"  # SSH proxy port
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/ssl:ro
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - backend-1
      - backend-2
      - frontend-1
      - frontend-2
      - python-1
      - python-2
    restart: unless-stopped
    networks:
      - remoteops-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Instances
  backend-1:
    build: ./backend
    container_name: remoteops-backend-1
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/remoteops
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - INSTANCE_ID=backend-1
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - remoteops-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  backend-2:
    build: ./backend
    container_name: remoteops-backend-2
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/remoteops
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - INSTANCE_ID=backend-2
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - remoteops-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  backend-3:
    build: ./backend
    container_name: remoteops-backend-3
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/remoteops
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - INSTANCE_ID=backend-3
      - BACKUP_INSTANCE=true  # Backup instance
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - remoteops-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Python Microservice Instances
  python-1:
    build: ./python-microservice
    container_name: remoteops-python-1
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=********************************************/remoteops
      - INSTANCE_ID=python-1
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    networks:
      - remoteops-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  python-2:
    build: ./python-microservice
    container_name: remoteops-python-2
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=********************************************/remoteops
      - INSTANCE_ID=python-2
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    networks:
      - remoteops-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Frontend Instances
  frontend-1:
    build: ./frontend
    container_name: remoteops-frontend-1
    environment:
      - NODE_ENV=production
      - INSTANCE_ID=frontend-1
    restart: unless-stopped
    networks:
      - remoteops-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  frontend-2:
    build: ./frontend
    container_name: remoteops-frontend-2
    environment:
      - NODE_ENV=production
      - INSTANCE_ID=frontend-2
    restart: unless-stopped
    networks:
      - remoteops-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  # Database (Single instance with replication)
  postgres:
    image: postgres:15-alpine
    container_name: remoteops-postgres
    environment:
      - POSTGRES_DB=remoteops
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - remoteops-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'

  # Redis Cluster for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: remoteops-redis
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - remoteops-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Monitoring and Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: remoteops-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - remoteops-network

  grafana:
    image: grafana/grafana:latest
    container_name: remoteops-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped
    networks:
      - remoteops-network

  # Log aggregation
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: remoteops-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    restart: unless-stopped
    networks:
      - remoteops-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: remoteops-logstash
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./nginx/logs:/var/log/nginx:ro
    depends_on:
      - elasticsearch
    restart: unless-stopped
    networks:
      - remoteops-network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: remoteops-kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    restart: unless-stopped
    networks:
      - remoteops-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  remoteops-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  IconButton,
  Tooltip,
  Avatar,
  LinearProgress
} from '@mui/material';
import {
  Computer as ServerIcon,
  CheckCircle as OnlineIcon,
  Error as OfflineIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreIcon
} from '@mui/icons-material';
import { DashboardWidget } from '../../hooks/useDashboard';

interface Server {
  id: string;
  name: string;
  ip: string;
  type: string;
  status: 'online' | 'offline' | 'warning';
  lastSeen: Date;
  responseTime?: number;
}

interface ServersWidgetProps {
  widget: DashboardWidget;
}

const ServersWidget: React.FC<ServersWidgetProps> = ({ widget }) => {
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(false);

  // Mock data - em produção viria da API
  const mockServers: Server[] = [
    {
      id: '1',
      name: 'Router Principal',
      ip: '***********',
      type: 'MIKROTIK',
      status: 'online',
      lastSeen: new Date(),
      responseTime: 45
    },
    {
      id: '2',
      name: 'Switch Core',
      ip: '***********',
      type: 'HUAWEI',
      status: 'online',
      lastSeen: new Date(Date.now() - 60000),
      responseTime: 32
    },
    {
      id: '3',
      name: 'OLT Fibra',
      ip: '***********',
      type: 'NOKIA',
      status: 'warning',
      lastSeen: new Date(Date.now() - 300000),
      responseTime: 120
    },
    {
      id: '4',
      name: 'Backup Router',
      ip: '***********',
      type: 'MIKROTIK',
      status: 'offline',
      lastSeen: new Date(Date.now() - 3600000)
    }
  ];

  useEffect(() => {
    loadServers();
    
    const interval = setInterval(() => {
      loadServers();
    }, widget.refreshInterval || 60000);

    return () => clearInterval(interval);
  }, [widget.refreshInterval]);

  const loadServers = async () => {
    setLoading(true);
    try {
      // Simular carregamento
      await new Promise(resolve => setTimeout(resolve, 500));
      
      let filteredServers = mockServers;
      
      // Aplicar filtros baseados na configuração do widget
      if (!widget.config.showOffline) {
        filteredServers = filteredServers.filter(server => server.status !== 'offline');
      }
      
      if (widget.config.maxItems) {
        filteredServers = filteredServers.slice(0, widget.config.maxItems);
      }
      
      setServers(filteredServers);
    } catch (error) {
      console.error('Erro ao carregar servidores:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: Server['status']) => {
    switch (status) {
      case 'online':
        return <OnlineIcon color="success" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'offline':
        return <OfflineIcon color="error" />;
      default:
        return <ServerIcon />;
    }
  };

  const getStatusColor = (status: Server['status']) => {
    switch (status) {
      case 'online':
        return 'success';
      case 'warning':
        return 'warning';
      case 'offline':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: Server['status']) => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'warning':
        return 'Atenção';
      case 'offline':
        return 'Offline';
      default:
        return 'Desconhecido';
    }
  };

  const getDeviceTypeColor = (type: string) => {
    switch (type) {
      case 'MIKROTIK':
        return '#1976d2';
      case 'HUAWEI':
        return '#d32f2f';
      case 'NOKIA':
        return '#388e3c';
      default:
        return '#757575';
    }
  };

  const formatLastSeen = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Agora';
    if (minutes < 60) return `${minutes}m atrás`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h atrás`;
    
    const days = Math.floor(hours / 24);
    return `${days}d atrás`;
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Cabeçalho */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="subtitle2" color="text.secondary">
          {servers.length} servidor(es)
        </Typography>
        <Tooltip title="Atualizar">
          <IconButton size="small" onClick={loadServers} disabled={loading}>
            <RefreshIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Loading */}
      {loading && <LinearProgress sx={{ mb: 2 }} />}

      {/* Lista de servidores */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        {servers.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <ServerIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="body2" color="text.secondary">
              Nenhum servidor encontrado
            </Typography>
          </Box>
        ) : (
          <List dense>
            {servers.map((server) => (
              <ListItem key={server.id} divider>
                <ListItemIcon>
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      bgcolor: getDeviceTypeColor(server.type),
                      fontSize: '0.75rem'
                    }}
                  >
                    {server.type.substring(0, 2)}
                  </Avatar>
                </ListItemIcon>
                
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {server.name}
                      </Typography>
                      {getStatusIcon(server.status)}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        {server.ip} • {formatLastSeen(server.lastSeen)}
                      </Typography>
                      {server.responseTime && (
                        <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                          • {server.responseTime}ms
                        </Typography>
                      )}
                    </Box>
                  }
                />
                
                <ListItemSecondaryAction>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Chip
                      label={getStatusText(server.status)}
                      size="small"
                      color={getStatusColor(server.status) as any}
                      variant="outlined"
                    />
                    <IconButton size="small">
                      <MoreIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        )}
      </Box>

      {/* Estatísticas resumidas */}
      <Box sx={{ mt: 'auto', pt: 2, borderTop: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-around', textAlign: 'center' }}>
          <Box>
            <Typography variant="h6" color="success.main">
              {servers.filter(s => s.status === 'online').length}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Online
            </Typography>
          </Box>
          <Box>
            <Typography variant="h6" color="warning.main">
              {servers.filter(s => s.status === 'warning').length}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Atenção
            </Typography>
          </Box>
          <Box>
            <Typography variant="h6" color="error.main">
              {servers.filter(s => s.status === 'offline').length}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Offline
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default ServersWidget;

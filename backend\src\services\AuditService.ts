import { PrismaClient } from '@prisma/client';
import { Logger } from '../utils/logger';

export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  oldValues?: any;
  newValues?: any;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}

export interface AuditFilter {
  userId?: string;
  action?: string;
  resource?: string;
  resourceId?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

export class AuditService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Registra uma ação de auditoria
   */
  async logAction(
    userId: string,
    action: string,
    resource: string,
    resourceId: string,
    oldValues?: any,
    newValues?: any,
    metadata?: {
      ipAddress?: string;
      userAgent?: string;
    }
  ): Promise<void> {
    try {
      await this.prisma.auditLog.create({
        data: {
          userId,
          action,
          resource,
          resourceId,
          oldValues: oldValues ? JSON.stringify(oldValues) : null,
          newValues: newValues ? JSON.stringify(newValues) : null,
          ipAddress: metadata?.ipAddress,
          userAgent: metadata?.userAgent,
          timestamp: new Date()
        }
      });

      Logger.log(`Auditoria registrada: ${action} em ${resource}:${resourceId} por usuário ${userId}`);
    } catch (error) {
      Logger.error('Erro ao registrar auditoria:', error);
    }
  }

  /**
   * Busca logs de auditoria com filtros
   */
  async getLogs(filter: AuditFilter = {}): Promise<{
    logs: AuditLog[];
    total: number;
  }> {
    try {
      const where: any = {};

      if (filter.userId) {
        where.userId = filter.userId;
      }

      if (filter.action) {
        where.action = {
          contains: filter.action,
          mode: 'insensitive'
        };
      }

      if (filter.resource) {
        where.resource = filter.resource;
      }

      if (filter.resourceId) {
        where.resourceId = filter.resourceId;
      }

      if (filter.startDate || filter.endDate) {
        where.timestamp = {};
        if (filter.startDate) {
          where.timestamp.gte = filter.startDate;
        }
        if (filter.endDate) {
          where.timestamp.lte = filter.endDate;
        }
      }

      const [logs, total] = await Promise.all([
        this.prisma.auditLog.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          },
          orderBy: {
            timestamp: 'desc'
          },
          take: filter.limit || 50,
          skip: filter.offset || 0
        }),
        this.prisma.auditLog.count({ where })
      ]);

      return {
        logs: logs.map(log => ({
          id: log.id,
          userId: log.userId,
          action: log.action,
          resource: log.resource,
          resourceId: log.resourceId,
          oldValues: log.oldValues ? JSON.parse(log.oldValues) : null,
          newValues: log.newValues ? JSON.parse(log.newValues) : null,
          ipAddress: log.ipAddress,
          userAgent: log.userAgent,
          timestamp: log.timestamp,
          user: log.user
        })),
        total
      };
    } catch (error) {
      Logger.error('Erro ao buscar logs de auditoria:', error);
      return { logs: [], total: 0 };
    }
  }

  /**
   * Obtém estatísticas de auditoria
   */
  async getStatistics(days: number = 30): Promise<{
    totalActions: number;
    actionsByType: { action: string; count: number }[];
    actionsByUser: { userId: string; userName: string; count: number }[];
    actionsByResource: { resource: string; count: number }[];
    actionsOverTime: { date: string; count: number }[];
  }> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const [
        totalActions,
        actionsByType,
        actionsByUser,
        actionsByResource,
        actionsOverTime
      ] = await Promise.all([
        // Total de ações
        this.prisma.auditLog.count({
          where: {
            timestamp: {
              gte: startDate
            }
          }
        }),

        // Ações por tipo
        this.prisma.auditLog.groupBy({
          by: ['action'],
          where: {
            timestamp: {
              gte: startDate
            }
          },
          _count: {
            action: true
          },
          orderBy: {
            _count: {
              action: 'desc'
            }
          }
        }),

        // Ações por usuário
        this.prisma.auditLog.groupBy({
          by: ['userId'],
          where: {
            timestamp: {
              gte: startDate
            }
          },
          _count: {
            userId: true
          },
          orderBy: {
            _count: {
              userId: 'desc'
            }
          }
        }),

        // Ações por recurso
        this.prisma.auditLog.groupBy({
          by: ['resource'],
          where: {
            timestamp: {
              gte: startDate
            }
          },
          _count: {
            resource: true
          },
          orderBy: {
            _count: {
              resource: 'desc'
            }
          }
        }),

        // Ações ao longo do tempo (por dia)
        this.prisma.$queryRaw`
          SELECT 
            DATE(timestamp) as date,
            COUNT(*) as count
          FROM "AuditLog"
          WHERE timestamp >= ${startDate}
          GROUP BY DATE(timestamp)
          ORDER BY date ASC
        `
      ]);

      // Buscar nomes dos usuários
      const userIds = actionsByUser.map(item => item.userId);
      const users = await this.prisma.user.findMany({
        where: {
          id: {
            in: userIds
          }
        },
        select: {
          id: true,
          name: true
        }
      });

      const userMap = new Map(users.map(user => [user.id, user.name]));

      return {
        totalActions,
        actionsByType: actionsByType.map(item => ({
          action: item.action,
          count: item._count.action
        })),
        actionsByUser: actionsByUser.map(item => ({
          userId: item.userId,
          userName: userMap.get(item.userId) || 'Usuário não encontrado',
          count: item._count.userId
        })),
        actionsByResource: actionsByResource.map(item => ({
          resource: item.resource,
          count: item._count.resource
        })),
        actionsOverTime: (actionsOverTime as any[]).map(item => ({
          date: item.date.toISOString().split('T')[0],
          count: parseInt(item.count)
        }))
      };
    } catch (error) {
      Logger.error('Erro ao obter estatísticas de auditoria:', error);
      return {
        totalActions: 0,
        actionsByType: [],
        actionsByUser: [],
        actionsByResource: [],
        actionsOverTime: []
      };
    }
  }

  /**
   * Limpa logs antigos de auditoria
   */
  async cleanOldLogs(daysToKeep: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await this.prisma.auditLog.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate
          }
        }
      });

      Logger.log(`Removidos ${result.count} logs de auditoria antigos (mais de ${daysToKeep} dias)`);
      return result.count;
    } catch (error) {
      Logger.error('Erro ao limpar logs antigos:', error);
      return 0;
    }
  }
}

// Instância singleton
export const auditService = new AuditService();

import { FastifyInstance } from 'fastify';
import { build } from '../helpers/app';
import { mockPrisma } from '../setup';

describe('Monitoring Routes', () => {
  let app: FastifyInstance;
  let authToken: string;

  beforeAll(async () => {
    app = await build();
    
    // Mock user para autenticação
    mockPrisma.user.findUnique.mockResolvedValue({
      id: '1',
      email: '<EMAIL>',
      name: 'Admin',
      role: 'ADMIN',
      isActive: true,
      password: 'hashedpassword',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Fazer login para obter token
    const loginResponse = await app.inject({
      method: 'POST',
      url: '/login',
      payload: {
        email: '<EMAIL>',
        password: 'password123'
      }
    });

    const loginData = JSON.parse(loginResponse.body);
    authToken = loginData.token;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /api/monitoring/health', () => {
    it('should return system health status', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/health',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(data.data).toHaveProperty('status');
      expect(data.data).toHaveProperty('services');
      expect(data.data).toHaveProperty('alerts');
      expect(data.data).toHaveProperty('lastCheck');
      
      expect(data.data.services).toHaveProperty('nodejs');
      expect(data.data.services).toHaveProperty('python');
      expect(data.data.services).toHaveProperty('database');
      expect(data.data.services).toHaveProperty('redis');
    });

    it('should require authentication', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/health'
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('GET /api/monitoring/metrics/devices', () => {
    it('should return device metrics', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/metrics/devices',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
      
      // Se houver dados, verificar estrutura
      if (data.data.length > 0) {
        const deviceMetric = data.data[0];
        expect(deviceMetric).toHaveProperty('deviceType');
        expect(deviceMetric).toHaveProperty('totalCommands');
        expect(deviceMetric).toHaveProperty('successfulCommands');
        expect(deviceMetric).toHaveProperty('failedCommands');
        expect(deviceMetric).toHaveProperty('averageExecutionTime');
        expect(deviceMetric).toHaveProperty('successRate');
      }
    });
  });

  describe('GET /api/monitoring/metrics/services', () => {
    it('should return service metrics', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/metrics/services',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
      
      // Se houver dados, verificar estrutura
      if (data.data.length > 0) {
        const serviceMetric = data.data[0];
        expect(serviceMetric).toHaveProperty('service');
        expect(serviceMetric).toHaveProperty('totalCommands');
        expect(serviceMetric).toHaveProperty('successfulCommands');
        expect(serviceMetric).toHaveProperty('failedCommands');
        expect(serviceMetric).toHaveProperty('averageExecutionTime');
        expect(serviceMetric).toHaveProperty('successRate');
        expect(serviceMetric).toHaveProperty('uptime');
      }
    });
  });

  describe('GET /api/monitoring/performance', () => {
    it('should return performance metrics with default time range', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/performance',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(data.data).toHaveProperty('timeRange');
      expect(data.data).toHaveProperty('intervals');
      expect(data.data).toHaveProperty('summary');
      expect(data.data.timeRange).toBe('24h');
    });

    it('should accept custom time range', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/performance?timeRange=1h',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.data.timeRange).toBe('1h');
    });
  });

  describe('GET /api/monitoring/alerts', () => {
    it('should return active alerts', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/alerts',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
    });
  });

  describe('GET /api/monitoring/alerts/history', () => {
    it('should return alert history with default limit', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/alerts/history',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
    });

    it('should accept custom limit', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/alerts/history?limit=50',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
    });
  });

  describe('GET /api/monitoring/cache/stats', () => {
    it('should return cache statistics', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/cache/stats',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(data.data).toHaveProperty('totalKeys');
      expect(data.data).toHaveProperty('memoryUsage');
      expect(data.data).toHaveProperty('hitRate');
      expect(data.data).toHaveProperty('totalHits');
      expect(data.data).toHaveProperty('totalMisses');
    });
  });

  describe('GET /api/monitoring/cache/popular', () => {
    it('should return popular commands', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/cache/popular',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
    });

    it('should accept custom limit', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/cache/popular?limit=5',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
    });
  });

  describe('POST /api/monitoring/cache/clear', () => {
    it('should clear cache successfully', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/monitoring/cache/clear',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(data.message).toContain('Cache limpo com sucesso');
    });
  });

  describe('GET /api/monitoring/performance/stats', () => {
    it('should return performance statistics', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/performance/stats',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
    });
  });

  describe('GET /api/monitoring/performance/recommendations', () => {
    it('should return optimization recommendations', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/performance/recommendations',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
    });
  });

  describe('POST /api/monitoring/performance/optimize', () => {
    it('should trigger optimization for all servers', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/monitoring/performance/optimize',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(data.message).toContain('Otimização forçada executada');
    });
  });

  describe('GET /api/monitoring/dashboard', () => {
    it('should return consolidated dashboard data', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/monitoring/dashboard',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const data = JSON.parse(response.body);
      expect(data.success).toBe(true);
      expect(data.data).toHaveProperty('devices');
      expect(data.data).toHaveProperty('services');
      expect(data.data).toHaveProperty('health');
      expect(data.data).toHaveProperty('performance');
      expect(data.data).toHaveProperty('alerts');
      expect(data.data).toHaveProperty('cache');
      
      expect(Array.isArray(data.data.devices)).toBe(true);
      expect(Array.isArray(data.data.services)).toBe(true);
      expect(Array.isArray(data.data.alerts)).toBe(true);
    });
  });
});

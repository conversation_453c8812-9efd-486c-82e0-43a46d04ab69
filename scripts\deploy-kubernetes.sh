#!/bin/bash

# RemoteOps Kubernetes Deployment Script
# Deploys RemoteOps to Kubernetes using Helm

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
HELM_CHART_PATH="$PROJECT_ROOT/kubernetes/helm/remoteops"
NAMESPACE="remoteops"
RELEASE_NAME="remoteops"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GRE<PERSON>}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        echo "Please install kubectl: https://kubernetes.io/docs/tasks/tools/"
        exit 1
    fi
    
    # Check helm
    if ! command -v helm &> /dev/null; then
        log_error "Helm is not installed"
        echo "Please install Helm: https://helm.sh/docs/intro/install/"
        exit 1
    fi
    
    # Check cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        echo "Please configure kubectl to connect to your cluster"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Create namespace
create_namespace() {
    log_info "Creating namespace: $NAMESPACE"
    
    if kubectl get namespace "$NAMESPACE" &> /dev/null; then
        log_warning "Namespace $NAMESPACE already exists"
    else
        kubectl create namespace "$NAMESPACE"
        log_success "Namespace $NAMESPACE created"
    fi
}

# Install or upgrade Helm dependencies
install_dependencies() {
    log_info "Installing Helm dependencies..."
    
    cd "$HELM_CHART_PATH"
    
    # Add required Helm repositories
    helm repo add bitnami https://charts.bitnami.com/bitnami
    helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo add grafana https://grafana.github.io/helm-charts
    
    # Update repositories
    helm repo update
    
    # Update dependencies
    helm dependency update
    
    log_success "Helm dependencies installed"
}

# Generate values file for environment
generate_values() {
    local environment=${1:-production}
    local values_file="$HELM_CHART_PATH/values-$environment.yaml"
    
    log_info "Generating values file for $environment environment..."
    
    case $environment in
        "development")
            cat > "$values_file" << EOF
# Development environment values
app:
  environment: development

backend:
  replicaCount: 1
  autoscaling:
    enabled: false
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi

frontend:
  replicaCount: 1
  autoscaling:
    enabled: false

pythonService:
  replicaCount: 1
  autoscaling:
    enabled: false

postgresql:
  primary:
    persistence:
      size: 8Gi
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi

redis:
  master:
    persistence:
      size: 4Gi
    resources:
      limits:
        cpu: 250m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 256Mi

ingress:
  hosts:
    - host: remoteops-dev.local
      paths:
        - path: /
          pathType: Prefix
          service:
            name: frontend
            port: 80
        - path: /api
          pathType: Prefix
          service:
            name: backend
            port: 3000
        - path: /python-api
          pathType: Prefix
          service:
            name: python-service
            port: 8000
  tls: []

monitoring:
  prometheus:
    enabled: true
  grafana:
    enabled: true
EOF
            ;;
        "staging")
            cat > "$values_file" << EOF
# Staging environment values
app:
  environment: staging

backend:
  replicaCount: 2
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 5

frontend:
  replicaCount: 2
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 3

pythonService:
  replicaCount: 2
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 4

ingress:
  hosts:
    - host: staging.remoteops.com
      paths:
        - path: /
          pathType: Prefix
          service:
            name: frontend
            port: 80
        - path: /api
          pathType: Prefix
          service:
            name: backend
            port: 3000
        - path: /python-api
          pathType: Prefix
          service:
            name: python-service
            port: 8000
  tls:
    - secretName: remoteops-staging-tls
      hosts:
        - staging.remoteops.com

monitoring:
  prometheus:
    enabled: true
  grafana:
    enabled: true
EOF
            ;;
        "production")
            cat > "$values_file" << EOF
# Production environment values
app:
  environment: production

backend:
  replicaCount: 3
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi

frontend:
  replicaCount: 2
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 5

pythonService:
  replicaCount: 2
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 8

postgresql:
  primary:
    persistence:
      size: 100Gi
    resources:
      limits:
        cpu: 2000m
        memory: 4Gi
      requests:
        cpu: 1000m
        memory: 2Gi

redis:
  master:
    persistence:
      size: 20Gi
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi

ingress:
  hosts:
    - host: remoteops.com
      paths:
        - path: /
          pathType: Prefix
          service:
            name: frontend
            port: 80
        - path: /api
          pathType: Prefix
          service:
            name: backend
            port: 3000
        - path: /python-api
          pathType: Prefix
          service:
            name: python-service
            port: 8000
  tls:
    - secretName: remoteops-tls
      hosts:
        - remoteops.com

monitoring:
  prometheus:
    enabled: true
  grafana:
    enabled: true

security:
  networkPolicy:
    enabled: true
  podSecurityPolicy:
    enabled: true
EOF
            ;;
    esac
    
    log_success "Values file generated: $values_file"
}

# Deploy using Helm
deploy() {
    local environment=${1:-production}
    local values_file="$HELM_CHART_PATH/values-$environment.yaml"
    
    log_info "Deploying RemoteOps to Kubernetes ($environment)..."
    
    cd "$HELM_CHART_PATH"
    
    # Check if release exists
    if helm list -n "$NAMESPACE" | grep -q "$RELEASE_NAME"; then
        log_info "Upgrading existing release..."
        helm upgrade "$RELEASE_NAME" . \
            --namespace "$NAMESPACE" \
            --values "$values_file" \
            --wait \
            --timeout 10m
    else
        log_info "Installing new release..."
        helm install "$RELEASE_NAME" . \
            --namespace "$NAMESPACE" \
            --values "$values_file" \
            --wait \
            --timeout 10m \
            --create-namespace
    fi
    
    log_success "Deployment completed successfully"
}

# Show deployment status
show_status() {
    log_info "Checking deployment status..."
    
    echo -e "\n${CYAN}Helm Release Status:${NC}"
    helm status "$RELEASE_NAME" -n "$NAMESPACE"
    
    echo -e "\n${CYAN}Pod Status:${NC}"
    kubectl get pods -n "$NAMESPACE" -l "app.kubernetes.io/instance=$RELEASE_NAME"
    
    echo -e "\n${CYAN}Service Status:${NC}"
    kubectl get services -n "$NAMESPACE" -l "app.kubernetes.io/instance=$RELEASE_NAME"
    
    echo -e "\n${CYAN}Ingress Status:${NC}"
    kubectl get ingress -n "$NAMESPACE" -l "app.kubernetes.io/instance=$RELEASE_NAME"
    
    echo -e "\n${CYAN}HPA Status:${NC}"
    kubectl get hpa -n "$NAMESPACE" -l "app.kubernetes.io/instance=$RELEASE_NAME"
}

# Get application URLs
get_urls() {
    log_info "Getting application URLs..."
    
    local ingress_ip=$(kubectl get ingress -n "$NAMESPACE" "$RELEASE_NAME" -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    local ingress_hostname=$(kubectl get ingress -n "$NAMESPACE" "$RELEASE_NAME" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "")
    
    if [ -n "$ingress_ip" ]; then
        echo -e "\n${CYAN}Application URLs (IP):${NC}"
        echo -e "Frontend: ${GREEN}http://$ingress_ip${NC}"
        echo -e "Backend API: ${GREEN}http://$ingress_ip/api${NC}"
        echo -e "Python API: ${GREEN}http://$ingress_ip/python-api${NC}"
    elif [ -n "$ingress_hostname" ]; then
        echo -e "\n${CYAN}Application URLs (Hostname):${NC}"
        echo -e "Frontend: ${GREEN}http://$ingress_hostname${NC}"
        echo -e "Backend API: ${GREEN}http://$ingress_hostname/api${NC}"
        echo -e "Python API: ${GREEN}http://$ingress_hostname/python-api${NC}"
    else
        echo -e "\n${YELLOW}Ingress not ready yet. Use port-forward to access:${NC}"
        echo -e "kubectl port-forward -n $NAMESPACE svc/$RELEASE_NAME-frontend 3001:80"
        echo -e "kubectl port-forward -n $NAMESPACE svc/$RELEASE_NAME-backend 3000:3000"
    fi
}

# Uninstall deployment
uninstall() {
    log_warning "Uninstalling RemoteOps from Kubernetes..."
    
    read -p "Are you sure you want to uninstall RemoteOps? This will delete all data. (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        helm uninstall "$RELEASE_NAME" -n "$NAMESPACE"
        kubectl delete namespace "$NAMESPACE" --ignore-not-found
        log_success "RemoteOps uninstalled successfully"
    else
        log_info "Uninstall cancelled"
    fi
}

# Show help
show_help() {
    echo "RemoteOps Kubernetes Deployment Script"
    echo "======================================"
    echo ""
    echo "Usage: $0 [COMMAND] [ENVIRONMENT]"
    echo ""
    echo "Commands:"
    echo "  deploy [env]    Deploy RemoteOps (default: production)"
    echo "  upgrade [env]   Upgrade existing deployment"
    echo "  status          Show deployment status"
    echo "  urls            Get application URLs"
    echo "  logs [service]  Show logs for service"
    echo "  shell [service] Open shell in service pod"
    echo "  uninstall       Uninstall RemoteOps"
    echo "  help            Show this help"
    echo ""
    echo "Environments:"
    echo "  development     Development environment"
    echo "  staging         Staging environment"
    echo "  production      Production environment (default)"
    echo ""
    echo "Examples:"
    echo "  $0 deploy development"
    echo "  $0 upgrade production"
    echo "  $0 status"
    echo "  $0 logs backend"
}

# Show logs
show_logs() {
    local service=${1:-backend}
    local pod_name=$(kubectl get pods -n "$NAMESPACE" -l "app.kubernetes.io/instance=$RELEASE_NAME,app.kubernetes.io/component=$service" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -n "$pod_name" ]; then
        log_info "Showing logs for $service ($pod_name)..."
        kubectl logs -n "$NAMESPACE" "$pod_name" -f
    else
        log_error "No pod found for service: $service"
    fi
}

# Open shell in pod
open_shell() {
    local service=${1:-backend}
    local pod_name=$(kubectl get pods -n "$NAMESPACE" -l "app.kubernetes.io/instance=$RELEASE_NAME,app.kubernetes.io/component=$service" -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [ -n "$pod_name" ]; then
        log_info "Opening shell in $service ($pod_name)..."
        kubectl exec -n "$NAMESPACE" -it "$pod_name" -- /bin/sh
    else
        log_error "No pod found for service: $service"
    fi
}

# Main execution
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    RemoteOps Kubernetes Deployment${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    check_prerequisites
    
    case "${1:-deploy}" in
        "deploy"|"upgrade")
            local environment=${2:-production}
            create_namespace
            install_dependencies
            generate_values "$environment"
            deploy "$environment"
            show_status
            get_urls
            
            echo ""
            log_success "🎉 RemoteOps deployed successfully to Kubernetes!"
            echo ""
            log_info "Next steps:"
            echo "1. Configure DNS to point to the ingress IP/hostname"
            echo "2. Set up SSL certificates (cert-manager recommended)"
            echo "3. Configure monitoring alerts"
            echo "4. Set up backup schedules"
            ;;
        "status")
            show_status
            ;;
        "urls")
            get_urls
            ;;
        "logs")
            show_logs "$2"
            ;;
        "shell")
            open_shell "$2"
            ;;
        "uninstall")
            uninstall
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

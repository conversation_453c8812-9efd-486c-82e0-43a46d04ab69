import { FastifyInstance } from 'fastify'
import { HealthCheckService } from '../services/HealthCheckService'
import { Logger } from '../utils/Logger'

/**
 * Rotas de health check para load balancer e monitoramento
 */
export async function healthRoutes(fastify: FastifyInstance) {
  
  /**
   * GET /api/health
   * Health check simples para load balancer
   */
  fastify.get('/', async (request, reply) => {
    try {
      const health = await HealthCheckService.getSimpleHealth()
      
      if (health.status === 'healthy') {
        return reply.status(200).send(health)
      } else {
        return reply.status(503).send(health)
      }
    } catch (error) {
      Logger.error('Erro no health check simples:', error)
      return reply.status(503).send({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })

  /**
   * GET /api/health/detailed
   * Health check detalhado com métricas
   */
  fastify.get('/detailed', async (request, reply) => {
    try {
      const health = await HealthCheckService.getHealthStatus()
      
      const statusCode = health.status === 'healthy' ? 200 : 
                        health.status === 'degraded' ? 200 : 503
      
      return reply.status(statusCode).send(health)
    } catch (error) {
      Logger.error('Erro no health check detalhado:', error)
      return reply.status(503).send({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })

  /**
   * GET /api/health/ready
   * Readiness probe - verifica se a aplicação está pronta para receber tráfego
   */
  fastify.get('/ready', async (request, reply) => {
    try {
      const health = await HealthCheckService.getHealthStatus()
      
      // Considera pronto se database e redis estão funcionais
      const isReady = health.checks.database.status !== 'unhealthy' && 
                     health.checks.redis.status !== 'unhealthy'
      
      if (isReady) {
        return reply.status(200).send({
          status: 'ready',
          timestamp: new Date().toISOString(),
          instanceId: health.instanceId
        })
      } else {
        return reply.status(503).send({
          status: 'not_ready',
          timestamp: new Date().toISOString(),
          instanceId: health.instanceId,
          issues: {
            database: health.checks.database.status,
            redis: health.checks.redis.status
          }
        })
      }
    } catch (error) {
      Logger.error('Erro no readiness check:', error)
      return reply.status(503).send({
        status: 'not_ready',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })

  /**
   * GET /api/health/live
   * Liveness probe - verifica se a aplicação está viva
   */
  fastify.get('/live', async (request, reply) => {
    try {
      // Verificação básica se o processo está respondendo
      const uptime = process.uptime()
      const memUsage = process.memoryUsage()
      
      return reply.status(200).send({
        status: 'alive',
        timestamp: new Date().toISOString(),
        uptime: uptime,
        memory: {
          heapUsed: memUsage.heapUsed,
          heapTotal: memUsage.heapTotal,
          external: memUsage.external,
          rss: memUsage.rss
        },
        pid: process.pid,
        instanceId: process.env.INSTANCE_ID || 'unknown'
      })
    } catch (error) {
      Logger.error('Erro no liveness check:', error)
      return reply.status(503).send({
        status: 'dead',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })

  /**
   * GET /api/health/metrics
   * Métricas para Prometheus
   */
  fastify.get('/metrics', async (request, reply) => {
    try {
      const health = await HealthCheckService.getHealthStatus()
      
      // Formato Prometheus
      const metrics = `
# HELP remoteops_health_status Health status of the instance (1=healthy, 0.5=degraded, 0=unhealthy)
# TYPE remoteops_health_status gauge
remoteops_health_status{instance="${health.instanceId}"} ${health.status === 'healthy' ? 1 : health.status === 'degraded' ? 0.5 : 0}

# HELP remoteops_uptime_seconds Uptime of the instance in seconds
# TYPE remoteops_uptime_seconds counter
remoteops_uptime_seconds{instance="${health.instanceId}"} ${health.uptime / 1000}

# HELP remoteops_requests_total Total number of requests processed
# TYPE remoteops_requests_total counter
remoteops_requests_total{instance="${health.instanceId}"} ${health.metrics.totalRequests}

# HELP remoteops_active_connections Current number of active connections
# TYPE remoteops_active_connections gauge
remoteops_active_connections{instance="${health.instanceId}"} ${health.metrics.activeConnections}

# HELP remoteops_response_time_avg Average response time in milliseconds
# TYPE remoteops_response_time_avg gauge
remoteops_response_time_avg{instance="${health.instanceId}"} ${health.metrics.averageResponseTime}

# HELP remoteops_error_rate Error rate percentage
# TYPE remoteops_error_rate gauge
remoteops_error_rate{instance="${health.instanceId}"} ${health.metrics.errorRate}

# HELP remoteops_memory_usage_bytes Memory usage in bytes
# TYPE remoteops_memory_usage_bytes gauge
remoteops_memory_usage_bytes{instance="${health.instanceId}",type="used"} ${health.metrics.memoryUsage.used}
remoteops_memory_usage_bytes{instance="${health.instanceId}",type="total"} ${health.metrics.memoryUsage.total}

# HELP remoteops_cpu_usage_seconds CPU usage in seconds
# TYPE remoteops_cpu_usage_seconds counter
remoteops_cpu_usage_seconds{instance="${health.instanceId}"} ${health.metrics.cpuUsage}

# HELP remoteops_database_response_time Database response time in milliseconds
# TYPE remoteops_database_response_time gauge
remoteops_database_response_time{instance="${health.instanceId}"} ${health.checks.database.responseTime}

# HELP remoteops_redis_response_time Redis response time in milliseconds
# TYPE remoteops_redis_response_time gauge
remoteops_redis_response_time{instance="${health.instanceId}"} ${health.checks.redis.responseTime}

# HELP remoteops_python_microservice_response_time Python microservice response time in milliseconds
# TYPE remoteops_python_microservice_response_time gauge
remoteops_python_microservice_response_time{instance="${health.instanceId}"} ${health.checks.python_microservice.responseTime}

# HELP remoteops_database_status Database health status (1=healthy, 0.5=degraded, 0=unhealthy)
# TYPE remoteops_database_status gauge
remoteops_database_status{instance="${health.instanceId}"} ${health.checks.database.status === 'healthy' ? 1 : health.checks.database.status === 'degraded' ? 0.5 : 0}

# HELP remoteops_redis_status Redis health status (1=healthy, 0.5=degraded, 0=unhealthy)
# TYPE remoteops_redis_status gauge
remoteops_redis_status{instance="${health.instanceId}"} ${health.checks.redis.status === 'healthy' ? 1 : health.checks.redis.status === 'degraded' ? 0.5 : 0}

# HELP remoteops_python_microservice_status Python microservice health status (1=healthy, 0.5=degraded, 0=unhealthy)
# TYPE remoteops_python_microservice_status gauge
remoteops_python_microservice_status{instance="${health.instanceId}"} ${health.checks.python_microservice.status === 'healthy' ? 1 : health.checks.python_microservice.status === 'degraded' ? 0.5 : 0}
`.trim()

      return reply
        .header('Content-Type', 'text/plain; version=0.0.4; charset=utf-8')
        .send(metrics)
    } catch (error) {
      Logger.error('Erro ao gerar métricas:', error)
      return reply.status(500).send('Error generating metrics')
    }
  })

  /**
   * GET /api/health/version
   * Informações de versão e build
   */
  fastify.get('/version', async (request, reply) => {
    try {
      return reply.status(200).send({
        name: 'RemoteOps',
        version: process.env.npm_package_version || '1.0.0',
        build: process.env.BUILD_NUMBER || 'development',
        commit: process.env.GIT_COMMIT || 'unknown',
        buildDate: process.env.BUILD_DATE || new Date().toISOString(),
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        instanceId: process.env.INSTANCE_ID || 'unknown',
        environment: process.env.NODE_ENV || 'development'
      })
    } catch (error) {
      Logger.error('Erro ao obter informações de versão:', error)
      return reply.status(500).send({
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })
}

export default healthRoutes

import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Tooltip,
  Badge,
  LinearProgress
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as ExportIcon,
  Clear as ClearIcon,
  Visibility as ViewIcon,
  Refresh as RefreshIcon,
  Delete as DeleteIcon,
  BugReport as DebugIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Report as FatalIcon
} from '@mui/icons-material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { ptBR } from 'date-fns/locale';
import { useSystemLogs, SystemLog } from '../hooks/useSystemLogs';
import { format } from 'date-fns';

const SystemLogs: React.FC = () => {
  const {
    logs,
    filteredLogs,
    stats,
    filter,
    isLoading,
    totalPages,
    currentPage,
    pageSize,
    updateFilter,
    setPage,
    setPageSize,
    exportLogs,
    clearLogs,
    getLogDetails
  } = useSystemLogs();

  const [showFilters, setShowFilters] = useState(false);
  const [selectedLog, setSelectedLog] = useState<SystemLog | null>(null);
  const [showLogDetails, setShowLogDetails] = useState(false);

  const getLevelIcon = (level: SystemLog['level']) => {
    switch (level) {
      case 'debug':
        return <DebugIcon fontSize="small" color="action" />;
      case 'info':
        return <InfoIcon fontSize="small" color="info" />;
      case 'warn':
        return <WarningIcon fontSize="small" color="warning" />;
      case 'error':
        return <ErrorIcon fontSize="small" color="error" />;
      case 'fatal':
        return <FatalIcon fontSize="small" color="error" />;
      default:
        return null;
    }
  };

  const getLevelColor = (level: SystemLog['level']) => {
    switch (level) {
      case 'debug':
        return 'default';
      case 'info':
        return 'info';
      case 'warn':
        return 'warning';
      case 'error':
        return 'error';
      case 'fatal':
        return 'error';
      default:
        return 'default';
    }
  };

  const getCategoryColor = (category: SystemLog['category']) => {
    switch (category) {
      case 'system':
        return 'primary';
      case 'security':
        return 'error';
      case 'network':
        return 'secondary';
      case 'application':
        return 'info';
      case 'user':
        return 'success';
      case 'backup':
        return 'warning';
      default:
        return 'default';
    }
  };

  const handleExport = async (format: 'csv' | 'json') => {
    try {
      await exportLogs(format);
    } catch (error) {
      console.error('Erro ao exportar logs:', error);
    }
  };

  const handleViewDetails = (log: SystemLog) => {
    setSelectedLog(log);
    setShowLogDetails(true);
  };

  const handleClearLogs = () => {
    if (window.confirm('Tem certeza que deseja limpar todos os logs? Esta ação não pode ser desfeita.')) {
      clearLogs();
    }
  };

  const uniqueSources = Array.from(new Set(filteredLogs.map(log => log.source)));

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={ptBR}>
      <Container maxWidth="xl">
        {/* Cabeçalho */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            Logs do Sistema
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Visualize e analise logs detalhados do sistema
          </Typography>
        </Box>

        {/* Estatísticas */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary">
                  {stats.totalLogs.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total de Logs
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="error.main">
                  {stats.logsByLevel.error + stats.logsByLevel.fatal}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Erros
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main">
                  {stats.logsByLevel.warn}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Avisos
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="info.main">
                  {stats.logsByLevel.info}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Informações
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="text.secondary">
                  {stats.errorRate.toFixed(1)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Taxa de Erro
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main">
                  {filteredLogs.length.toLocaleString()}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Filtrados
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Controles */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Buscar nos logs..."
                  value={filter.searchTerm}
                  onChange={(e) => updateFilter({ searchTerm: e.target.value })}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                    endAdornment: filter.searchTerm && (
                      <InputAdornment position="end">
                        <IconButton
                          size="small"
                          onClick={() => updateFilter({ searchTerm: '' })}
                        >
                          <ClearIcon />
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                />
              </Grid>

              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Nível</InputLabel>
                  <Select
                    multiple
                    value={filter.levels}
                    onChange={(e) => updateFilter({ levels: e.target.value as SystemLog['level'][] })}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {['debug', 'info', 'warn', 'error', 'fatal'].map((level) => (
                      <MenuItem key={level} value={level}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getLevelIcon(level as SystemLog['level'])}
                          {level}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={2}>
                <FormControl fullWidth>
                  <InputLabel>Categoria</InputLabel>
                  <Select
                    multiple
                    value={filter.categories}
                    onChange={(e) => updateFilter({ categories: e.target.value as SystemLog['category'][] })}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {['system', 'security', 'network', 'application', 'user', 'backup'].map((category) => (
                      <MenuItem key={category} value={category}>
                        {category}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={4}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    startIcon={<FilterIcon />}
                    onClick={() => setShowFilters(!showFilters)}
                    variant={showFilters ? 'contained' : 'outlined'}
                  >
                    Filtros
                  </Button>
                  
                  <Button
                    startIcon={<ExportIcon />}
                    onClick={() => handleExport('csv')}
                    disabled={isLoading}
                  >
                    CSV
                  </Button>
                  
                  <Button
                    startIcon={<ExportIcon />}
                    onClick={() => handleExport('json')}
                    disabled={isLoading}
                  >
                    JSON
                  </Button>
                  
                  <Button
                    startIcon={<DeleteIcon />}
                    onClick={handleClearLogs}
                    color="error"
                  >
                    Limpar
                  </Button>
                </Box>
              </Grid>
            </Grid>

            {/* Filtros avançados */}
            {showFilters && (
              <Box sx={{ mt: 3, pt: 3, borderTop: 1, borderColor: 'divider' }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={3}>
                    <DateTimePicker
                      label="Data Inicial"
                      value={filter.dateRange.start}
                      onChange={(date) => date && updateFilter({
                        dateRange: { ...filter.dateRange, start: date }
                      })}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <DateTimePicker
                      label="Data Final"
                      value={filter.dateRange.end}
                      onChange={(date) => date && updateFilter({
                        dateRange: { ...filter.dateRange, end: date }
                      })}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <FormControl fullWidth>
                      <InputLabel>Fonte</InputLabel>
                      <Select
                        multiple
                        value={filter.sources}
                        onChange={(e) => updateFilter({ sources: e.target.value as string[] })}
                        renderValue={(selected) => (
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {selected.map((value) => (
                              <Chip key={value} label={value} size="small" />
                            ))}
                          </Box>
                        )}
                      >
                        {uniqueSources.map((source) => (
                          <MenuItem key={source} value={source}>
                            {source}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="ID do Usuário"
                      value={filter.userId || ''}
                      onChange={(e) => updateFilter({ userId: e.target.value || undefined })}
                    />
                  </Grid>
                </Grid>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Loading */}
        {isLoading && <LinearProgress sx={{ mb: 2 }} />}

        {/* Tabela de logs */}
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Timestamp</TableCell>
                  <TableCell>Nível</TableCell>
                  <TableCell>Categoria</TableCell>
                  <TableCell>Fonte</TableCell>
                  <TableCell>Mensagem</TableCell>
                  <TableCell>Usuário</TableCell>
                  <TableCell>Ações</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {logs.map((log) => (
                  <TableRow key={log.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {format(log.timestamp, 'dd/MM/yyyy HH:mm:ss')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getLevelIcon(log.level)}
                        label={log.level.toUpperCase()}
                        size="small"
                        color={getLevelColor(log.level) as any}
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={log.category}
                        size="small"
                        color={getCategoryColor(log.category) as any}
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontFamily="monospace">
                        {log.source}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" sx={{ maxWidth: 300 }}>
                        {log.message.length > 100 
                          ? `${log.message.substring(0, 100)}...`
                          : log.message
                        }
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {log.userId && (
                        <Chip label={log.userId} size="small" variant="outlined" />
                      )}
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Ver detalhes">
                        <IconButton
                          size="small"
                          onClick={() => handleViewDetails(log)}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            component="div"
            count={filteredLogs.length}
            page={currentPage - 1}
            onPageChange={(_, page) => setPage(page + 1)}
            rowsPerPage={pageSize}
            onRowsPerPageChange={(e) => setPageSize(parseInt(e.target.value))}
            rowsPerPageOptions={[25, 50, 100, 200]}
            labelRowsPerPage="Logs por página:"
            labelDisplayedRows={({ from, to, count }) => 
              `${from}-${to} de ${count !== -1 ? count : `mais de ${to}`}`
            }
          />
        </Card>

        {/* Dialog de detalhes do log */}
        <Dialog
          open={showLogDetails}
          onClose={() => setShowLogDetails(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            Detalhes do Log
          </DialogTitle>
          <DialogContent>
            {selectedLog && (
              <Box sx={{ pt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Timestamp
                    </Typography>
                    <Typography variant="body2" fontFamily="monospace">
                      {format(selectedLog.timestamp, 'dd/MM/yyyy HH:mm:ss.SSS')}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      ID
                    </Typography>
                    <Typography variant="body2" fontFamily="monospace">
                      {selectedLog.id}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Nível
                    </Typography>
                    <Chip
                      icon={getLevelIcon(selectedLog.level)}
                      label={selectedLog.level.toUpperCase()}
                      color={getLevelColor(selectedLog.level) as any}
                    />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" gutterBottom>
                      Categoria
                    </Typography>
                    <Chip
                      label={selectedLog.category}
                      color={getCategoryColor(selectedLog.category) as any}
                    />
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" gutterBottom>
                      Mensagem
                    </Typography>
                    <Typography variant="body2">
                      {selectedLog.message}
                    </Typography>
                  </Grid>
                  
                  {selectedLog.details && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" gutterBottom>
                        Detalhes
                      </Typography>
                      <Paper sx={{ p: 2, backgroundColor: 'grey.50' }}>
                        <pre style={{ margin: 0, fontSize: '0.875rem' }}>
                          {JSON.stringify(selectedLog.details, null, 2)}
                        </pre>
                      </Paper>
                    </Grid>
                  )}
                  
                  {selectedLog.userId && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        Usuário
                      </Typography>
                      <Typography variant="body2">
                        {selectedLog.userId}
                      </Typography>
                    </Grid>
                  )}
                  
                  {selectedLog.ipAddress && (
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" gutterBottom>
                        IP
                      </Typography>
                      <Typography variant="body2" fontFamily="monospace">
                        {selectedLog.ipAddress}
                      </Typography>
                    </Grid>
                  )}
                </Grid>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setShowLogDetails(false)}>
              Fechar
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </LocalizationProvider>
  );
};

export default SystemLogs;

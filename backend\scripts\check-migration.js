const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkMigration() {
  try {
    console.log('Verificando se o campo commandId é nullable...');
    
    // Verificar a estrutura da tabela
    const tableInfo = await prisma.$queryRaw`
      SELECT column_name, is_nullable
      FROM information_schema.columns
      WHERE table_name = 'CommandHistory' AND column_name = 'commandId';
    `;
    
    console.log('Informações da coluna commandId:');
    console.log(tableInfo);
    
    if (tableInfo.length > 0) {
      const isNullable = tableInfo[0].is_nullable === 'YES';
      console.log(`O campo commandId ${isNullable ? 'é' : 'NÃO é'} nullable.`);
      
      if (!isNullable) {
        console.log('ATENÇÃO: A migração ainda não foi aplicada!');
      } else {
        console.log('A migração foi aplicada com sucesso!');
      }
    } else {
      console.log('Não foi possível encontrar informações sobre a coluna commandId.');
    }
    
  } catch (error) {
    console.error('Erro ao verificar migração:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkMigration();

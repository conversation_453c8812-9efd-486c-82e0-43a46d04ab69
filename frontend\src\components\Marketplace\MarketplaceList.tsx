import React from 'react'
import { 
  Download, 
  Heart, 
  Star, 
  User, 
  Calendar,
  Tag,
  Eye,
  BookmarkPlus
} from 'lucide-react'
import { MarketplaceTemplate, MarketplaceService } from '../../services/marketplaceService'

interface MarketplaceListProps {
  templates: MarketplaceTemplate[]
  onDownload: (templateId: string) => Promise<void>
  onLike: (templateId: string) => Promise<void>
  onFavorite: (templateId: string) => Promise<void>
  isLoading?: boolean
}

const MarketplaceList: React.FC<MarketplaceListProps> = ({
  templates,
  onDownload,
  onLike,
  onFavorite,
  isLoading = false
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR')
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  const renderStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    
    for (let i = 0; i < 5; i++) {
      stars.push(
        <Star 
          key={i} 
          className={`h-3 w-3 ${
            i < fullStars 
              ? 'fill-yellow-400 text-yellow-400' 
              : 'text-gray-300'
          }`} 
        />
      )
    }
    
    return stars
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="h-5 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 mb-3"></div>
                <div className="flex items-center space-x-4">
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                </div>
              </div>
              <div className="h-10 bg-gray-200 rounded w-24"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {templates.map((template) => (
        <div
          key={template.id}
          className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200"
        >
          <div className="p-6">
            <div className="flex items-start justify-between">
              {/* Left Content */}
              <div className="flex-1 min-w-0">
                {/* Header */}
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {template.name}
                    </h3>
                    {template.isFeatured && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        ⭐ Destaque
                      </span>
                    )}
                  </div>
                  
                  {template.stats.averageRating > 0 && (
                    <div className="flex items-center">
                      <div className="flex items-center mr-2">
                        {renderStars(template.stats.averageRating)}
                      </div>
                      <span className="text-sm text-gray-600">
                        {template.stats.averageRating.toFixed(1)} ({template.stats.totalReviews})
                      </span>
                    </div>
                  )}
                </div>

                {/* Description */}
                {template.description && (
                  <p className="text-gray-600 mb-4 line-clamp-2">
                    {template.description}
                  </p>
                )}

                {/* Category and Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {template.category && (
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${MarketplaceService.getCategoryColor(template.category)}`}>
                      {MarketplaceService.getCategoryIcon(template.category)} {template.category}
                    </span>
                  )}
                  {template.tags.slice(0, 4).map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700"
                    >
                      <Tag className="h-3 w-3 mr-1" />
                      {tag}
                    </span>
                  ))}
                  {template.tags.length > 4 && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-500">
                      +{template.tags.length - 4} mais
                    </span>
                  )}
                </div>

                {/* Stats and Meta */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-6 text-sm text-gray-500">
                    <div className="flex items-center">
                      <Download className="h-4 w-4 mr-1" />
                      {formatNumber(template.downloads)} downloads
                    </div>
                    <div className="flex items-center">
                      <Heart className={`h-4 w-4 mr-1 ${template.userInteraction?.hasLiked ? 'fill-red-500 text-red-500' : ''}`} />
                      {formatNumber(template.likes)} likes
                    </div>
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-1" />
                      {template.user.name}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {formatDate(template.createdAt)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Actions */}
              <div className="flex items-center space-x-3 ml-6">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => onLike(template.id)}
                    className={`p-2 rounded-lg transition-colors ${
                      template.userInteraction?.hasLiked
                        ? 'bg-red-100 text-red-600 hover:bg-red-200'
                        : 'bg-gray-100 text-gray-400 hover:text-red-500 hover:bg-red-50'
                    }`}
                    title="Curtir"
                  >
                    <Heart className={`h-4 w-4 ${template.userInteraction?.hasLiked ? 'fill-current' : ''}`} />
                  </button>
                  
                  <button
                    onClick={() => onFavorite(template.id)}
                    className={`p-2 rounded-lg transition-colors ${
                      template.userInteraction?.hasFavorited
                        ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                        : 'bg-gray-100 text-gray-400 hover:text-blue-500 hover:bg-blue-50'
                    }`}
                    title="Favoritar"
                  >
                    <BookmarkPlus className={`h-4 w-4 ${template.userInteraction?.hasFavorited ? 'fill-current' : ''}`} />
                  </button>
                  
                  <button
                    className="p-2 rounded-lg bg-gray-100 text-gray-400 hover:text-gray-600 hover:bg-gray-200 transition-colors"
                    title="Ver detalhes"
                  >
                    <Eye className="h-4 w-4" />
                  </button>
                </div>
                
                <button
                  onClick={() => onDownload(template.id)}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Baixar
                </button>
              </div>
            </div>

            {/* Device Types */}
            {template.deviceTypes.length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="flex items-center">
                  <span className="text-sm text-gray-500 mr-2">Compatível com:</span>
                  <div className="flex flex-wrap gap-1">
                    {template.deviceTypes.slice(0, 5).map((deviceType) => (
                      <span
                        key={deviceType}
                        className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-50 text-blue-700"
                      >
                        {deviceType}
                      </span>
                    ))}
                    {template.deviceTypes.length > 5 && (
                      <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-50 text-gray-500">
                        +{template.deviceTypes.length - 5} mais
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}

export default MarketplaceList

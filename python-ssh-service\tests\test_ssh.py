import pytest
from unittest.mock import Mock, patch, AsyncMock
from app.models import SSHCommandRequest, DeviceType
from app.services.ssh_service import SSHService

@pytest.fixture
def ssh_service():
    return SSHService()

@pytest.fixture
def sample_request():
    return SSHCommandRequest(
        host="***********",
        username="admin",
        password="password123",
        command="display version",
        device_type=DeviceType.HUAWEI,
        timeout=60
    )

class TestSSHService:
    
    def test_get_connection_key(self, ssh_service, sample_request):
        """Testa a geração de chave de conexão"""
        key = ssh_service._get_connection_key(sample_request)
        expected = "***********:22:admin"
        assert key == expected
    
    def test_get_device_config_huawei(self, ssh_service, sample_request):
        """Testa configuração específica para dispositivos Huawei"""
        config = ssh_service._get_device_config(sample_request)
        
        assert config['host'] == "***********"
        assert config['username'] == "admin"
        assert config['password'] == "password123"
        assert config['device_type'] == 'huawei'
        assert config['global_delay_factor'] == 2
        assert config['timeout'] == 90  # Timeout específico do Huawei
    
    def test_get_device_config_linux(self, ssh_service):
        """Testa configuração para dispositivos Linux"""
        request = SSHCommandRequest(
            host="***********",
            username="root",
            password="password",
            command="ls -la",
            device_type=DeviceType.LINUX,
            timeout=30
        )
        
        config = ssh_service._get_device_config(request)
        
        assert config['device_type'] == 'linux'
        assert config['timeout'] == 30
        assert config['global_delay_factor'] == 1
    
    def test_detect_device_info_huawei(self, ssh_service):
        """Testa detecção de informações do dispositivo Huawei"""
        output = "VRP (R) software, Version 8.180\nHuawei Versatile Routing Platform Software"
        info = ssh_service._detect_device_info(output, DeviceType.HUAWEI)
        
        assert info['device_type'] == 'huawei'
        assert info['detected_os'] == 'VRP'
    
    def test_detect_device_info_mikrotik(self, ssh_service):
        """Testa detecção de informações do dispositivo Mikrotik"""
        output = "RouterOS 6.48.6 (stable) on RB4011iGS+"
        info = ssh_service._detect_device_info(output, DeviceType.MIKROTIK)
        
        assert info['device_type'] == 'mikrotik'
        assert info['detected_os'] == 'RouterOS'
    
    def test_get_stats_initial(self, ssh_service):
        """Testa estatísticas iniciais do serviço"""
        stats = ssh_service.get_stats()
        
        assert stats['total_commands'] == 0
        assert stats['successful_commands'] == 0
        assert stats['failed_commands'] == 0
        assert stats['success_rate'] == 0
        assert stats['average_execution_time'] == 0
        assert stats['active_connections'] == 0
    
    @patch('app.services.ssh_service.ConnectHandler')
    async def test_connect_and_execute_success(self, mock_connect_handler, ssh_service, sample_request):
        """Testa execução bem-sucedida de comando"""
        # Mock do ConnectHandler
        mock_conn = Mock()
        mock_conn.send_command.return_value = "VRP (R) software, Version 8.180"
        mock_connect_handler.return_value.__enter__.return_value = mock_conn
        
        result = await ssh_service.connect_and_execute(sample_request)
        
        assert result.code == 0
        assert "VRP" in result.stdout
        assert result.execution_time > 0
        assert result.device_info['detected_os'] == 'VRP'
    
    @patch('app.services.ssh_service.ConnectHandler')
    async def test_connect_and_execute_timeout(self, mock_connect_handler, ssh_service, sample_request):
        """Testa tratamento de timeout"""
        from netmiko.ssh_exception import NetMikoTimeoutException
        
        mock_connect_handler.side_effect = NetMikoTimeoutException("Connection timeout")
        
        result = await ssh_service.connect_and_execute(sample_request)
        
        assert result.code == 1
        assert "Timeout" in result.stderr
        assert result.execution_time > 0
    
    @patch('app.services.ssh_service.ConnectHandler')
    async def test_connect_and_execute_auth_failure(self, mock_connect_handler, ssh_service, sample_request):
        """Testa tratamento de falha de autenticação"""
        from netmiko.ssh_exception import NetMikoAuthenticationException
        
        mock_connect_handler.side_effect = NetMikoAuthenticationException("Authentication failed")
        
        result = await ssh_service.connect_and_execute(sample_request)
        
        assert result.code == 1
        assert "autenticação" in result.stderr.lower()
    
    def test_request_validation(self):
        """Testa validação de requisições"""
        # Teste de porta inválida
        with pytest.raises(ValueError):
            SSHCommandRequest(
                host="***********",
                port=70000,  # Porta inválida
                username="admin",
                password="password",
                command="test"
            )
        
        # Teste de timeout muito baixo (deve ser ajustado para 5)
        request = SSHCommandRequest(
            host="***********",
            username="admin",
            password="password",
            command="test",
            timeout=1  # Muito baixo
        )
        assert request.timeout == 5
        
        # Teste de timeout muito alto (deve ser ajustado para 300)
        request = SSHCommandRequest(
            host="***********",
            username="admin",
            password="password",
            command="test",
            timeout=500  # Muito alto
        )
        assert request.timeout == 300

if __name__ == "__main__":
    pytest.main([__file__])

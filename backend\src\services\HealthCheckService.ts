import { PrismaClient } from '@prisma/client'
import { createClient } from 'redis'
import { Logger } from '../utils/Logger'
import os from 'os'

const prisma = new PrismaClient()

export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded'
  timestamp: string
  instanceId: string
  version: string
  uptime: number
  checks: {
    database: HealthCheck
    redis: HealthCheck
    memory: HealthCheck
    disk: HealthCheck
    python_microservice: HealthCheck
  }
  metrics: {
    totalRequests: number
    activeConnections: number
    averageResponseTime: number
    errorRate: number
    memoryUsage: {
      used: number
      total: number
      percentage: number
    }
    cpuUsage: number
  }
}

export interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded'
  responseTime: number
  message?: string
  lastChecked: string
}

/**
 * Serviço para health checks e monitoramento de saúde da aplicação
 */
export class HealthCheckService {
  private static instanceId = process.env.INSTANCE_ID || `backend-${Math.random().toString(36).substr(2, 9)}`
  private static startTime = Date.now()
  private static metrics = {
    totalRequests: 0,
    activeConnections: 0,
    totalResponseTime: 0,
    errorCount: 0
  }

  /**
   * Executa health check completo
   */
  static async getHealthStatus(): Promise<HealthStatus> {
    const startTime = Date.now()
    
    try {
      const [
        databaseCheck,
        redisCheck,
        memoryCheck,
        diskCheck,
        pythonCheck
      ] = await Promise.allSettled([
        this.checkDatabase(),
        this.checkRedis(),
        this.checkMemory(),
        this.checkDisk(),
        this.checkPythonMicroservice()
      ])

      const checks = {
        database: this.getCheckResult(databaseCheck),
        redis: this.getCheckResult(redisCheck),
        memory: this.getCheckResult(memoryCheck),
        disk: this.getCheckResult(diskCheck),
        python_microservice: this.getCheckResult(pythonCheck)
      }

      // Determinar status geral
      const overallStatus = this.determineOverallStatus(checks)
      
      // Coletar métricas
      const metrics = await this.collectMetrics()

      return {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        instanceId: this.instanceId,
        version: process.env.npm_package_version || '1.0.0',
        uptime: Date.now() - this.startTime,
        checks,
        metrics
      }
    } catch (error) {
      Logger.error('Erro ao executar health check:', error)
      
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        instanceId: this.instanceId,
        version: process.env.npm_package_version || '1.0.0',
        uptime: Date.now() - this.startTime,
        checks: {
          database: { status: 'unhealthy', responseTime: 0, message: 'Health check failed', lastChecked: new Date().toISOString() },
          redis: { status: 'unhealthy', responseTime: 0, message: 'Health check failed', lastChecked: new Date().toISOString() },
          memory: { status: 'unhealthy', responseTime: 0, message: 'Health check failed', lastChecked: new Date().toISOString() },
          disk: { status: 'unhealthy', responseTime: 0, message: 'Health check failed', lastChecked: new Date().toISOString() },
          python_microservice: { status: 'unhealthy', responseTime: 0, message: 'Health check failed', lastChecked: new Date().toISOString() }
        },
        metrics: {
          totalRequests: 0,
          activeConnections: 0,
          averageResponseTime: 0,
          errorRate: 0,
          memoryUsage: { used: 0, total: 0, percentage: 0 },
          cpuUsage: 0
        }
      }
    }
  }

  /**
   * Health check simples para load balancer
   */
  static async getSimpleHealth(): Promise<{ status: string; timestamp: string }> {
    try {
      // Verificação rápida do banco
      await prisma.$queryRaw`SELECT 1`
      
      return {
        status: 'healthy',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * Verifica saúde do banco de dados
   */
  private static async checkDatabase(): Promise<HealthCheck> {
    const startTime = Date.now()
    
    try {
      await prisma.$queryRaw`SELECT 1`
      const responseTime = Date.now() - startTime
      
      return {
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        responseTime,
        message: responseTime < 1000 ? 'Database responding normally' : 'Database responding slowly',
        lastChecked: new Date().toISOString()
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        message: `Database connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        lastChecked: new Date().toISOString()
      }
    }
  }

  /**
   * Verifica saúde do Redis
   */
  private static async checkRedis(): Promise<HealthCheck> {
    const startTime = Date.now()
    
    try {
      const redis = createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379'
      })
      
      await redis.connect()
      await redis.ping()
      await redis.disconnect()
      
      const responseTime = Date.now() - startTime
      
      return {
        status: responseTime < 500 ? 'healthy' : 'degraded',
        responseTime,
        message: responseTime < 500 ? 'Redis responding normally' : 'Redis responding slowly',
        lastChecked: new Date().toISOString()
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        message: `Redis connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        lastChecked: new Date().toISOString()
      }
    }
  }

  /**
   * Verifica uso de memória
   */
  private static async checkMemory(): Promise<HealthCheck> {
    const startTime = Date.now()
    
    try {
      const memUsage = process.memoryUsage()
      const totalMem = os.totalmem()
      const freeMem = os.freemem()
      const usedMem = totalMem - freeMem
      const memPercentage = (usedMem / totalMem) * 100
      
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
      let message = 'Memory usage normal'
      
      if (memPercentage > 90) {
        status = 'unhealthy'
        message = `Critical memory usage: ${memPercentage.toFixed(1)}%`
      } else if (memPercentage > 80) {
        status = 'degraded'
        message = `High memory usage: ${memPercentage.toFixed(1)}%`
      }
      
      return {
        status,
        responseTime: Date.now() - startTime,
        message,
        lastChecked: new Date().toISOString()
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        message: `Memory check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        lastChecked: new Date().toISOString()
      }
    }
  }

  /**
   * Verifica espaço em disco
   */
  private static async checkDisk(): Promise<HealthCheck> {
    const startTime = Date.now()
    
    try {
      const fs = require('fs')
      const stats = fs.statSync('.')
      
      // Simulação de verificação de disco (em produção, usar biblioteca específica)
      return {
        status: 'healthy',
        responseTime: Date.now() - startTime,
        message: 'Disk space sufficient',
        lastChecked: new Date().toISOString()
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        message: `Disk check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        lastChecked: new Date().toISOString()
      }
    }
  }

  /**
   * Verifica saúde do microserviço Python
   */
  private static async checkPythonMicroservice(): Promise<HealthCheck> {
    const startTime = Date.now()
    
    try {
      const response = await fetch('http://python-microservice:8000/health', {
        method: 'GET',
        timeout: 5000
      })
      
      const responseTime = Date.now() - startTime
      
      if (response.ok) {
        return {
          status: responseTime < 1000 ? 'healthy' : 'degraded',
          responseTime,
          message: responseTime < 1000 ? 'Python microservice responding normally' : 'Python microservice responding slowly',
          lastChecked: new Date().toISOString()
        }
      } else {
        return {
          status: 'unhealthy',
          responseTime,
          message: `Python microservice returned status ${response.status}`,
          lastChecked: new Date().toISOString()
        }
      }
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        message: `Python microservice unreachable: ${error instanceof Error ? error.message : 'Unknown error'}`,
        lastChecked: new Date().toISOString()
      }
    }
  }

  /**
   * Coleta métricas da aplicação
   */
  private static async collectMetrics() {
    const memUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    
    return {
      totalRequests: this.metrics.totalRequests,
      activeConnections: this.metrics.activeConnections,
      averageResponseTime: this.metrics.totalRequests > 0 
        ? this.metrics.totalResponseTime / this.metrics.totalRequests 
        : 0,
      errorRate: this.metrics.totalRequests > 0 
        ? (this.metrics.errorCount / this.metrics.totalRequests) * 100 
        : 0,
      memoryUsage: {
        used: memUsage.heapUsed,
        total: memUsage.heapTotal,
        percentage: (memUsage.heapUsed / memUsage.heapTotal) * 100
      },
      cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000000 // Convert to seconds
    }
  }

  /**
   * Determina status geral baseado nos checks individuais
   */
  private static determineOverallStatus(checks: Record<string, HealthCheck>): 'healthy' | 'degraded' | 'unhealthy' {
    const statuses = Object.values(checks).map(check => check.status)
    
    if (statuses.includes('unhealthy')) {
      return 'unhealthy'
    }
    
    if (statuses.includes('degraded')) {
      return 'degraded'
    }
    
    return 'healthy'
  }

  /**
   * Extrai resultado de Promise.allSettled
   */
  private static getCheckResult(result: PromiseSettledResult<HealthCheck>): HealthCheck {
    if (result.status === 'fulfilled') {
      return result.value
    } else {
      return {
        status: 'unhealthy',
        responseTime: 0,
        message: `Check failed: ${result.reason}`,
        lastChecked: new Date().toISOString()
      }
    }
  }

  /**
   * Incrementa contador de requests
   */
  static incrementRequestCount() {
    this.metrics.totalRequests++
  }

  /**
   * Adiciona tempo de resposta
   */
  static addResponseTime(time: number) {
    this.metrics.totalResponseTime += time
  }

  /**
   * Incrementa contador de erros
   */
  static incrementErrorCount() {
    this.metrics.errorCount++
  }

  /**
   * Atualiza conexões ativas
   */
  static setActiveConnections(count: number) {
    this.metrics.activeConnections = count
  }
}

export default HealthCheckService

import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  FormControlLabel,
  Switch,
  Chip,
  LinearProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Tabs,
  Tab,
  Fab
} from '@mui/material';
import {
  Backup as BackupIcon,
  Restore as RestoreIcon,
  Delete as DeleteIcon,
  Download as ExportIcon,
  Upload as ImportIcon,
  Settings as SettingsIcon,
  Verified as VerifyIcon,
  Schedule as ScheduleIcon,
  Add as AddIcon,
  MoreVert as MoreIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Storage as StorageIcon
} from '@mui/icons-material';
import { useBackup, BackupItem, RestoreOptions } from '../hooks/useBackup';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
};

const BackupManagement: React.FC = () => {
  const {
    backups,
    config,
    isCreating,
    isRestoring,
    lastBackup,
    nextScheduledBackup,
    createBackup,
    restoreBackup,
    deleteBackup,
    verifyBackup,
    updateConfig,
    exportBackup,
    importBackup,
    getBackupSize
  } = useBackup();

  const [activeTab, setActiveTab] = useState(0);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showRestoreDialog, setShowRestoreDialog] = useState(false);
  const [showConfigDialog, setShowConfigDialog] = useState(false);
  const [selectedBackup, setSelectedBackup] = useState<BackupItem | null>(null);
  const [menuAnchor, setMenuAnchor] = useState<{ element: HTMLElement; backupId: string } | null>(null);
  const [estimatedSize, setEstimatedSize] = useState<number>(0);
  const [verifyingBackup, setVerifyingBackup] = useState<string | null>(null);

  // Opções de criação de backup
  const [createOptions, setCreateOptions] = useState({
    includeHistory: true,
    includeLogs: true,
    includeSettings: true,
    includeTemplates: true,
    compression: config.compression,
    encryption: config.encryption
  });

  // Opções de restauração
  const [restoreOptions, setRestoreOptions] = useState<RestoreOptions>({
    backupId: '',
    restoreHistory: true,
    restoreLogs: true,
    restoreSettings: true,
    restoreTemplates: true,
    overwriteExisting: false
  });

  // Configuração temporária
  const [tempConfig, setTempConfig] = useState(config);

  useEffect(() => {
    setTempConfig(config);
  }, [config]);

  useEffect(() => {
    if (showCreateDialog) {
      getBackupSize().then(setEstimatedSize);
    }
  }, [showCreateDialog, createOptions, getBackupSize]);

  const handleCreateBackup = async () => {
    try {
      await createBackup(createOptions);
      setShowCreateDialog(false);
    } catch (error) {
      console.error('Erro ao criar backup:', error);
    }
  };

  const handleRestoreBackup = async () => {
    try {
      await restoreBackup(restoreOptions);
      setShowRestoreDialog(false);
      setSelectedBackup(null);
    } catch (error) {
      console.error('Erro ao restaurar backup:', error);
    }
  };

  const handleVerifyBackup = async (backupId: string) => {
    setVerifyingBackup(backupId);
    try {
      const isValid = await verifyBackup(backupId);
      alert(isValid ? 'Backup verificado com sucesso!' : 'Backup corrompido!');
    } catch (error) {
      console.error('Erro ao verificar backup:', error);
    } finally {
      setVerifyingBackup(null);
    }
  };

  const handleExportBackup = async (backupId: string) => {
    try {
      await exportBackup(backupId);
      setMenuAnchor(null);
    } catch (error) {
      console.error('Erro ao exportar backup:', error);
    }
  };

  const handleImportBackup = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        await importBackup(file);
      } catch (error) {
        console.error('Erro ao importar backup:', error);
      }
    }
  };

  const handleSaveConfig = () => {
    updateConfig(tempConfig);
    setShowConfigDialog(false);
  };

  const getStatusIcon = (status: BackupItem['status']) => {
    switch (status) {
      case 'completed':
        return <SuccessIcon color="success" />;
      case 'failed':
        return <ErrorIcon color="error" />;
      case 'corrupted':
        return <WarningIcon color="warning" />;
      case 'creating':
        return <LinearProgress />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: BackupItem['status']) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'corrupted':
        return 'warning';
      case 'creating':
        return 'info';
      default:
        return 'default';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Container maxWidth="xl">
      {/* Cabeçalho */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Gerenciamento de Backup
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Crie, gerencie e restaure backups do sistema
        </Typography>
      </Box>

      {/* Resumo */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <BackupIcon color="primary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4">{backups.length}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total de Backups
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <StorageIcon color="secondary" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4">
                    {formatFileSize(backups.reduce((total, backup) => total + backup.size, 0))}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Espaço Utilizado
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <SuccessIcon color="success" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="h4">
                    {backups.filter(b => b.status === 'completed').length}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Backups Válidos
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <ScheduleIcon color="info" sx={{ fontSize: 40 }} />
                <Box>
                  <Typography variant="body1" fontWeight="bold">
                    {lastBackup ? format(lastBackup.createdAt, 'dd/MM HH:mm') : 'Nunca'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Último Backup
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Status do backup automático */}
      {config.autoBackup.enabled && nextScheduledBackup && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            Próximo backup automático agendado para: {format(nextScheduledBackup, 'dd/MM/yyyy HH:mm', { locale: ptBR })}
          </Typography>
        </Alert>
      )}

      {/* Loading */}
      {(isCreating || isRestoring) && (
        <Box sx={{ mb: 3 }}>
          <Alert severity="info">
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <Typography>
                {isCreating ? 'Criando backup...' : 'Restaurando backup...'}
              </Typography>
              <LinearProgress sx={{ flexGrow: 1 }} />
            </Box>
          </Alert>
        </Box>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="Backups" />
          <Tab label="Configurações" />
        </Tabs>
      </Box>

      {/* Aba Backups */}
      <TabPanel value={activeTab} index={0}>
        {backups.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <BackupIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Nenhum backup encontrado
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Crie seu primeiro backup para proteger seus dados
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setShowCreateDialog(true)}
            >
              Criar Backup
            </Button>
          </Box>
        ) : (
          <List>
            {backups.map((backup) => (
              <React.Fragment key={backup.id}>
                <ListItem>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1">
                          {backup.name}
                        </Typography>
                        <Chip
                          label={backup.status}
                          color={getStatusColor(backup.status) as any}
                          size="small"
                        />
                        <Chip
                          label={backup.type}
                          variant="outlined"
                          size="small"
                        />
                        <Chip
                          label={backup.location}
                          variant="outlined"
                          size="small"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {backup.description}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Criado em {format(backup.createdAt, 'dd/MM/yyyy HH:mm', { locale: ptBR })} • 
                          Tamanho: {formatFileSize(backup.size)} • 
                          Checksum: {backup.checksum.substring(0, 8)}...
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        size="small"
                        startIcon={<RestoreIcon />}
                        onClick={() => {
                          setSelectedBackup(backup);
                          setRestoreOptions(prev => ({ ...prev, backupId: backup.id }));
                          setShowRestoreDialog(true);
                        }}
                        disabled={backup.status !== 'completed' || isRestoring}
                      >
                        Restaurar
                      </Button>
                      <IconButton
                        onClick={(e) => setMenuAnchor({ element: e.currentTarget, backupId: backup.id })}
                      >
                        <MoreIcon />
                      </IconButton>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
                <Divider />
              </React.Fragment>
            ))}
          </List>
        )}
      </TabPanel>

      {/* Aba Configurações */}
      <TabPanel value={activeTab} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Backup Automático
                </Typography>
                <FormControlLabel
                  control={
                    <Switch
                      checked={tempConfig.autoBackup.enabled}
                      onChange={(e) => setTempConfig(prev => ({
                        ...prev,
                        autoBackup: { ...prev.autoBackup, enabled: e.target.checked }
                      }))}
                    />
                  }
                  label="Habilitar backup automático"
                />
                {/* Mais configurações... */}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* FAB para criar backup */}
      <Fab
        color="primary"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setShowCreateDialog(true)}
        disabled={isCreating}
      >
        <AddIcon />
      </Fab>

      {/* Menu de ações */}
      <Menu
        anchorEl={menuAnchor?.element}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem
          onClick={() => {
            if (menuAnchor) handleVerifyBackup(menuAnchor.backupId);
          }}
          disabled={verifyingBackup === menuAnchor?.backupId}
        >
          <VerifyIcon sx={{ mr: 1 }} />
          {verifyingBackup === menuAnchor?.backupId ? 'Verificando...' : 'Verificar Integridade'}
        </MenuItem>
        <MenuItem onClick={() => {
          if (menuAnchor) handleExportBackup(menuAnchor.backupId);
        }}>
          <ExportIcon sx={{ mr: 1 }} />
          Exportar
        </MenuItem>
        <MenuItem
          onClick={() => {
            if (menuAnchor) {
              deleteBackup(menuAnchor.backupId);
              setMenuAnchor(null);
            }
          }}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          Excluir
        </MenuItem>
      </Menu>

      {/* Dialog para criar backup */}
      <Dialog open={showCreateDialog} onClose={() => setShowCreateDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Criar Novo Backup</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Incluir nos dados:
            </Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={createOptions.includeHistory}
                  onChange={(e) => setCreateOptions(prev => ({ ...prev, includeHistory: e.target.checked }))}
                />
              }
              label="Histórico de comandos"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={createOptions.includeLogs}
                  onChange={(e) => setCreateOptions(prev => ({ ...prev, includeLogs: e.target.checked }))}
                />
              }
              label="Logs de auditoria"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={createOptions.includeSettings}
                  onChange={(e) => setCreateOptions(prev => ({ ...prev, includeSettings: e.target.checked }))}
                />
              }
              label="Configurações do usuário"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={createOptions.includeTemplates}
                  onChange={(e) => setCreateOptions(prev => ({ ...prev, includeTemplates: e.target.checked }))}
                />
              }
              label="Templates de comandos"
            />
            
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              Tamanho estimado: {formatFileSize(estimatedSize)}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCreateDialog(false)}>Cancelar</Button>
          <Button
            onClick={handleCreateBackup}
            variant="contained"
            disabled={isCreating}
            startIcon={<BackupIcon />}
          >
            {isCreating ? 'Criando...' : 'Criar Backup'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para restaurar backup */}
      <Dialog open={showRestoreDialog} onClose={() => setShowRestoreDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Restaurar Backup: {selectedBackup?.name}
        </DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            Esta ação irá sobrescrever os dados atuais. Certifique-se de ter um backup recente.
          </Alert>
          {/* Opções de restauração... */}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowRestoreDialog(false)}>Cancelar</Button>
          <Button
            onClick={handleRestoreBackup}
            variant="contained"
            color="warning"
            disabled={isRestoring}
            startIcon={<RestoreIcon />}
          >
            {isRestoring ? 'Restaurando...' : 'Restaurar'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default BackupManagement;

import { useState, useEffect, useCallback } from 'react';

export interface ReportFilter {
  dateRange: {
    start: Date;
    end: Date;
  };
  servers?: string[];
  users?: string[];
  commandTypes?: string[];
  status?: ('success' | 'error' | 'timeout')[];
  deviceTypes?: string[];
}

export interface ReportData {
  id: string;
  name: string;
  type: 'usage' | 'performance' | 'errors' | 'security' | 'custom';
  description: string;
  data: any;
  generatedAt: Date;
  filters: ReportFilter;
  format: 'table' | 'chart' | 'summary';
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: ReportData['type'];
  defaultFilters: Partial<ReportFilter>;
  fields: string[];
  chartType?: 'line' | 'bar' | 'pie' | 'area';
  schedule?: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly';
    time: string;
    recipients: string[];
  };
}

interface UseReportsReturn {
  reports: ReportData[];
  templates: ReportTemplate[];
  isGenerating: boolean;
  generateReport: (templateId: string, filters: ReportFilter) => Promise<string>;
  saveReport: (report: ReportData) => void;
  deleteReport: (reportId: string) => void;
  exportReport: (reportId: string, format: 'pdf' | 'excel' | 'csv') => Promise<void>;
  scheduleReport: (templateId: string, schedule: ReportTemplate['schedule']) => void;
  getReportData: (reportId: string) => ReportData | null;
  createTemplate: (template: Omit<ReportTemplate, 'id'>) => string;
  updateTemplate: (templateId: string, updates: Partial<ReportTemplate>) => void;
  deleteTemplate: (templateId: string) => void;
}

const STORAGE_KEY = 'sem-fronteiras-reports';
const TEMPLATES_KEY = 'sem-fronteiras-report-templates';

const DEFAULT_TEMPLATES: ReportTemplate[] = [
  {
    id: 'usage-summary',
    name: 'Resumo de Uso',
    description: 'Relatório geral de uso do sistema',
    type: 'usage',
    defaultFilters: {
      dateRange: {
        start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        end: new Date()
      }
    },
    fields: ['commands_executed', 'users_active', 'servers_accessed', 'success_rate'],
    chartType: 'bar'
  },
  {
    id: 'performance-analysis',
    name: 'Análise de Performance',
    description: 'Métricas de performance e tempo de resposta',
    type: 'performance',
    defaultFilters: {
      dateRange: {
        start: new Date(Date.now() - 24 * 60 * 60 * 1000),
        end: new Date()
      }
    },
    fields: ['avg_response_time', 'max_response_time', 'timeout_rate', 'server_load'],
    chartType: 'line'
  },
  {
    id: 'error-report',
    name: 'Relatório de Erros',
    description: 'Análise detalhada de erros e falhas',
    type: 'errors',
    defaultFilters: {
      dateRange: {
        start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        end: new Date()
      },
      status: ['error', 'timeout']
    },
    fields: ['error_count', 'error_types', 'affected_servers', 'error_trends'],
    chartType: 'pie'
  },
  {
    id: 'security-audit',
    name: 'Auditoria de Segurança',
    description: 'Relatório de atividades de segurança',
    type: 'security',
    defaultFilters: {
      dateRange: {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: new Date()
      }
    },
    fields: ['login_attempts', 'failed_logins', 'privilege_escalations', 'suspicious_activities'],
    chartType: 'area'
  }
];

export const useReports = (): UseReportsReturn => {
  const [reports, setReports] = useState<ReportData[]>([]);
  const [templates, setTemplates] = useState<ReportTemplate[]>(DEFAULT_TEMPLATES);
  const [isGenerating, setIsGenerating] = useState(false);

  // Carregar dados do localStorage
  useEffect(() => {
    try {
      const storedReports = localStorage.getItem(STORAGE_KEY);
      const storedTemplates = localStorage.getItem(TEMPLATES_KEY);
      
      if (storedReports) {
        const parsed = JSON.parse(storedReports);
        const reportsWithDates = parsed.map((report: any) => ({
          ...report,
          generatedAt: new Date(report.generatedAt),
          filters: {
            ...report.filters,
            dateRange: {
              start: new Date(report.filters.dateRange.start),
              end: new Date(report.filters.dateRange.end)
            }
          }
        }));
        setReports(reportsWithDates);
      }
      
      if (storedTemplates) {
        const parsed = JSON.parse(storedTemplates);
        setTemplates([...DEFAULT_TEMPLATES, ...parsed]);
      }
    } catch (error) {
      console.error('Erro ao carregar relatórios:', error);
    }
  }, []);

  // Salvar no localStorage
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(reports));
    } catch (error) {
      console.error('Erro ao salvar relatórios:', error);
    }
  }, [reports]);

  useEffect(() => {
    try {
      const customTemplates = templates.filter(t => !DEFAULT_TEMPLATES.find(dt => dt.id === t.id));
      localStorage.setItem(TEMPLATES_KEY, JSON.stringify(customTemplates));
    } catch (error) {
      console.error('Erro ao salvar templates:', error);
    }
  }, [templates]);

  const generateMockData = (template: ReportTemplate, filters: ReportFilter) => {
    const daysDiff = Math.ceil((filters.dateRange.end.getTime() - filters.dateRange.start.getTime()) / (1000 * 60 * 60 * 24));
    
    switch (template.type) {
      case 'usage':
        return {
          summary: {
            commands_executed: Math.floor(Math.random() * 1000) + 500,
            users_active: Math.floor(Math.random() * 50) + 10,
            servers_accessed: Math.floor(Math.random() * 20) + 5,
            success_rate: Math.random() * 20 + 80
          },
          daily_data: Array.from({ length: daysDiff }, (_, i) => ({
            date: new Date(filters.dateRange.start.getTime() + i * 24 * 60 * 60 * 1000),
            commands: Math.floor(Math.random() * 100) + 20,
            users: Math.floor(Math.random() * 10) + 2,
            success_rate: Math.random() * 20 + 75
          }))
        };
        
      case 'performance':
        return {
          summary: {
            avg_response_time: Math.random() * 2000 + 500,
            max_response_time: Math.random() * 5000 + 2000,
            timeout_rate: Math.random() * 5,
            server_load: Math.random() * 100
          },
          hourly_data: Array.from({ length: 24 }, (_, i) => ({
            hour: i,
            response_time: Math.random() * 1000 + 200,
            requests: Math.floor(Math.random() * 50) + 10,
            errors: Math.floor(Math.random() * 5)
          }))
        };
        
      case 'errors':
        return {
          summary: {
            total_errors: Math.floor(Math.random() * 100) + 10,
            error_rate: Math.random() * 10 + 2,
            most_common: 'Connection timeout',
            affected_servers: Math.floor(Math.random() * 5) + 1
          },
          error_types: [
            { type: 'Connection timeout', count: Math.floor(Math.random() * 30) + 10 },
            { type: 'Authentication failed', count: Math.floor(Math.random() * 20) + 5 },
            { type: 'Command not found', count: Math.floor(Math.random() * 15) + 3 },
            { type: 'Permission denied', count: Math.floor(Math.random() * 10) + 2 }
          ]
        };
        
      case 'security':
        return {
          summary: {
            login_attempts: Math.floor(Math.random() * 500) + 100,
            failed_logins: Math.floor(Math.random() * 50) + 5,
            privilege_escalations: Math.floor(Math.random() * 10),
            suspicious_activities: Math.floor(Math.random() * 5)
          },
          security_events: Array.from({ length: 10 }, (_, i) => ({
            timestamp: new Date(Date.now() - Math.random() * daysDiff * 24 * 60 * 60 * 1000),
            type: ['login_attempt', 'failed_login', 'privilege_escalation'][Math.floor(Math.random() * 3)],
            user: `user${Math.floor(Math.random() * 10) + 1}`,
            ip: `192.168.1.${Math.floor(Math.random() * 254) + 1}`,
            severity: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)]
          }))
        };
        
      default:
        return { message: 'Dados não disponíveis para este tipo de relatório' };
    }
  };

  const generateReport = useCallback(async (templateId: string, filters: ReportFilter): Promise<string> => {
    setIsGenerating(true);
    
    try {
      const template = templates.find(t => t.id === templateId);
      if (!template) {
        throw new Error('Template não encontrado');
      }
      
      // Simular geração de relatório
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const reportData = generateMockData(template, filters);
      
      const newReport: ReportData = {
        id: Date.now().toString(),
        name: `${template.name} - ${filters.dateRange.start.toLocaleDateString()}`,
        type: template.type,
        description: template.description,
        data: reportData,
        generatedAt: new Date(),
        filters,
        format: template.chartType ? 'chart' : 'table'
      };
      
      setReports(prev => [newReport, ...prev]);
      return newReport.id;
    } catch (error) {
      console.error('Erro ao gerar relatório:', error);
      throw error;
    } finally {
      setIsGenerating(false);
    }
  }, [templates]);

  const saveReport = useCallback((report: ReportData) => {
    setReports(prev => {
      const existing = prev.find(r => r.id === report.id);
      if (existing) {
        return prev.map(r => r.id === report.id ? report : r);
      }
      return [report, ...prev];
    });
  }, []);

  const deleteReport = useCallback((reportId: string) => {
    setReports(prev => prev.filter(r => r.id !== reportId));
  }, []);

  const exportReport = useCallback(async (reportId: string, format: 'pdf' | 'excel' | 'csv') => {
    const report = reports.find(r => r.id === reportId);
    if (!report) {
      throw new Error('Relatório não encontrado');
    }
    
    // Simular exportação
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const filename = `${report.name.replace(/\s+/g, '-').toLowerCase()}.${format}`;
    console.log(`Exportando relatório como ${filename}`);
    
    // Em produção, aqui seria feita a exportação real
    alert(`Relatório exportado como ${filename}`);
  }, [reports]);

  const scheduleReport = useCallback((templateId: string, schedule: ReportTemplate['schedule']) => {
    setTemplates(prev => prev.map(template => 
      template.id === templateId 
        ? { ...template, schedule }
        : template
    ));
  }, []);

  const getReportData = useCallback((reportId: string) => {
    return reports.find(r => r.id === reportId) || null;
  }, [reports]);

  const createTemplate = useCallback((template: Omit<ReportTemplate, 'id'>): string => {
    const id = Date.now().toString();
    const newTemplate: ReportTemplate = { ...template, id };
    setTemplates(prev => [...prev, newTemplate]);
    return id;
  }, []);

  const updateTemplate = useCallback((templateId: string, updates: Partial<ReportTemplate>) => {
    setTemplates(prev => prev.map(template => 
      template.id === templateId 
        ? { ...template, ...updates }
        : template
    ));
  }, []);

  const deleteTemplate = useCallback((templateId: string) => {
    // Não permitir deletar templates padrão
    if (DEFAULT_TEMPLATES.find(t => t.id === templateId)) {
      throw new Error('Não é possível deletar templates padrão');
    }
    setTemplates(prev => prev.filter(t => t.id !== templateId));
  }, []);

  return {
    reports,
    templates,
    isGenerating,
    generateReport,
    saveReport,
    deleteReport,
    exportReport,
    scheduleReport,
    getReportData,
    createTemplate,
    updateTemplate,
    deleteTemplate
  };
};

export default useReports;

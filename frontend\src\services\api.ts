import { api } from '../lib/api'
import {
  CommandHistory,
  CommandResult,
  CommandTemplate,
  CreateCommandTemplateDTO,
  CreateServerDTO,
  SSHServer,
  UpdateCommandTemplateDTO,
  UpdateServerDTO,
  PaginatedResponse
} from '../types/server'

export async function listServers(): Promise<SSHServer[]> {
  const response = await api.get('/api/servers')
  // Verifica se a resposta é um array ou um objeto com a propriedade servers
  if (Array.isArray(response.data)) {
    return response.data
  } else if (response.data && typeof response.data === 'object' && 'servers' in response.data) {
    return response.data.servers || []
  }
  return []
}

export async function createServer(data: CreateServerDTO): Promise<SSHServer> {
  const response = await api.post('/api/servers', data)
  return response.data
}

export async function updateServer(id: string, data: UpdateServerDTO): Promise<SSHServer> {
  try {
    const response = await api.put(`/api/servers/${id}`, data);
    return response.data;
  } catch (error: any) {
    console.error('Erro ao atualizar servidor:', error);
    throw error;
  }
}

export async function deleteServer(id: string): Promise<void> {
  await api.delete(`/api/servers/${id}`)
}

export async function executeCommand(serverId: string, commandId: string): Promise<CommandResult> {
  const response = await api.post(`/api/servers/${serverId}/execute`, { commandId })
  return response.data
}

export async function listCommandHistory(page = 1, limit = 10): Promise<PaginatedResponse<CommandHistory>> {
  const response = await api.get('/api/servers/command-history', {
    params: { page, limit }
  })
  return {
    data: response.data.history,
    pagination: response.data.pagination
  }
}

// Templates de Comandos
export async function listCommandTemplates(): Promise<CommandTemplate[]> {
  const response = await api.get('/api/command-templates')
  return response.data.templates
}

export async function getCommandTemplate(id: string): Promise<CommandTemplate> {
  const response = await api.get(`/api/command-templates/${id}`)
  return response.data
}

export async function createCommandTemplate(data: CreateCommandTemplateDTO): Promise<CommandTemplate> {
  const response = await api.post('/api/command-templates', data)
  return response.data
}

export async function updateCommandTemplate(id: string, data: UpdateCommandTemplateDTO): Promise<CommandTemplate> {
  const response = await api.put(`/api/command-templates/${id}`, data)
  return response.data
}

export async function deleteCommandTemplate(id: string): Promise<void> {
  await api.delete(`/api/command-templates/${id}`)
}

export async function applyTemplateToServer(templateId: string, serverId: string): Promise<any> {
  const response = await api.post(`/api/command-templates/${templateId}/apply/${serverId}`)
  return response.data
}

export async function updateCommandsOrder(serverId: string, commands: { id: string, order: number }[]): Promise<SSHServer> {
  const response = await api.put(`/api/servers/${serverId}/commands-order`, { commands })
  return response.data
}
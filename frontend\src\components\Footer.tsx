import React from 'react'
import { Terminal, Github, Twitter, Linkedin, Heart } from 'lucide-react'
import BRAND_CONFIG from '../config/brand'

interface FooterProps {
  variant?: 'default' | 'minimal'
}

const Footer: React.FC<FooterProps> = ({ variant = 'default' }) => {
  if (variant === 'minimal') {
    return (
      <footer className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="flex items-center justify-center w-6 h-6 bg-blue-600 rounded">
                <Terminal className="h-4 w-4 text-white" />
              </div>
              <span className="text-sm font-medium text-gray-900">{BRAND_CONFIG.name}</span>
            </div>
            <p className="text-sm text-gray-500">{BRAND_CONFIG.copyright}</p>
          </div>
        </div>
      </footer>
    )
  }

  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              <div className="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg">
                <Terminal className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold">{BRAND_CONFIG.name}</h3>
                <p className="text-blue-400 text-sm">{BRAND_CONFIG.tagline}</p>
              </div>
            </div>
            <p className="text-gray-300 mb-4 max-w-md">
              {BRAND_CONFIG.description}
            </p>
            <div className="flex space-x-4">
              <a
                href={BRAND_CONFIG.social.github}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Github className="h-5 w-5" />
              </a>
              <a
                href={BRAND_CONFIG.social.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Twitter className="h-5 w-5" />
              </a>
              <a
                href={BRAND_CONFIG.social.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-white transition-colors"
              >
                <Linkedin className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Produto</h4>
            <ul className="space-y-2">
              <li>
                <a href="#features" className="text-gray-300 hover:text-white transition-colors">
                  Funcionalidades
                </a>
              </li>
              <li>
                <a href="#pricing" className="text-gray-300 hover:text-white transition-colors">
                  Preços
                </a>
              </li>
              <li>
                <a href="#integrations" className="text-gray-300 hover:text-white transition-colors">
                  Integrações
                </a>
              </li>
              <li>
                <a href="#api" className="text-gray-300 hover:text-white transition-colors">
                  API
                </a>
              </li>
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Suporte</h4>
            <ul className="space-y-2">
              <li>
                <a 
                  href={BRAND_CONFIG.support.documentation}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  Documentação
                </a>
              </li>
              <li>
                <a 
                  href={BRAND_CONFIG.support.helpCenter}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  Central de Ajuda
                </a>
              </li>
              <li>
                <a 
                  href={BRAND_CONFIG.support.community}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  Comunidade
                </a>
              </li>
              <li>
                <a 
                  href={BRAND_CONFIG.support.statusPage}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-300 hover:text-white transition-colors"
                >
                  Status
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-8 border-t border-gray-800">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <p className="text-gray-400 text-sm">{BRAND_CONFIG.copyright}</p>
            </div>
            
            <div className="flex items-center space-x-6 text-sm">
              <a href="#privacy" className="text-gray-400 hover:text-white transition-colors">
                Política de Privacidade
              </a>
              <a href="#terms" className="text-gray-400 hover:text-white transition-colors">
                Termos de Uso
              </a>
              <a href="#security" className="text-gray-400 hover:text-white transition-colors">
                Segurança
              </a>
            </div>
          </div>
          
          <div className="mt-4 flex items-center justify-center md:justify-start">
            <p className="text-gray-400 text-sm flex items-center">
              Feito com <Heart className="h-4 w-4 text-red-500 mx-1" /> para DevOps e SysAdmins
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer

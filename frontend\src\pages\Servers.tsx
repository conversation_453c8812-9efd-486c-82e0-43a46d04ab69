import React, { useState, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { api } from '../lib/api'
import ServerList from '../components/ServerList'
import AddServerModal from '../components/AddServerModal'
import EditServerModal from '../components/EditServerModal'
import CommandModal from '../components/CommandModal'
import ServerGroupManager from '../components/ServerGroupManager'
import ServerGroupFilter from '../components/ServerGroupFilter'
import { SSHServer, Command } from '../types/server'
import { PlusCircle, Database, FolderPlus } from 'lucide-react'
import { useAuth } from '../contexts/AuthContext'

export function Servers() {
  const { user } = useAuth()
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isCommandModalOpen, setIsCommandModalOpen] = useState(false)
  const [isGroupManagerOpen, setIsGroupManagerOpen] = useState(false)
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null)
  const [selectedServer, setSelectedServer] = useState<SSHServer & { commands: Command[] } | null>(null)

  const { data: servers, refetch } = useQuery<(SSHServer & { commands: Command[] })[]>({
    queryKey: ['servers'],
    queryFn: async () => {
      const response = await api.get('/api/servers')
      const data = response.data.servers || []
      return data.map((server: SSHServer) => ({
        ...server,
        commands: server.commands || []
      }))
    },
  })

  // Filtrar servidores por grupo selecionado
  const filteredServers = useMemo(() => {
    if (!servers) return []

    if (!selectedGroupId) {
      return servers
    }

    return servers.filter(server =>
      server.groupMembers?.some(member => member.group.id === selectedGroupId)
    )
  }, [servers, selectedGroupId])

  function handleAddServer() {
    setIsAddModalOpen(true)
  }

  function handleEditServer(server: SSHServer & { commands: Command[] }) {
    setSelectedServer(server)
    setIsEditModalOpen(true)
  }

  function handleExecuteCommand(server: SSHServer & { commands: Command[] }) {
    setSelectedServer(server)
    setIsCommandModalOpen(true)
  }

  function handleDeleteServer(server: SSHServer & { commands: Command[] }) {
    if (confirm('Tem certeza que deseja excluir este servidor?')) {
      api.delete(`/api/servers/${server.id}`).then(() => {
        refetch()
      })
    }
  }

  return (
    <div className="py-4 sm:py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 gap-4">
          <div className="flex items-center gap-2">
            <Database className="h-6 w-6 text-primary-500" />
            <h1 className="text-xl sm:text-2xl font-semibold text-gray-900">Servidores</h1>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <button
              onClick={() => setIsGroupManagerOpen(true)}
              className="bg-green-600 text-white hover:bg-green-700 px-4 py-2 rounded-md flex items-center justify-center gap-2 w-full sm:w-auto"
            >
              <FolderPlus className="h-5 w-5" />
              Gerenciar Grupos
            </button>
            {user?.role === 'ADMIN' && (
              <button
                onClick={handleAddServer}
                className="bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md flex items-center justify-center gap-2 w-full sm:w-auto"
              >
                <PlusCircle className="h-5 w-5" />
                Adicionar Servidor
              </button>
            )}
          </div>
        </div>

        {/* Filtro por grupos */}
        <div className="mb-4 sm:mb-6">
          <ServerGroupFilter
            selectedGroupId={selectedGroupId}
            onGroupSelect={setSelectedGroupId}
          />
        </div>

        <ServerList
          servers={filteredServers}
          onServerUpdated={refetch}
        />

        {isAddModalOpen && (
          <AddServerModal
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
            onServerAdded={refetch}
          />
        )}

        {isEditModalOpen && selectedServer && (
          <EditServerModal
            isOpen={isEditModalOpen}
            server={selectedServer}
            onClose={() => setIsEditModalOpen(false)}
            onServerUpdated={refetch}
          />
        )}

        {isCommandModalOpen && selectedServer && (
          <CommandModal
            isOpen={isCommandModalOpen}
            server={selectedServer}
            onClose={() => setIsCommandModalOpen(false)}
          />
        )}

        <ServerGroupManager
          isOpen={isGroupManagerOpen}
          onClose={() => setIsGroupManagerOpen(false)}
        />
      </div>
    </div>
  )
}
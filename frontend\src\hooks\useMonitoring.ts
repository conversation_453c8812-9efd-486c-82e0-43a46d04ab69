import { useState, useEffect, useCallback, useRef } from 'react';

export interface MonitoringMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  threshold: {
    warning: number;
    critical: number;
  };
  trend: 'up' | 'down' | 'stable';
  category: 'system' | 'network' | 'application' | 'security';
}

export interface MonitoringAlert {
  id: string;
  metricId: string;
  level: 'warning' | 'critical';
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  resolvedAt?: Date;
}

export interface MonitoringConfig {
  refreshInterval: number;
  retentionPeriod: number; // em horas
  alertsEnabled: boolean;
  thresholds: Record<string, { warning: number; critical: number }>;
}

interface UseMonitoringReturn {
  metrics: MonitoringMetric[];
  alerts: MonitoringAlert[];
  config: MonitoringConfig;
  isConnected: boolean;
  lastUpdate: Date | null;
  updateConfig: (newConfig: Partial<MonitoringConfig>) => void;
  acknowledgeAlert: (alertId: string) => void;
  resolveAlert: (alertId: string) => void;
  getMetricHistory: (metricId: string, hours: number) => MonitoringMetric[];
  exportMetrics: (startDate: Date, endDate: Date) => Promise<string>;
  startMonitoring: () => void;
  stopMonitoring: () => void;
}

const DEFAULT_CONFIG: MonitoringConfig = {
  refreshInterval: 5000, // 5 segundos
  retentionPeriod: 24, // 24 horas
  alertsEnabled: true,
  thresholds: {
    cpu_usage: { warning: 70, critical: 90 },
    memory_usage: { warning: 80, critical: 95 },
    disk_usage: { warning: 85, critical: 95 },
    network_latency: { warning: 100, critical: 500 },
    error_rate: { warning: 5, critical: 10 },
    response_time: { warning: 1000, critical: 3000 }
  }
};

const STORAGE_KEY = 'sem-fronteiras-monitoring-config';
const METRICS_KEY = 'sem-fronteiras-monitoring-metrics';

export const useMonitoring = (): UseMonitoringReturn => {
  const [metrics, setMetrics] = useState<MonitoringMetric[]>([]);
  const [alerts, setAlerts] = useState<MonitoringAlert[]>([]);
  const [config, setConfig] = useState<MonitoringConfig>(DEFAULT_CONFIG);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  
  const intervalRef = useRef<NodeJS.Timeout>();
  const metricsHistoryRef = useRef<MonitoringMetric[]>([]);

  // Carregar configuração do localStorage
  useEffect(() => {
    try {
      const storedConfig = localStorage.getItem(STORAGE_KEY);
      if (storedConfig) {
        const parsed = JSON.parse(storedConfig);
        setConfig({ ...DEFAULT_CONFIG, ...parsed });
      }
    } catch (error) {
      console.error('Erro ao carregar configuração de monitoramento:', error);
    }
  }, []);

  // Salvar configuração no localStorage
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(config));
    } catch (error) {
      console.error('Erro ao salvar configuração de monitoramento:', error);
    }
  }, [config]);

  // Gerar métricas mock
  const generateMockMetrics = useCallback((): MonitoringMetric[] => {
    const now = new Date();
    
    return [
      {
        id: 'cpu_usage',
        name: 'Uso de CPU',
        value: Math.random() * 100,
        unit: '%',
        timestamp: now,
        threshold: config.thresholds.cpu_usage,
        trend: Math.random() > 0.5 ? 'up' : 'down',
        category: 'system'
      },
      {
        id: 'memory_usage',
        name: 'Uso de Memória',
        value: Math.random() * 100,
        unit: '%',
        timestamp: now,
        threshold: config.thresholds.memory_usage,
        trend: Math.random() > 0.5 ? 'up' : 'stable',
        category: 'system'
      },
      {
        id: 'disk_usage',
        name: 'Uso de Disco',
        value: Math.random() * 100,
        unit: '%',
        timestamp: now,
        threshold: config.thresholds.disk_usage,
        trend: 'up',
        category: 'system'
      },
      {
        id: 'network_latency',
        name: 'Latência de Rede',
        value: Math.random() * 200 + 10,
        unit: 'ms',
        timestamp: now,
        threshold: config.thresholds.network_latency,
        trend: Math.random() > 0.5 ? 'down' : 'stable',
        category: 'network'
      },
      {
        id: 'active_connections',
        name: 'Conexões Ativas',
        value: Math.floor(Math.random() * 100) + 10,
        unit: 'conn',
        timestamp: now,
        threshold: { warning: 80, critical: 100 },
        trend: Math.random() > 0.5 ? 'up' : 'down',
        category: 'network'
      },
      {
        id: 'error_rate',
        name: 'Taxa de Erro',
        value: Math.random() * 15,
        unit: '%',
        timestamp: now,
        threshold: config.thresholds.error_rate,
        trend: Math.random() > 0.7 ? 'up' : 'down',
        category: 'application'
      },
      {
        id: 'response_time',
        name: 'Tempo de Resposta',
        value: Math.random() * 2000 + 100,
        unit: 'ms',
        timestamp: now,
        threshold: config.thresholds.response_time,
        trend: Math.random() > 0.5 ? 'down' : 'up',
        category: 'application'
      },
      {
        id: 'failed_logins',
        name: 'Logins Falhados',
        value: Math.floor(Math.random() * 10),
        unit: 'count',
        timestamp: now,
        threshold: { warning: 5, critical: 10 },
        trend: Math.random() > 0.8 ? 'up' : 'stable',
        category: 'security'
      }
    ];
  }, [config.thresholds]);

  // Verificar alertas
  const checkAlerts = useCallback((newMetrics: MonitoringMetric[]) => {
    if (!config.alertsEnabled) return;

    const newAlerts: MonitoringAlert[] = [];

    newMetrics.forEach(metric => {
      let level: 'warning' | 'critical' | null = null;
      
      if (metric.value >= metric.threshold.critical) {
        level = 'critical';
      } else if (metric.value >= metric.threshold.warning) {
        level = 'warning';
      }

      if (level) {
        // Verificar se já existe um alerta ativo para esta métrica
        const existingAlert = alerts.find(alert => 
          alert.metricId === metric.id && 
          !alert.acknowledged && 
          !alert.resolvedAt
        );

        if (!existingAlert) {
          newAlerts.push({
            id: `${metric.id}-${Date.now()}`,
            metricId: metric.id,
            level,
            message: `${metric.name} está em ${metric.value.toFixed(1)}${metric.unit} (limite ${level}: ${metric.threshold[level]}${metric.unit})`,
            timestamp: new Date(),
            acknowledged: false
          });
        }
      }
    });

    if (newAlerts.length > 0) {
      setAlerts(prev => [...newAlerts, ...prev]);
    }
  }, [alerts, config.alertsEnabled]);

  // Função para coletar métricas
  const collectMetrics = useCallback(async () => {
    try {
      setIsConnected(true);
      
      // Simular coleta de métricas
      const newMetrics = generateMockMetrics();
      
      setMetrics(newMetrics);
      setLastUpdate(new Date());
      
      // Adicionar ao histórico
      metricsHistoryRef.current = [
        ...metricsHistoryRef.current,
        ...newMetrics
      ].slice(-1000); // Manter apenas os últimos 1000 registros
      
      // Verificar alertas
      checkAlerts(newMetrics);
      
    } catch (error) {
      console.error('Erro ao coletar métricas:', error);
      setIsConnected(false);
    }
  }, [generateMockMetrics, checkAlerts]);

  // Iniciar monitoramento
  const startMonitoring = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    // Coletar métricas imediatamente
    collectMetrics();
    
    // Configurar intervalo
    intervalRef.current = setInterval(collectMetrics, config.refreshInterval);
  }, [collectMetrics, config.refreshInterval]);

  // Parar monitoramento
  const stopMonitoring = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = undefined;
    }
    setIsConnected(false);
  }, []);

  // Iniciar monitoramento automaticamente
  useEffect(() => {
    startMonitoring();
    return () => stopMonitoring();
  }, [startMonitoring, stopMonitoring]);

  // Atualizar configuração
  const updateConfig = useCallback((newConfig: Partial<MonitoringConfig>) => {
    setConfig(prev => ({ ...prev, ...newConfig }));
    
    // Reiniciar monitoramento se o intervalo mudou
    if (newConfig.refreshInterval && newConfig.refreshInterval !== config.refreshInterval) {
      startMonitoring();
    }
  }, [config.refreshInterval, startMonitoring]);

  // Reconhecer alerta
  const acknowledgeAlert = useCallback((alertId: string) => {
    setAlerts(prev => prev.map(alert =>
      alert.id === alertId
        ? { ...alert, acknowledged: true }
        : alert
    ));
  }, []);

  // Resolver alerta
  const resolveAlert = useCallback((alertId: string) => {
    setAlerts(prev => prev.map(alert =>
      alert.id === alertId
        ? { ...alert, resolvedAt: new Date() }
        : alert
    ));
  }, []);

  // Obter histórico de métrica
  const getMetricHistory = useCallback((metricId: string, hours: number) => {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    return metricsHistoryRef.current.filter(metric =>
      metric.id === metricId && metric.timestamp >= cutoffTime
    );
  }, []);

  // Exportar métricas
  const exportMetrics = useCallback(async (startDate: Date, endDate: Date): Promise<string> => {
    const filteredMetrics = metricsHistoryRef.current.filter(metric =>
      metric.timestamp >= startDate && metric.timestamp <= endDate
    );
    
    const csvHeader = 'Timestamp,Metric,Value,Unit,Category\n';
    const csvData = filteredMetrics.map(metric =>
      `${metric.timestamp.toISOString()},${metric.name},${metric.value},${metric.unit},${metric.category}`
    ).join('\n');
    
    return csvHeader + csvData;
  }, []);

  // Limpeza automática do histórico
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const cutoffTime = new Date(Date.now() - config.retentionPeriod * 60 * 60 * 1000);
      metricsHistoryRef.current = metricsHistoryRef.current.filter(
        metric => metric.timestamp >= cutoffTime
      );
    }, 60000); // Limpar a cada minuto

    return () => clearInterval(cleanupInterval);
  }, [config.retentionPeriod]);

  return {
    metrics,
    alerts,
    config,
    isConnected,
    lastUpdate,
    updateConfig,
    acknowledgeAlert,
    resolveAlert,
    getMetricHistory,
    exportMetrics,
    startMonitoring,
    stopMonitoring
  };
};

export default useMonitoring;

import { PrismaClient, User, Role } from '@prisma/client'
import { hash } from 'bcryptjs'
import { CreateUserDTO, UpdateUserDTO } from '../types/user'

export class UserService {
  constructor(private prisma: PrismaClient) {}

  async create(data: CreateUserDTO): Promise<User> {
    const hashedPassword = await hash(data.password, 10)

    const existingUser = await this.prisma.user.findUnique({
      where: { email: data.email }
    })

    if (existingUser) {
      throw new Error('Usuário já existe com este e-mail')
    }

    return this.prisma.user.create({
      data: {
        ...data,
        password: hashedPassword
      }
    })
  }

  async findAll(includeInactive: boolean = false): Promise<User[]> {
    if (includeInactive) {
      return this.prisma.user.findMany({
        orderBy: { createdAt: 'desc' }
      })
    } else {
      // Usar SQL direto para filtrar usuários ativos
      const activeUsers = await this.prisma.$queryRaw<User[]>`
        SELECT * FROM "User" WHERE active = true ORDER BY "createdAt" DESC
      `
      return activeUsers
    }
  }

  async findById(id: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { id }
    })
  }

  async update(id: string, data: UpdateUserDTO): Promise<User> {
    console.log('Service - Dados recebidos para atualização:', id, data)
    
    // Cria um objeto com os dados a serem atualizados
    const updateData: any = { ...data }
    
    // Se a senha foi fornecida, faz o hash
    if (data.password) {
      console.log('Service - Atualizando senha')
      updateData.password = await hash(data.password, 10)
    } else {
      console.log('Service - Mantendo senha atual')
      // Se não foi fornecida senha, remove do objeto para não atualizar
      delete updateData.password
    }

    if (data.email) {
      const existingUser = await this.prisma.user.findFirst({
        where: {
          email: data.email,
          NOT: { id }
        }
      })

      if (existingUser) {
        throw new Error('E-mail já está em uso por outro usuário')
      }
    }

    // Se estiver alterando o papel para não-admin ou desativando um admin, verificar se é o último
    if ((data.role && data.role !== 'ADMIN' && updateData.active !== false) || 
        (data.active === false)) {
      const user = await this.findById(id)
      
      if (user?.role === 'ADMIN') {
        // Verificar se é o último administrador ativo usando SQL direto
        const activeAdmins = await this.prisma.$queryRaw<Array<{ count: bigint }>>`
          SELECT COUNT(*) as count FROM "User" 
          WHERE role = 'ADMIN' AND active = true AND id != ${id}
        `
        const activeAdminCount = Number(activeAdmins[0]?.count || 0)
        
        if (activeAdminCount === 0) {
          throw new Error('Não é possível alterar o último administrador ativo do sistema')
        }
      }
    }

    console.log('Service - Dados finais para atualização:', updateData)

    try {
      // Usar SQL direto para atualizar o usuário se active estiver presente
      if (updateData.active !== undefined) {
        // Em vez de usar SQL direto, vamos usar o método update do Prisma
        // que lida corretamente com os tipos enum
        try {
          // Usando uma conversão de tipo para evitar erros de tipagem
          const updatedUser = await this.prisma.user.update({
            where: { id },
            data: updateData as any
          })
          
          console.log('Service - Usuário atualizado com sucesso:', updatedUser.id)
          return updatedUser
        } catch (error) {
          console.error('Service - Erro ao atualizar usuário:', error)
          throw error
        }
      } else {
        // Se não estiver atualizando active, usar o método normal
        delete updateData.active
        const updatedUser = await this.prisma.user.update({
          where: { id },
          data: updateData
        })
        
        console.log('Service - Usuário atualizado com sucesso:', updatedUser.id)
        return updatedUser
      }
    } catch (error) {
      console.error('Service - Erro ao atualizar usuário:', error)
      throw error
    }
  }

  async delete(id: string): Promise<void> {
    // Implementação anterior (exclusão física)
    // await this.prisma.user.delete({
    //   where: { id }
    // })
    
    // Nova implementação (exclusão lógica)
    await this.deactivateUser(id)
  }

  async deactivateUser(id: string): Promise<User> {
    // Verificar se é o último administrador ativo
    const user = await this.findById(id)
    
    if (user?.role === 'ADMIN') {
      // Verificar se é o último administrador ativo usando SQL direto
      const activeAdmins = await this.prisma.$queryRaw<Array<{ count: bigint }>>`
        SELECT COUNT(*) as count FROM "User" 
        WHERE role = 'ADMIN' AND active = true AND id != ${id}
      `
      const activeAdminCount = Number(activeAdmins[0]?.count || 0)
      
      if (activeAdminCount === 0) {
        throw new Error('Não é possível desativar o último administrador ativo do sistema')
      }
    }
    
    // Usar o método update do Prisma com conversão de tipo
    return this.prisma.user.update({
      where: { id },
      data: { 
        active: false,
        updatedAt: new Date()
      } as any
    })
  }

  async reactivateUser(id: string): Promise<User> {
    // Usar o método update do Prisma com conversão de tipo
    return this.prisma.user.update({
      where: { id },
      data: { 
        active: true,
        updatedAt: new Date()
      } as any
    })
  }
} 
import { useEffect, useRef, useState, useCallback } from 'react';
import { useSystemNotifications } from './useNotifications';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

interface UseWebSocketOptions {
  url?: string;
  protocols?: string | string[];
  onOpen?: (event: Event) => void;
  onClose?: (event: CloseEvent) => void;
  onError?: (event: Event) => void;
  onMessage?: (message: WebSocketMessage) => void;
  reconnectAttempts?: number;
  reconnectInterval?: number;
  enabled?: boolean;
}

interface UseWebSocketReturn {
  socket: WebSocket | null;
  connectionState: 'connecting' | 'connected' | 'disconnected' | 'error';
  sendMessage: (message: any) => void;
  reconnect: () => void;
  disconnect: () => void;
}

export const useWebSocket = (options: UseWebSocketOptions = {}): UseWebSocketReturn => {
  const {
    url = `ws://${window.location.host}/ws`,
    protocols,
    onOpen,
    onClose,
    onError,
    onMessage,
    reconnectAttempts = 5,
    reconnectInterval = 3000,
    enabled = true
  } = options;

  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [connectionState, setConnectionState] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectCountRef = useRef(0);
  const shouldReconnectRef = useRef(true);

  const connect = useCallback(() => {
    if (!enabled) return;

    try {
      setConnectionState('connecting');
      const ws = new WebSocket(url, protocols);

      ws.onopen = (event) => {
        setConnectionState('connected');
        reconnectCountRef.current = 0;
        setSocket(ws);
        onOpen?.(event);
      };

      ws.onclose = (event) => {
        setConnectionState('disconnected');
        setSocket(null);
        onClose?.(event);

        // Tentar reconectar se não foi fechado intencionalmente
        if (shouldReconnectRef.current && reconnectCountRef.current < reconnectAttempts) {
          reconnectCountRef.current++;
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        }
      };

      ws.onerror = (event) => {
        setConnectionState('error');
        onError?.(event);
      };

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          onMessage?.(message);
        } catch (error) {
          console.error('Erro ao parsear mensagem WebSocket:', error);
        }
      };

    } catch (error) {
      setConnectionState('error');
      console.error('Erro ao conectar WebSocket:', error);
    }
  }, [url, protocols, enabled, onOpen, onClose, onError, onMessage, reconnectAttempts, reconnectInterval]);

  const disconnect = useCallback(() => {
    shouldReconnectRef.current = false;
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    if (socket) {
      socket.close();
    }
  }, [socket]);

  const reconnect = useCallback(() => {
    disconnect();
    shouldReconnectRef.current = true;
    reconnectCountRef.current = 0;
    setTimeout(connect, 100);
  }, [disconnect, connect]);

  const sendMessage = useCallback((message: any) => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket não está conectado');
    }
  }, [socket]);

  useEffect(() => {
    if (enabled) {
      connect();
    }

    return () => {
      shouldReconnectRef.current = false;
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (socket) {
        socket.close();
      }
    };
  }, [enabled, connect]);

  return {
    socket,
    connectionState,
    sendMessage,
    reconnect,
    disconnect
  };
};

// Hook específico para notificações do sistema
export const useSystemWebSocket = () => {
  const {
    notifyCommandExecuted,
    notifyServerConnected,
    notifyServerDisconnected,
    notifyAlert,
    notifyBackupCompleted
  } = useSystemNotifications();

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'command_executed':
        notifyCommandExecuted(
          message.data.serverName,
          message.data.command,
          message.data.success
        );
        break;

      case 'server_connected':
        notifyServerConnected(message.data.serverName);
        break;

      case 'server_disconnected':
        notifyServerDisconnected(message.data.serverName);
        break;

      case 'alert_triggered':
        notifyAlert(message.data.alertName, message.data.severity);
        break;

      case 'backup_completed':
        notifyBackupCompleted(message.data.success);
        break;

      default:
        console.log('Mensagem WebSocket não tratada:', message);
    }
  }, [
    notifyCommandExecuted,
    notifyServerConnected,
    notifyServerDisconnected,
    notifyAlert,
    notifyBackupCompleted
  ]);

  return useWebSocket({
    onMessage: handleMessage,
    reconnectAttempts: 10,
    reconnectInterval: 5000
  });
};

// Hook para monitoramento de status em tempo real
export const useRealTimeStatus = () => {
  const [systemStatus, setSystemStatus] = useState({
    serversOnline: 0,
    totalServers: 0,
    activeAlerts: 0,
    lastUpdate: new Date()
  });

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'system_status':
        setSystemStatus({
          ...message.data,
          lastUpdate: new Date(message.data.timestamp)
        });
        break;

      case 'server_status_change':
        setSystemStatus(prev => ({
          ...prev,
          serversOnline: message.data.online ? prev.serversOnline + 1 : prev.serversOnline - 1,
          lastUpdate: new Date()
        }));
        break;

      case 'alert_status_change':
        setSystemStatus(prev => ({
          ...prev,
          activeAlerts: message.data.active ? prev.activeAlerts + 1 : prev.activeAlerts - 1,
          lastUpdate: new Date()
        }));
        break;
    }
  }, []);

  const { connectionState, sendMessage } = useWebSocket({
    onMessage: handleMessage
  });

  // Solicitar status inicial quando conectar
  useEffect(() => {
    if (connectionState === 'connected') {
      sendMessage({ type: 'request_system_status' });
    }
  }, [connectionState, sendMessage]);

  return {
    systemStatus,
    connectionState,
    isConnected: connectionState === 'connected'
  };
};

export default useWebSocket;

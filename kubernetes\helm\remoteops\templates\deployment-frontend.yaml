{{- if .Values.frontend.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "remoteops.fullname" . }}-frontend
  labels:
    {{- include "remoteops.labels" . | nindent 4 }}
    app.kubernetes.io/component: frontend
spec:
  {{- if not .Values.frontend.autoscaling.enabled }}
  replicas: {{ .Values.frontend.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "remoteops.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: frontend
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      labels:
        {{- include "remoteops.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: frontend
    spec:
      {{- with .Values.image.pullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "remoteops.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: frontend
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.registry }}/{{ .Values.frontend.image.repository }}:{{ .Values.frontend.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.frontend.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.frontend.service.targetPort }}
              protocol: TCP
          env:
            - name: REACT_APP_API_URL
              value: {{ .Values.frontend.env.REACT_APP_API_URL | default (printf "https://%s/api" (index .Values.ingress.hosts 0).host) | quote }}
            - name: REACT_APP_PYTHON_API_URL
              value: {{ .Values.frontend.env.REACT_APP_PYTHON_API_URL | default (printf "https://%s/python-api" (index .Values.ingress.hosts 0).host) | quote }}
            {{- range $key, $value := .Values.frontend.env }}
            {{- if not (has $key (list "REACT_APP_API_URL" "REACT_APP_PYTHON_API_URL")) }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          livenessProbe:
            {{- toYaml .Values.frontend.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.frontend.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.frontend.resources | nindent 12 }}
          volumeMounts:
            - name: config
              mountPath: /app/config
              readOnly: true
      volumes:
        - name: config
          configMap:
            name: {{ include "remoteops.fullname" . }}-config
      {{- with .Values.frontend.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.frontend.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.frontend.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}

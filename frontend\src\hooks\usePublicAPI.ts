import { useState, useEffect, useCallback } from 'react';

export interface APIKey {
  id: string;
  name: string;
  key: string;
  permissions: APIPermission[];
  createdAt: Date;
  lastUsed?: Date;
  expiresAt?: Date;
  isActive: boolean;
  rateLimit: {
    requests: number;
    window: number; // em segundos
  };
  ipWhitelist?: string[];
  description?: string;
}

export interface APIPermission {
  resource: 'servers' | 'users' | 'commands' | 'templates' | 'groups' | 'monitoring' | 'backups' | 'logs';
  actions: ('read' | 'write' | 'delete' | 'execute')[];
}

export interface APIUsage {
  keyId: string;
  endpoint: string;
  method: string;
  timestamp: Date;
  responseTime: number;
  statusCode: number;
  ipAddress: string;
  userAgent?: string;
}

export interface APIStats {
  totalKeys: number;
  activeKeys: number;
  totalRequests: number;
  requestsToday: number;
  averageResponseTime: number;
  errorRate: number;
  topEndpoints: Array<{ endpoint: string; count: number }>;
  usageByKey: Array<{ keyId: string; name: string; requests: number }>;
}

export interface WebhookConfig {
  id: string;
  name: string;
  url: string;
  events: string[];
  isActive: boolean;
  secret?: string;
  headers?: Record<string, string>;
  retryPolicy: {
    maxRetries: number;
    backoffMultiplier: number;
  };
  createdAt: Date;
  lastTriggered?: Date;
}

interface UsePublicAPIReturn {
  apiKeys: APIKey[];
  webhooks: WebhookConfig[];
  usage: APIUsage[];
  stats: APIStats;
  isLoading: boolean;
  createAPIKey: (data: Omit<APIKey, 'id' | 'key' | 'createdAt'>) => Promise<APIKey>;
  updateAPIKey: (keyId: string, updates: Partial<APIKey>) => Promise<boolean>;
  deleteAPIKey: (keyId: string) => Promise<boolean>;
  regenerateAPIKey: (keyId: string) => Promise<string>;
  createWebhook: (data: Omit<WebhookConfig, 'id' | 'createdAt'>) => Promise<WebhookConfig>;
  updateWebhook: (webhookId: string, updates: Partial<WebhookConfig>) => Promise<boolean>;
  deleteWebhook: (webhookId: string) => Promise<boolean>;
  testWebhook: (webhookId: string) => Promise<boolean>;
  getAPIDocumentation: () => Promise<any>;
  exportAPIKeys: () => Promise<void>;
  getUsageReport: (keyId?: string, days?: number) => Promise<APIUsage[]>;
}

const STORAGE_KEY = 'sem-fronteiras-api-keys';
const WEBHOOKS_KEY = 'sem-fronteiras-webhooks';
const USAGE_KEY = 'sem-fronteiras-api-usage';

const DEFAULT_PERMISSIONS: APIPermission[] = [
  { resource: 'servers', actions: ['read'] },
  { resource: 'users', actions: ['read'] },
  { resource: 'commands', actions: ['read', 'execute'] },
  { resource: 'templates', actions: ['read'] },
  { resource: 'groups', actions: ['read'] },
  { resource: 'monitoring', actions: ['read'] },
  { resource: 'backups', actions: ['read'] },
  { resource: 'logs', actions: ['read'] }
];

export const usePublicAPI = (): UsePublicAPIReturn => {
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [webhooks, setWebhooks] = useState<WebhookConfig[]>([]);
  const [usage, setUsage] = useState<APIUsage[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Carregar dados do localStorage
  useEffect(() => {
    try {
      const storedKeys = localStorage.getItem(STORAGE_KEY);
      const storedWebhooks = localStorage.getItem(WEBHOOKS_KEY);
      const storedUsage = localStorage.getItem(USAGE_KEY);
      
      if (storedKeys) {
        const parsed = JSON.parse(storedKeys);
        const keysWithDates = parsed.map((key: any) => ({
          ...key,
          createdAt: new Date(key.createdAt),
          lastUsed: key.lastUsed ? new Date(key.lastUsed) : undefined,
          expiresAt: key.expiresAt ? new Date(key.expiresAt) : undefined
        }));
        setApiKeys(keysWithDates);
      } else {
        // Gerar dados mock para demonstração
        generateMockData();
      }
      
      if (storedWebhooks) {
        const parsed = JSON.parse(storedWebhooks);
        const webhooksWithDates = parsed.map((webhook: any) => ({
          ...webhook,
          createdAt: new Date(webhook.createdAt),
          lastTriggered: webhook.lastTriggered ? new Date(webhook.lastTriggered) : undefined
        }));
        setWebhooks(webhooksWithDates);
      }
      
      if (storedUsage) {
        const parsed = JSON.parse(storedUsage);
        const usageWithDates = parsed.map((usage: any) => ({
          ...usage,
          timestamp: new Date(usage.timestamp)
        }));
        setUsage(usageWithDates);
      }
    } catch (error) {
      console.error('Erro ao carregar dados da API:', error);
    }
  }, []);

  // Salvar no localStorage
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(apiKeys));
    } catch (error) {
      console.error('Erro ao salvar chaves API:', error);
    }
  }, [apiKeys]);

  useEffect(() => {
    try {
      localStorage.setItem(WEBHOOKS_KEY, JSON.stringify(webhooks));
    } catch (error) {
      console.error('Erro ao salvar webhooks:', error);
    }
  }, [webhooks]);

  useEffect(() => {
    try {
      localStorage.setItem(USAGE_KEY, JSON.stringify(usage));
    } catch (error) {
      console.error('Erro ao salvar uso da API:', error);
    }
  }, [usage]);

  const generateAPIKey = (): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = 'sf_';
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const generateMockData = useCallback(() => {
    const mockKeys: APIKey[] = [
      {
        id: '1',
        name: 'Aplicação Principal',
        key: generateAPIKey(),
        permissions: DEFAULT_PERMISSIONS,
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000),
        isActive: true,
        rateLimit: { requests: 1000, window: 3600 },
        description: 'Chave principal para integração com sistema de monitoramento'
      },
      {
        id: '2',
        name: 'Dashboard Externo',
        key: generateAPIKey(),
        permissions: [
          { resource: 'monitoring', actions: ['read'] },
          { resource: 'servers', actions: ['read'] }
        ],
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
        lastUsed: new Date(Date.now() - 30 * 60 * 1000),
        isActive: true,
        rateLimit: { requests: 500, window: 3600 },
        ipWhitelist: ['*************', '*********'],
        description: 'Acesso somente leitura para dashboard externo'
      }
    ];

    const mockWebhooks: WebhookConfig[] = [
      {
        id: '1',
        name: 'Notificações Slack',
        url: '*****************************************************************************',
        events: ['server.down', 'alert.critical', 'backup.failed'],
        isActive: true,
        secret: 'webhook_secret_123',
        retryPolicy: { maxRetries: 3, backoffMultiplier: 2 },
        createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
        lastTriggered: new Date(Date.now() - 4 * 60 * 60 * 1000)
      }
    ];

    const mockUsage: APIUsage[] = [];
    const endpoints = ['/api/v1/servers', '/api/v1/monitoring/metrics', '/api/v1/commands/execute', '/api/v1/users'];
    
    for (let i = 0; i < 100; i++) {
      mockUsage.push({
        keyId: Math.random() > 0.7 ? '1' : '2',
        endpoint: endpoints[Math.floor(Math.random() * endpoints.length)],
        method: ['GET', 'POST', 'PUT'][Math.floor(Math.random() * 3)],
        timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
        responseTime: Math.floor(Math.random() * 1000) + 50,
        statusCode: Math.random() > 0.9 ? 500 : Math.random() > 0.95 ? 404 : 200,
        ipAddress: `192.168.1.${Math.floor(Math.random() * 254) + 1}`
      });
    }

    setApiKeys(mockKeys);
    setWebhooks(mockWebhooks);
    setUsage(mockUsage);
  }, []);

  const createAPIKey = useCallback(async (data: Omit<APIKey, 'id' | 'key' | 'createdAt'>): Promise<APIKey> => {
    setIsLoading(true);
    try {
      // Simular API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newKey: APIKey = {
        ...data,
        id: Date.now().toString(),
        key: generateAPIKey(),
        createdAt: new Date()
      };
      
      setApiKeys(prev => [newKey, ...prev]);
      return newKey;
    } catch (error) {
      console.error('Erro ao criar chave API:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateAPIKey = useCallback(async (keyId: string, updates: Partial<APIKey>): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setApiKeys(prev => prev.map(key => 
        key.id === keyId ? { ...key, ...updates } : key
      ));
      
      return true;
    } catch (error) {
      console.error('Erro ao atualizar chave API:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteAPIKey = useCallback(async (keyId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setApiKeys(prev => prev.filter(key => key.id !== keyId));
      return true;
    } catch (error) {
      console.error('Erro ao deletar chave API:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const regenerateAPIKey = useCallback(async (keyId: string): Promise<string> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newKey = generateAPIKey();
      setApiKeys(prev => prev.map(key => 
        key.id === keyId ? { ...key, key: newKey } : key
      ));
      
      return newKey;
    } catch (error) {
      console.error('Erro ao regenerar chave API:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createWebhook = useCallback(async (data: Omit<WebhookConfig, 'id' | 'createdAt'>): Promise<WebhookConfig> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newWebhook: WebhookConfig = {
        ...data,
        id: Date.now().toString(),
        createdAt: new Date()
      };
      
      setWebhooks(prev => [newWebhook, ...prev]);
      return newWebhook;
    } catch (error) {
      console.error('Erro ao criar webhook:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateWebhook = useCallback(async (webhookId: string, updates: Partial<WebhookConfig>): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setWebhooks(prev => prev.map(webhook => 
        webhook.id === webhookId ? { ...webhook, ...updates } : webhook
      ));
      
      return true;
    } catch (error) {
      console.error('Erro ao atualizar webhook:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteWebhook = useCallback(async (webhookId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setWebhooks(prev => prev.filter(webhook => webhook.id !== webhookId));
      return true;
    } catch (error) {
      console.error('Erro ao deletar webhook:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const testWebhook = useCallback(async (webhookId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simular teste de webhook
      const webhook = webhooks.find(w => w.id === webhookId);
      if (webhook) {
        setWebhooks(prev => prev.map(w => 
          w.id === webhookId ? { ...w, lastTriggered: new Date() } : w
        ));
      }
      
      return Math.random() > 0.2; // 80% de sucesso
    } catch (error) {
      console.error('Erro ao testar webhook:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [webhooks]);

  const getAPIDocumentation = useCallback(async (): Promise<any> => {
    // Retornar documentação da API em formato OpenAPI/Swagger
    return {
      openapi: '3.0.0',
      info: {
        title: 'REMOTEOPS API',
        version: '1.0.0',
        description: 'API pública para integração com o sistema REMOTEOPS'
      },
      servers: [
        { url: 'https://api.semfronteiras.com/v1', description: 'Produção' },
        { url: 'https://staging-api.semfronteiras.com/v1', description: 'Staging' }
      ],
      paths: {
        '/servers': {
          get: {
            summary: 'Listar servidores',
            security: [{ ApiKeyAuth: [] }],
            responses: {
              200: { description: 'Lista de servidores' }
            }
          }
        },
        '/commands/execute': {
          post: {
            summary: 'Executar comando',
            security: [{ ApiKeyAuth: [] }],
            requestBody: {
              required: true,
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      serverId: { type: 'string' },
                      command: { type: 'string' }
                    }
                  }
                }
              }
            }
          }
        }
      }
    };
  }, []);

  const exportAPIKeys = useCallback(async (): Promise<void> => {
    try {
      const exportData = {
        exportedAt: new Date().toISOString(),
        keys: apiKeys.map(key => ({
          ...key,
          key: '***HIDDEN***' // Não exportar a chave real por segurança
        }))
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `api-keys-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao exportar chaves API:', error);
      throw error;
    }
  }, [apiKeys]);

  const getUsageReport = useCallback(async (keyId?: string, days: number = 7): Promise<APIUsage[]> => {
    const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    
    return usage.filter(u => {
      const matchesKey = !keyId || u.keyId === keyId;
      const matchesDate = u.timestamp >= cutoffDate;
      return matchesKey && matchesDate;
    });
  }, [usage]);

  // Calcular estatísticas
  const stats: APIStats = {
    totalKeys: apiKeys.length,
    activeKeys: apiKeys.filter(k => k.isActive).length,
    totalRequests: usage.length,
    requestsToday: usage.filter(u => 
      u.timestamp >= new Date(Date.now() - 24 * 60 * 60 * 1000)
    ).length,
    averageResponseTime: usage.length > 0 
      ? usage.reduce((sum, u) => sum + u.responseTime, 0) / usage.length 
      : 0,
    errorRate: usage.length > 0 
      ? (usage.filter(u => u.statusCode >= 400).length / usage.length) * 100 
      : 0,
    topEndpoints: Object.entries(
      usage.reduce((acc, u) => {
        acc[u.endpoint] = (acc[u.endpoint] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    )
      .map(([endpoint, count]) => ({ endpoint, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5),
    usageByKey: Object.entries(
      usage.reduce((acc, u) => {
        acc[u.keyId] = (acc[u.keyId] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    )
      .map(([keyId, requests]) => {
        const key = apiKeys.find(k => k.id === keyId);
        return { keyId, name: key?.name || 'Chave Desconhecida', requests };
      })
      .sort((a, b) => b.requests - a.requests)
  };

  return {
    apiKeys,
    webhooks,
    usage,
    stats,
    isLoading,
    createAPIKey,
    updateAPIKey,
    deleteAPIKey,
    regenerateAPIKey,
    createWebhook,
    updateWebhook,
    deleteWebhook,
    testWebhook,
    getAPIDocumentation,
    exportAPIKeys,
    getUsageReport
  };
};

export default usePublicAPI;

// Script para resetar a senha de um usuário via CLI
// Uso: node scripts/reset-password.js --email <EMAIL> --password novaSenha
//   ou: node scripts/reset-password.js --id 123e4567-e89b-12d3-a456-426614174000 --password novaSenha

require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const prisma = new PrismaClient();

// Função para analisar os argumentos da linha de comando
function parseArgs() {
  const args = process.argv.slice(2);
  const params = {};

  for (let i = 0; i < args.length; i += 2) {
    if (args[i].startsWith('--')) {
      const key = args[i].slice(2);
      const value = args[i + 1];
      params[key] = value;
    }
  }

  return params;
}

// Função principal
async function resetPassword() {
  try {
    console.log('=== FERRAMENTA DE RESET DE SENHA ===');
    
    // Obter parâmetros da linha de comando
    const params = parseArgs();
    
    if (!params.password) {
      console.error('Erro: Senha não fornecida');
      console.log('Uso: node scripts/reset-password.js --email <EMAIL> --password novaSenha');
      console.log('  ou: node scripts/reset-password.js --id 123e4567-e89b-12d3-a456-426614174000 --password novaSenha');
      process.exit(1);
    }
    
    // Verificar se temos email ou id
    if (!params.email && !params.id) {
      console.error('Erro: É necessário fornecer um email ou ID de usuário');
      console.log('Uso: node scripts/reset-password.js --email <EMAIL> --password novaSenha');
      console.log('  ou: node scripts/reset-password.js --id 123e4567-e89b-12d3-a456-426614174000 --password novaSenha');
      process.exit(1);
    }
    
    // Buscar o usuário
    let user;
    if (params.email) {
      console.log(`Buscando usuário com email: ${params.email}`);
      user = await prisma.user.findUnique({
        where: { email: params.email }
      });
    } else {
      console.log(`Buscando usuário com ID: ${params.id}`);
      user = await prisma.user.findUnique({
        where: { id: params.id }
      });
    }
    
    // Verificar se o usuário foi encontrado
    if (!user) {
      console.error('Erro: Usuário não encontrado');
      process.exit(1);
    }
    
    console.log(`Usuário encontrado: ${user.name} (${user.email})`);
    
    // Verificar se o usuário está ativo
    if (!user.active) {
      console.log('AVISO: Este usuário está desativado. Deseja reativá-lo? (s/N)');
      
      // Simular uma pergunta simples (em produção, use uma biblioteca como 'readline')
      const stdin = process.openStdin();
      stdin.addListener('data', async (d) => {
        const input = d.toString().trim().toLowerCase();
        
        if (input === 's' || input === 'sim') {
          console.log('Reativando usuário...');
          await prisma.user.update({
            where: { id: user.id },
            data: { active: true }
          });
          console.log('Usuário reativado com sucesso!');
        }
        
        // Continuar com a alteração de senha
        await updatePassword(user.id, params.password);
        stdin.end();
      });
    } else {
      // Usuário já está ativo, apenas atualizar a senha
      await updatePassword(user.id, params.password);
    }
  } catch (error) {
    console.error('Erro ao resetar senha:', error);
    process.exit(1);
  }
}

// Função para atualizar a senha
async function updatePassword(userId, newPassword) {
  try {
    // Gerar hash da nova senha
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    // Atualizar a senha no banco de dados
    await prisma.user.update({
      where: { id: userId },
      data: { 
        password: hashedPassword,
        updatedAt: new Date()
      }
    });
    
    console.log('Senha atualizada com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('Erro ao atualizar senha:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar a função principal
resetPassword();

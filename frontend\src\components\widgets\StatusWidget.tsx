import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  LinearProgress,
  Chip,
  Card,
  CardContent,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Computer as ServerIcon,
  Warning as AlertIcon,
  CheckCircle as HealthyIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { DashboardWidget } from '../../hooks/useDashboard';
import { useSystemHealth } from '../RealTimeStatus';

interface StatusWidgetProps {
  widget: DashboardWidget;
}

const StatusWidget: React.FC<StatusWidgetProps> = ({ widget }) => {
  const {
    serversOnline,
    totalServers,
    activeAlerts,
    isConnected,
    healthPercentage,
    healthStatus,
    hasAlerts,
    allServersOnline
  } = useSystemHealth();

  const [lastRefresh, setLastRefresh] = useState(new Date());

  useEffect(() => {
    const interval = setInterval(() => {
      setLastRefresh(new Date());
    }, widget.refreshInterval || 30000);

    return () => clearInterval(interval);
  }, [widget.refreshInterval]);

  const getHealthColor = () => {
    if (healthStatus === 'healthy') return 'success';
    if (healthStatus === 'warning') return 'warning';
    return 'error';
  };

  const getHealthIcon = () => {
    if (healthStatus === 'healthy') return <HealthyIcon color="success" />;
    if (healthStatus === 'warning') return <AlertIcon color="warning" />;
    return <ErrorIcon color="error" />;
  };

  const getConnectionStatus = () => {
    if (isConnected) {
      return { text: 'Conectado', color: 'success' as const };
    }
    return { text: 'Desconectado', color: 'error' as const };
  };

  const connectionStatus = getConnectionStatus();

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Cabeçalho com status de conexão */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
        <Chip
          label={connectionStatus.text}
          color={connectionStatus.color}
          size="small"
          variant="outlined"
        />
        <Tooltip title="Atualizar">
          <IconButton size="small" onClick={() => setLastRefresh(new Date())}>
            <RefreshIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Métricas principais */}
      <Grid container spacing={2} sx={{ flexGrow: 1 }}>
        {/* Saúde dos Servidores */}
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                {getHealthIcon()}
                <Typography variant="subtitle2">
                  Saúde dos Servidores
                </Typography>
              </Box>
              
              <LinearProgress
                variant="determinate"
                value={healthPercentage}
                color={getHealthColor() as any}
                sx={{ mb: 1, height: 8, borderRadius: 4 }}
              />
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="body2" color="text.secondary">
                  {serversOnline}/{totalServers} online
                </Typography>
                <Typography variant="body2" fontWeight="bold" color={`${getHealthColor()}.main`}>
                  {Math.round(healthPercentage)}%
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Servidores Online */}
        <Grid item xs={6}>
          <Card variant="outlined">
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 }, textAlign: 'center' }}>
              <ServerIcon color="primary" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h4" color="primary.main">
                {serversOnline}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Servidores Online
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Alertas Ativos */}
        <Grid item xs={6}>
          <Card variant="outlined">
            <CardContent sx={{ p: 2, '&:last-child': { pb: 2 }, textAlign: 'center' }}>
              <AlertIcon 
                color={hasAlerts ? 'warning' : 'action'} 
                sx={{ fontSize: 32, mb: 1 }} 
              />
              <Typography 
                variant="h4" 
                color={hasAlerts ? 'warning.main' : 'text.secondary'}
              >
                {activeAlerts}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Alertas Ativos
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Status Geral */}
        <Grid item xs={12}>
          <Box sx={{ textAlign: 'center', py: 1 }}>
            {allServersOnline && !hasAlerts ? (
              <Chip
                icon={<HealthyIcon />}
                label="Sistema Saudável"
                color="success"
                variant="filled"
              />
            ) : hasAlerts ? (
              <Chip
                icon={<AlertIcon />}
                label="Atenção Necessária"
                color="warning"
                variant="filled"
              />
            ) : (
              <Chip
                icon={<ErrorIcon />}
                label="Problemas Detectados"
                color="error"
                variant="filled"
              />
            )}
          </Box>
        </Grid>
      </Grid>

      {/* Rodapé com última atualização */}
      <Box sx={{ mt: 'auto', pt: 1, borderTop: 1, borderColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          Última atualização: {lastRefresh.toLocaleTimeString()}
        </Typography>
      </Box>
    </Box>
  );
};

export default StatusWidget;

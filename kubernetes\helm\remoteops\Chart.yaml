apiVersion: v2
name: remoteops
description: A Helm chart for RemoteOps - Modern SSH Infrastructure Management Platform
type: application
version: 1.0.0
appVersion: "1.0.0"
home: https://remoteops.com
sources:
  - https://github.com/remoteops/remoteops
maintainers:
  - name: RemoteOps Team
    email: <EMAIL>
keywords:
  - ssh
  - infrastructure
  - management
  - devops
  - saas
  - multi-tenant
annotations:
  category: Infrastructure
  licenses: MIT
dependencies:
  - name: postgresql
    version: 12.1.9
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
  - name: redis
    version: 17.3.7
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled
  - name: nginx-ingress
    version: 4.4.0
    repository: https://kubernetes.github.io/ingress-nginx
    condition: ingress.enabled
  - name: prometheus
    version: 15.16.1
    repository: https://prometheus-community.github.io/helm-charts
    condition: monitoring.prometheus.enabled
  - name: grafana
    version: 6.44.11
    repository: https://grafana.github.io/helm-charts
    condition: monitoring.grafana.enabled

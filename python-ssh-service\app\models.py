from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
from enum import Enum

class DeviceType(str, Enum):
    LINUX = "linux"
    WINDOWS = "windows"
    HUAWEI = "huawei"
    HUAWEI_VRP = "huawei_vrp"
    NOKIA = "nokia"
    MIKROTIK = "mikrotik"
    CISCO = "cisco"
    GENERIC = "generic"

class SSHCommandRequest(BaseModel):
    host: str = Field(..., description="Endereço IP ou hostname do servidor")
    port: int = Field(22, description="Porta SSH")
    username: str = Field(..., description="Nome de usuário")
    password: Optional[str] = Field(None, description="Senha de acesso")
    private_key: Optional[str] = Field(None, description="Chave privada SSH")
    command: str = Field(..., description="Comando a ser executado")
    device_type: DeviceType = Field(DeviceType.LINUX, description="Tipo do dispositivo")
    timeout: int = Field(60, description="Timeout em segundos")
    
    @validator('timeout')
    def validate_timeout(cls, v):
        if v < 5:
            return 5
        if v > 300:
            return 300
        return v
    
    @validator('port')
    def validate_port(cls, v):
        if v < 1 or v > 65535:
            raise ValueError('Porta deve estar entre 1 e 65535')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "host": "***********",
                "port": 22,
                "username": "admin",
                "password": "password123",
                "command": "display version",
                "device_type": "huawei",
                "timeout": 90
            }
        }

class CommandResult(BaseModel):
    stdout: Optional[str] = Field(None, description="Saída padrão do comando")
    stderr: Optional[str] = Field(None, description="Saída de erro do comando")
    code: Optional[int] = Field(None, description="Código de retorno")
    execution_time: Optional[float] = Field(None, description="Tempo de execução em segundos")
    device_info: Optional[Dict[str, Any]] = Field(None, description="Informações do dispositivo")
    
    class Config:
        schema_extra = {
            "example": {
                "stdout": "VRP (R) software, Version 8.180\nHuawei Versatile Routing Platform Software",
                "stderr": "",
                "code": 0,
                "execution_time": 2.5,
                "device_info": {
                    "device_type": "huawei",
                    "detected_os": "VRP"
                }
            }
        }

class HealthCheck(BaseModel):
    status: str
    timestamp: str
    version: str
    active_connections: int
    
class ConnectionInfo(BaseModel):
    host: str
    port: int
    device_type: DeviceType
    connected_at: str
    last_activity: str
    
class ServiceStats(BaseModel):
    total_commands: int
    successful_commands: int
    failed_commands: int
    average_execution_time: float
    active_connections: int
    uptime: str

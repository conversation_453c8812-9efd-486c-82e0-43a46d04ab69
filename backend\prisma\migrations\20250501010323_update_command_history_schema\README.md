# Correção da Migração

Este arquivo de migração foi modificado para resolver um problema no ambiente de produção.

## Alterações Realizadas

- Remo<PERSON><PERSON> as instruções `DROP INDEX` que tentavam excluir índices inexistentes:
  - `CommandHistory_createdAt_idx`
  - `CommandHistory_serverId_createdAt_idx`
  - `Server_active_idx`

## Motivo da Alteração

No ambiente de produção, esses índices não existiam, o que causava falha na migração. A remoção dessas linhas permite que a migração seja aplicada com sucesso, mantendo as alterações nas tabelas que são necessárias.

## Data da Correção

30/04/2025

## Observações

Para evitar problemas semelhantes no futuro, considere:

1. Sincronizar os ambientes de desenvolvimento e produção antes de criar novas migrações
2. Usar comandos SQL condicionais para verificar a existência de objetos antes de tentar modificá-los
3. Testar as migrações em um ambiente de staging antes de aplicá-las em produção

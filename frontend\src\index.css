@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .responsive-container {
    @apply w-full px-4 sm:px-6 lg:px-8 mx-auto;
  }
  
  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
  }
  
  .responsive-card {
    @apply p-4 sm:p-6 bg-white rounded-lg shadow-sm border border-gray-200;
  }
  
  .responsive-button {
    @apply w-full sm:w-auto flex items-center justify-center gap-2 px-4 py-2 rounded-md;
  }
  
  .responsive-table-container {
    @apply overflow-x-auto -mx-4 sm:mx-0 shadow-sm ring-1 ring-black ring-opacity-5 rounded-lg;
  }
  
  .responsive-heading {
    @apply text-xl sm:text-2xl font-bold;
  }
  
  .responsive-text {
    @apply text-sm sm:text-base;
  }
  
  .responsive-flex {
    @apply flex flex-col sm:flex-row sm:items-center gap-4;
  }
} 
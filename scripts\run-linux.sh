#!/bin/bash

# RemoteOps Linux Automation Script
# Universal setup and management for Linux distributions

set -e

# Configuration
PROJECT_NAME="remoteops"
NODE_VERSION="18"
PYTHON_VERSION="3.11"
PROJECT_PATH="/opt/remoteops"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${PURPLE}[DEBUG]${NC} $1"
}

# Detect Linux distribution
detect_distro() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        DISTRO=$ID
        VERSION=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        DISTRO=$(lsb_release -si | tr '[:upper:]' '[:lower:]')
        VERSION=$(lsb_release -sr)
    elif [ -f /etc/redhat-release ]; then
        DISTRO="rhel"
        VERSION=$(grep -oE '[0-9]+\.[0-9]+' /etc/redhat-release | head -1)
    else
        DISTRO="unknown"
        VERSION="unknown"
    fi
    
    log_info "Detected distribution: $DISTRO $VERSION"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "Running as root. This is not recommended for development."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Install packages based on distribution
install_package() {
    local package=$1
    
    case $DISTRO in
        ubuntu|debian)
            sudo apt-get update -qq
            sudo apt-get install -y $package
            ;;
        fedora)
            sudo dnf install -y $package
            ;;
        centos|rhel)
            sudo yum install -y $package
            ;;
        arch|manjaro)
            sudo pacman -S --noconfirm $package
            ;;
        opensuse*)
            sudo zypper install -y $package
            ;;
        *)
            log_error "Unsupported distribution: $DISTRO"
            exit 1
            ;;
    esac
}

# Install Node.js
install_nodejs() {
    if command_exists node; then
        local current_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$current_version" -ge "$NODE_VERSION" ]; then
            log_success "Node.js $current_version is already installed"
            return
        fi
    fi
    
    log_info "Installing Node.js $NODE_VERSION..."
    
    # Install Node.js using NodeSource repository
    curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
    
    case $DISTRO in
        ubuntu|debian)
            sudo apt-get install -y nodejs
            ;;
        fedora|centos|rhel)
            curl -fsSL https://rpm.nodesource.com/setup_${NODE_VERSION}.x | sudo bash -
            sudo yum install -y nodejs
            ;;
        *)
            # Fallback to package manager
            install_package nodejs
            install_package npm
            ;;
    esac
    
    # Install yarn globally
    sudo npm install -g yarn
    
    log_success "Node.js installed successfully"
}

# Install Python
install_python() {
    if command_exists python3; then
        local current_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        if [ "$(echo "$current_version >= $PYTHON_VERSION" | bc -l)" -eq 1 ]; then
            log_success "Python $current_version is already installed"
            return
        fi
    fi
    
    log_info "Installing Python $PYTHON_VERSION..."
    
    case $DISTRO in
        ubuntu|debian)
            install_package python3
            install_package python3-pip
            install_package python3-venv
            ;;
        fedora)
            install_package python3
            install_package python3-pip
            ;;
        centos|rhel)
            install_package python3
            install_package python3-pip
            ;;
        arch|manjaro)
            install_package python
            install_package python-pip
            ;;
        *)
            install_package python3
            install_package python3-pip
            ;;
    esac
    
    log_success "Python installed successfully"
}

# Install PostgreSQL
install_postgresql() {
    if command_exists psql; then
        log_success "PostgreSQL is already installed"
        return
    fi
    
    log_info "Installing PostgreSQL..."
    
    case $DISTRO in
        ubuntu|debian)
            install_package postgresql
            install_package postgresql-contrib
            ;;
        fedora)
            install_package postgresql-server
            install_package postgresql-contrib
            sudo postgresql-setup --initdb
            ;;
        centos|rhel)
            install_package postgresql-server
            install_package postgresql-contrib
            sudo postgresql-setup initdb
            ;;
        arch|manjaro)
            install_package postgresql
            sudo -u postgres initdb -D /var/lib/postgres/data
            ;;
        *)
            install_package postgresql
            ;;
    esac
    
    # Start and enable PostgreSQL
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    
    # Set password for postgres user
    sudo -u postgres psql -c "ALTER USER postgres PASSWORD 'password';"
    
    log_success "PostgreSQL installed and configured"
}

# Install Redis
install_redis() {
    if command_exists redis-server; then
        log_success "Redis is already installed"
        return
    fi
    
    log_info "Installing Redis..."
    
    case $DISTRO in
        ubuntu|debian)
            install_package redis-server
            ;;
        fedora|centos|rhel)
            install_package redis
            ;;
        arch|manjaro)
            install_package redis
            ;;
        *)
            install_package redis
            ;;
    esac
    
    # Start and enable Redis
    sudo systemctl start redis
    sudo systemctl enable redis
    
    log_success "Redis installed and started"
}

# Install Git
install_git() {
    if command_exists git; then
        log_success "Git is already installed"
        return
    fi
    
    log_info "Installing Git..."
    install_package git
    log_success "Git installed successfully"
}

# Install Docker
install_docker() {
    if command_exists docker; then
        log_success "Docker is already installed"
        return
    fi
    
    log_info "Installing Docker..."
    
    case $DISTRO in
        ubuntu|debian)
            # Install Docker using official repository
            curl -fsSL https://download.docker.com/linux/$DISTRO/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
            echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/$DISTRO $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
            sudo apt-get update
            sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            ;;
        fedora)
            sudo dnf install -y docker docker-compose
            ;;
        centos|rhel)
            sudo yum install -y docker docker-compose
            ;;
        arch|manjaro)
            sudo pacman -S --noconfirm docker docker-compose
            ;;
        *)
            install_package docker
            install_package docker-compose
            ;;
    esac
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # Add current user to docker group
    sudo usermod -aG docker $USER
    
    log_success "Docker installed successfully"
    log_warning "Please log out and log back in for Docker group changes to take effect"
}

# Install prerequisites
install_prerequisites() {
    log_info "Installing prerequisites..."
    
    # Update package manager
    case $DISTRO in
        ubuntu|debian)
            sudo apt-get update
            install_package curl
            install_package wget
            install_package gnupg
            install_package lsb-release
            install_package bc
            ;;
        fedora)
            sudo dnf update -y
            install_package curl
            install_package wget
            install_package bc
            ;;
        centos|rhel)
            sudo yum update -y
            install_package curl
            install_package wget
            install_package bc
            ;;
        arch|manjaro)
            sudo pacman -Syu --noconfirm
            install_package curl
            install_package wget
            install_package bc
            ;;
    esac
    
    install_git
    install_nodejs
    install_python
    install_postgresql
    install_redis
    
    log_success "Prerequisites installed successfully"
}

# Setup project
setup_project() {
    log_info "Setting up RemoteOps project..."
    
    # Create project directory
    if [ ! -d "$PROJECT_PATH" ]; then
        sudo mkdir -p "$PROJECT_PATH"
        sudo chown $USER:$USER "$PROJECT_PATH"
        log_success "Created project directory: $PROJECT_PATH"
    fi
    
    cd "$PROJECT_PATH"
    
    # Copy project files if not exists
    if [ ! -f "package.json" ]; then
        log_info "Copying project files..."
        cp -r "$SCRIPT_DIR/../"* . 2>/dev/null || true
        log_success "Project files copied"
    fi
    
    # Install dependencies
    install_dependencies
    
    # Setup environment
    setup_environment
    
    # Setup database
    setup_database
}

# Install dependencies
install_dependencies() {
    log_info "Installing project dependencies..."
    
    # Backend dependencies
    if [ -f "backend/package.json" ]; then
        cd backend
        log_info "Installing backend dependencies..."
        npm install
        cd ..
    fi
    
    # Frontend dependencies
    if [ -f "frontend/package.json" ]; then
        cd frontend
        log_info "Installing frontend dependencies..."
        npm install
        cd ..
    fi
    
    # Python dependencies
    if [ -f "python-microservice/requirements.txt" ]; then
        cd python-microservice
        log_info "Installing Python dependencies..."
        python3 -m pip install --user -r requirements.txt
        cd ..
    fi
    
    log_success "Dependencies installed successfully"
}

# Setup environment
setup_environment() {
    log_info "Setting up environment..."
    
    if [ ! -f ".env" ]; then
        log_info "Creating environment file..."
        cat > .env << EOF
NODE_ENV=development
JWT_SECRET=$(openssl rand -base64 32)
DATABASE_URL=postgresql://postgres:password@localhost:5432/remoteops
REDIS_URL=redis://localhost:6379
PORT=3000
FRONTEND_URL=http://localhost:3001
PYTHON_MICROSERVICE_URL=http://localhost:8000
EOF
        log_success "Environment file created"
    fi
}

# Setup database
setup_database() {
    log_info "Setting up database..."
    
    # Ensure PostgreSQL is running
    sudo systemctl start postgresql
    
    # Create database
    sudo -u postgres createdb remoteops 2>/dev/null || log_warning "Database might already exist"
    
    # Run migrations
    if [ -f "backend/prisma/schema.prisma" ]; then
        cd backend
        log_info "Running database migrations..."
        npx prisma migrate dev --name init
        cd ..
    fi
    
    # Ensure Redis is running
    sudo systemctl start redis
    
    log_success "Database setup completed"
}

# Start applications
start_applications() {
    log_info "Starting RemoteOps applications..."
    
    cd "$PROJECT_PATH"
    
    # Create logs directory
    mkdir -p logs
    
    # Start backend
    log_info "Starting backend server..."
    cd backend
    nohup npm run dev > ../logs/backend.log 2>&1 &
    echo $! > ../logs/backend.pid
    cd ..
    
    sleep 5
    
    # Start frontend
    log_info "Starting frontend server..."
    cd frontend
    nohup npm run dev > ../logs/frontend.log 2>&1 &
    echo $! > ../logs/frontend.pid
    cd ..
    
    # Start Python microservice
    log_info "Starting Python microservice..."
    cd python-microservice
    nohup python3 main.py > ../logs/python.log 2>&1 &
    echo $! > ../logs/python.pid
    cd ..
    
    log_success "Applications started successfully"
    
    # Wait for services
    log_info "Waiting for services to be ready..."
    sleep 15
    
    # Show status
    show_status
}

# Stop applications
stop_applications() {
    log_info "Stopping RemoteOps applications..."
    
    cd "$PROJECT_PATH"
    
    # Stop processes using PID files
    for service in backend frontend python; do
        if [ -f "logs/${service}.pid" ]; then
            local pid=$(cat "logs/${service}.pid")
            if kill -0 "$pid" 2>/dev/null; then
                kill "$pid"
                log_success "Stopped $service (PID: $pid)"
            fi
            rm -f "logs/${service}.pid"
        fi
    done
    
    # Fallback: kill by process name
    pkill -f "npm run dev" 2>/dev/null || true
    pkill -f "python3 main.py" 2>/dev/null || true
    
    log_success "Applications stopped"
}

# Show application status
show_status() {
    log_info "Checking application status..."
    
    echo -e "\n${CYAN}RemoteOps Status:${NC}"
    echo "================="
    
    # Check services
    local backend_status="STOPPED"
    local frontend_status="STOPPED"
    local python_status="STOPPED"
    local db_status="STOPPED"
    local redis_status="STOPPED"
    
    # Check backend
    if curl -s http://localhost:3000/api/health >/dev/null 2>&1; then
        backend_status="${GREEN}RUNNING${NC}"
    else
        backend_status="${RED}STOPPED${NC}"
    fi
    
    # Check frontend
    if curl -s http://localhost:3001 >/dev/null 2>&1; then
        frontend_status="${GREEN}RUNNING${NC}"
    else
        frontend_status="${RED}STOPPED${NC}"
    fi
    
    # Check Python microservice
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        python_status="${GREEN}RUNNING${NC}"
    else
        python_status="${RED}STOPPED${NC}"
    fi
    
    # Check database
    if systemctl is-active --quiet postgresql; then
        db_status="${GREEN}RUNNING${NC}"
    else
        db_status="${RED}STOPPED${NC}"
    fi
    
    # Check Redis
    if systemctl is-active --quiet redis; then
        redis_status="${GREEN}RUNNING${NC}"
    else
        redis_status="${RED}STOPPED${NC}"
    fi
    
    echo -e "Backend:    $backend_status"
    echo -e "Frontend:   $frontend_status"
    echo -e "Python:     $python_status"
    echo -e "Database:   $db_status"
    echo -e "Redis:      $redis_status"
    
    if [[ "$backend_status" == *"RUNNING"* && "$frontend_status" == *"RUNNING"* ]]; then
        echo -e "\n${CYAN}Application URLs:${NC}"
        echo -e "Frontend:    ${GREEN}http://localhost:3001${NC}"
        echo -e "Backend API: ${GREEN}http://localhost:3000${NC}"
        echo -e "Python API:  ${GREEN}http://localhost:8000${NC}"
        
        echo -e "\n${CYAN}Default credentials:${NC}"
        echo -e "Email:    ${GREEN}<EMAIL>${NC}"
        echo -e "Password: ${GREEN}admin123${NC}"
    fi
}

# Show help
show_help() {
    echo "RemoteOps Linux Management Script"
    echo "================================="
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  install    Install and setup RemoteOps"
    echo "  start      Start all services"
    echo "  stop       Stop all services"
    echo "  restart    Restart all services"
    echo "  status     Show service status"
    echo "  update     Update dependencies"
    echo "  logs       Show application logs"
    echo "  help       Show this help message"
    echo ""
    echo "Options:"
    echo "  --docker   Use Docker for installation"
    echo "  --force    Force installation/uninstallation"
    echo ""
    echo "Examples:"
    echo "  $0 install              # Install RemoteOps"
    echo "  $0 start                # Start services"
    echo "  $0 status               # Check status"
    echo "  $0 install --docker     # Install with Docker"
}

# Main execution
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}    RemoteOps Linux Setup Script${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo ""
    
    detect_distro
    
    case "${1:-install}" in
        "install")
            check_root
            install_prerequisites
            setup_project
            start_applications
            
            echo ""
            log_success "🎉 RemoteOps installation completed successfully!"
            echo ""
            log_info "Next steps:"
            echo "1. Access the application at http://localhost:3001"
            echo "2. <NAME_EMAIL> / admin123"
            echo "3. Configure your SSH servers"
            echo "4. Start managing your infrastructure!"
            ;;
        "start")
            start_applications
            ;;
        "stop")
            stop_applications
            ;;
        "restart")
            stop_applications
            sleep 3
            start_applications
            ;;
        "status")
            show_status
            ;;
        "update")
            stop_applications
            install_dependencies
            start_applications
            ;;
        "logs")
            if [ -d "$PROJECT_PATH/logs" ]; then
                echo "=== Backend Logs ==="
                tail -n 20 "$PROJECT_PATH/logs/backend.log" 2>/dev/null || echo "No backend logs"
                echo -e "\n=== Frontend Logs ==="
                tail -n 20 "$PROJECT_PATH/logs/frontend.log" 2>/dev/null || echo "No frontend logs"
                echo -e "\n=== Python Logs ==="
                tail -n 20 "$PROJECT_PATH/logs/python.log" 2>/dev/null || echo "No python logs"
            else
                log_error "No logs directory found"
            fi
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "Unknown command: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Chip,
  LinearProgress,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Switch,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Badge
} from '@mui/material';
import {
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  CheckCircle as CheckIcon,
  Notifications as NotificationIcon,
  Download as ExportIcon,
  PlayArrow as StartIcon,
  Stop as StopIcon
} from '@mui/icons-material';
import { useMonitoring, MonitoringMetric } from '../hooks/useMonitoring';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const AdvancedMonitoring: React.FC = () => {
  const {
    metrics,
    alerts,
    config,
    isConnected,
    lastUpdate,
    updateConfig,
    acknowledgeAlert,
    resolveAlert,
    exportMetrics,
    startMonitoring,
    stopMonitoring
  } = useMonitoring();

  const [showConfigDialog, setShowConfigDialog] = useState(false);
  const [showAlertsDialog, setShowAlertsDialog] = useState(false);
  const [tempConfig, setTempConfig] = useState(config);

  const activeAlerts = alerts.filter(alert => !alert.acknowledged && !alert.resolvedAt);
  const criticalAlerts = activeAlerts.filter(alert => alert.level === 'critical');

  const getMetricStatus = (metric: MonitoringMetric) => {
    if (metric.value >= metric.threshold.critical) return 'critical';
    if (metric.value >= metric.threshold.warning) return 'warning';
    return 'normal';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical': return 'error';
      case 'warning': return 'warning';
      case 'normal': return 'success';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'critical': return <ErrorIcon color="error" />;
      case 'warning': return <WarningIcon color="warning" />;
      case 'normal': return <CheckIcon color="success" />;
      default: return null;
    }
  };

  const getTrendIcon = (trend: MonitoringMetric['trend']) => {
    switch (trend) {
      case 'up': return <TrendingUpIcon color="error" fontSize="small" />;
      case 'down': return <TrendingDownIcon color="success" fontSize="small" />;
      default: return null;
    }
  };

  const getCategoryColor = (category: MonitoringMetric['category']) => {
    switch (category) {
      case 'system': return 'primary';
      case 'network': return 'secondary';
      case 'application': return 'info';
      case 'security': return 'warning';
      default: return 'default';
    }
  };

  const handleSaveConfig = () => {
    updateConfig(tempConfig);
    setShowConfigDialog(false);
  };

  const handleExportMetrics = async () => {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
    
    try {
      const csvData = await exportMetrics(startDate, endDate);
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `metricas-${format(new Date(), 'yyyy-MM-dd-HH-mm')}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao exportar métricas:', error);
    }
  };

  return (
    <Container maxWidth="xl">
      {/* Cabeçalho */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 4 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Monitoramento Avançado
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Chip
              icon={isConnected ? <CheckIcon /> : <ErrorIcon />}
              label={isConnected ? 'Conectado' : 'Desconectado'}
              color={isConnected ? 'success' : 'error'}
              size="small"
            />
            {lastUpdate && (
              <Typography variant="caption" color="text.secondary">
                Última atualização: {format(lastUpdate, 'HH:mm:ss')}
              </Typography>
            )}
          </Box>
        </Box>

        <Box sx={{ display: 'flex', gap: 1 }}>
          <Badge badgeContent={activeAlerts.length} color="error">
            <IconButton onClick={() => setShowAlertsDialog(true)}>
              <NotificationIcon />
            </IconButton>
          </Badge>
          
          <IconButton onClick={handleExportMetrics}>
            <ExportIcon />
          </IconButton>
          
          <IconButton onClick={() => setShowConfigDialog(true)}>
            <SettingsIcon />
          </IconButton>
          
          <IconButton onClick={isConnected ? stopMonitoring : startMonitoring}>
            {isConnected ? <StopIcon /> : <StartIcon />}
          </IconButton>
        </Box>
      </Box>

      {/* Alertas críticos */}
      {criticalAlerts.length > 0 && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            {criticalAlerts.length} alerta(s) crítico(s) detectado(s)
          </Typography>
          {criticalAlerts.slice(0, 3).map(alert => (
            <Typography key={alert.id} variant="body2">
              • {alert.message}
            </Typography>
          ))}
        </Alert>
      )}

      {/* Métricas */}
      <Grid container spacing={3}>
        {metrics.map((metric) => {
          const status = getMetricStatus(metric);
          const progressValue = Math.min((metric.value / metric.threshold.critical) * 100, 100);
          
          return (
            <Grid item xs={12} sm={6} md={4} lg={3} key={metric.id}>
              <Card>
                <CardHeader
                  title={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle2" sx={{ flexGrow: 1 }}>
                        {metric.name}
                      </Typography>
                      {getTrendIcon(metric.trend)}
                    </Box>
                  }
                  subheader={
                    <Chip
                      label={metric.category}
                      size="small"
                      color={getCategoryColor(metric.category) as any}
                      variant="outlined"
                    />
                  }
                  action={getStatusIcon(status)}
                  sx={{ pb: 1 }}
                />
                
                <CardContent sx={{ pt: 0 }}>
                  <Typography 
                    variant="h4" 
                    sx={{ 
                      color: `${getStatusColor(status)}.main`,
                      fontWeight: 'bold',
                      mb: 1
                    }}
                  >
                    {metric.value.toFixed(1)}{metric.unit}
                  </Typography>

                  <LinearProgress
                    variant="determinate"
                    value={progressValue}
                    color={getStatusColor(status) as any}
                    sx={{ 
                      height: 8, 
                      borderRadius: 4,
                      mb: 1,
                      backgroundColor: 'action.hover'
                    }}
                  />

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="caption" color="text.secondary">
                      Aviso: {metric.threshold.warning}{metric.unit}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Crítico: {metric.threshold.critical}{metric.unit}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {/* Dialog de configurações */}
      <Dialog open={showConfigDialog} onClose={() => setShowConfigDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Configurações de Monitoramento</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  label="Intervalo de Atualização (ms)"
                  type="number"
                  fullWidth
                  value={tempConfig.refreshInterval}
                  onChange={(e) => setTempConfig(prev => ({
                    ...prev,
                    refreshInterval: parseInt(e.target.value)
                  }))}
                  inputProps={{ min: 1000, max: 60000 }}
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={tempConfig.alertsEnabled}
                      onChange={(e) => setTempConfig(prev => ({
                        ...prev,
                        alertsEnabled: e.target.checked
                      }))}
                    />
                  }
                  label="Alertas Habilitados"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowConfigDialog(false)}>Cancelar</Button>
          <Button onClick={handleSaveConfig} variant="contained">Salvar</Button>
        </DialogActions>
      </Dialog>

      {/* Dialog de alertas */}
      <Dialog open={showAlertsDialog} onClose={() => setShowAlertsDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Alertas Ativos ({activeAlerts.length})
        </DialogTitle>
        <DialogContent>
          {activeAlerts.length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <CheckIcon sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
              <Typography variant="h6" color="success.main">
                Nenhum alerta ativo
              </Typography>
            </Box>
          ) : (
            <List>
              {activeAlerts.map((alert, index) => (
                <React.Fragment key={alert.id}>
                  <ListItem>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {alert.level === 'critical' ? <ErrorIcon color="error" /> : <WarningIcon color="warning" />}
                          <Typography variant="subtitle2">
                            {alert.message}
                          </Typography>
                        </Box>
                      }
                      secondary={format(alert.timestamp, 'dd/MM/yyyy HH:mm:ss', { locale: ptBR })}
                    />
                    <ListItemSecondaryAction>
                      <Button
                        size="small"
                        onClick={() => acknowledgeAlert(alert.id)}
                      >
                        Reconhecer
                      </Button>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < activeAlerts.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAlertsDialog(false)}>Fechar</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default AdvancedMonitoring;

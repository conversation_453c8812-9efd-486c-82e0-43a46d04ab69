import { useState, useEffect, useCallback } from 'react';

export interface SystemLog {
  id: string;
  timestamp: Date;
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  category: 'system' | 'security' | 'network' | 'application' | 'user' | 'backup';
  source: string;
  message: string;
  details?: Record<string, any>;
  userId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  correlationId?: string;
}

export interface LogFilter {
  levels: SystemLog['level'][];
  categories: SystemLog['category'][];
  sources: string[];
  dateRange: {
    start: Date;
    end: Date;
  };
  searchTerm: string;
  userId?: string;
  correlationId?: string;
}

export interface LogStats {
  totalLogs: number;
  logsByLevel: Record<SystemLog['level'], number>;
  logsByCategory: Record<SystemLog['category'], number>;
  topSources: Array<{ source: string; count: number }>;
  errorRate: number;
  logsPerHour: Array<{ hour: number; count: number }>;
}

interface UseSystemLogsReturn {
  logs: SystemLog[];
  filteredLogs: SystemLog[];
  stats: LogStats;
  filter: LogFilter;
  isLoading: boolean;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  updateFilter: (newFilter: Partial<LogFilter>) => void;
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  exportLogs: (format: 'csv' | 'json') => Promise<void>;
  clearLogs: () => void;
  addLog: (log: Omit<SystemLog, 'id' | 'timestamp'>) => void;
  getLogDetails: (logId: string) => SystemLog | null;
  searchLogs: (term: string) => void;
}

const STORAGE_KEY = 'sem-fronteiras-system-logs';
const MAX_LOGS = 10000; // Máximo de logs mantidos em memória

const DEFAULT_FILTER: LogFilter = {
  levels: ['debug', 'info', 'warn', 'error', 'fatal'],
  categories: ['system', 'security', 'network', 'application', 'user', 'backup'],
  sources: [],
  dateRange: {
    start: new Date(Date.now() - 24 * 60 * 60 * 1000), // Últimas 24h
    end: new Date()
  },
  searchTerm: ''
};

export const useSystemLogs = (): UseSystemLogsReturn => {
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [filter, setFilter] = useState<LogFilter>(DEFAULT_FILTER);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);

  // Carregar logs do localStorage
  useEffect(() => {
    try {
      const storedLogs = localStorage.getItem(STORAGE_KEY);
      if (storedLogs) {
        const parsed = JSON.parse(storedLogs);
        const logsWithDates = parsed.map((log: any) => ({
          ...log,
          timestamp: new Date(log.timestamp)
        }));
        setLogs(logsWithDates);
      } else {
        // Gerar logs iniciais para demonstração
        generateInitialLogs();
      }
    } catch (error) {
      console.error('Erro ao carregar logs:', error);
      generateInitialLogs();
    }
  }, []);

  // Salvar logs no localStorage
  useEffect(() => {
    try {
      // Manter apenas os logs mais recentes para evitar problemas de performance
      const recentLogs = logs.slice(0, MAX_LOGS);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(recentLogs));
    } catch (error) {
      console.error('Erro ao salvar logs:', error);
    }
  }, [logs]);

  // Gerar logs iniciais para demonstração
  const generateInitialLogs = useCallback(() => {
    const initialLogs: SystemLog[] = [];
    const now = new Date();
    
    const sources = [
      'AuthService', 'CommandExecutor', 'BackupManager', 'NetworkMonitor',
      'UserManager', 'SecurityAuditor', 'DatabaseConnector', 'FileManager'
    ];
    
    const messages = {
      debug: [
        'Iniciando processo de verificação',
        'Cache atualizado com sucesso',
        'Conexão estabelecida com servidor',
        'Validando parâmetros de entrada'
      ],
      info: [
        'Usuário logado com sucesso',
        'Comando executado com sucesso',
        'Backup criado automaticamente',
        'Configuração atualizada'
      ],
      warn: [
        'Tentativa de login com credenciais inválidas',
        'Timeout na conexão com servidor',
        'Espaço em disco baixo',
        'Configuração não encontrada, usando padrão'
      ],
      error: [
        'Falha na execução do comando',
        'Erro de conexão com banco de dados',
        'Arquivo não encontrado',
        'Permissão negada para operação'
      ],
      fatal: [
        'Sistema crítico indisponível',
        'Falha catastrófica no banco de dados',
        'Memória insuficiente para continuar',
        'Corrupção detectada nos dados'
      ]
    };
    
    // Gerar 500 logs dos últimos 7 dias
    for (let i = 0; i < 500; i++) {
      const level = ['debug', 'info', 'warn', 'error', 'fatal'][
        Math.floor(Math.random() * 5)
      ] as SystemLog['level'];
      
      const category = ['system', 'security', 'network', 'application', 'user', 'backup'][
        Math.floor(Math.random() * 6)
      ] as SystemLog['category'];
      
      const source = sources[Math.floor(Math.random() * sources.length)];
      const message = messages[level][Math.floor(Math.random() * messages[level].length)];
      
      const logTime = new Date(now.getTime() - Math.random() * 7 * 24 * 60 * 60 * 1000);
      
      initialLogs.push({
        id: `log-${Date.now()}-${i}`,
        timestamp: logTime,
        level,
        category,
        source,
        message,
        details: {
          processId: Math.floor(Math.random() * 10000),
          threadId: Math.floor(Math.random() * 100),
          duration: Math.floor(Math.random() * 1000)
        },
        userId: Math.random() > 0.7 ? `user-${Math.floor(Math.random() * 10)}` : undefined,
        sessionId: `session-${Math.floor(Math.random() * 1000)}`,
        ipAddress: `192.168.1.${Math.floor(Math.random() * 254) + 1}`,
        correlationId: Math.random() > 0.8 ? `corr-${Math.floor(Math.random() * 1000)}` : undefined
      });
    }
    
    // Ordenar por timestamp (mais recente primeiro)
    initialLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    setLogs(initialLogs);
  }, []);

  // Filtrar logs
  const filteredLogs = logs.filter(log => {
    // Filtro por nível
    if (!filter.levels.includes(log.level)) return false;
    
    // Filtro por categoria
    if (!filter.categories.includes(log.category)) return false;
    
    // Filtro por fonte
    if (filter.sources.length > 0 && !filter.sources.includes(log.source)) return false;
    
    // Filtro por data
    if (log.timestamp < filter.dateRange.start || log.timestamp > filter.dateRange.end) return false;
    
    // Filtro por termo de busca
    if (filter.searchTerm) {
      const searchTerm = filter.searchTerm.toLowerCase();
      const searchableText = `${log.message} ${log.source} ${log.details ? JSON.stringify(log.details) : ''}`.toLowerCase();
      if (!searchableText.includes(searchTerm)) return false;
    }
    
    // Filtro por usuário
    if (filter.userId && log.userId !== filter.userId) return false;
    
    // Filtro por correlationId
    if (filter.correlationId && log.correlationId !== filter.correlationId) return false;
    
    return true;
  });

  // Paginação
  const totalPages = Math.ceil(filteredLogs.length / pageSize);
  const paginatedLogs = filteredLogs.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // Calcular estatísticas
  const stats: LogStats = {
    totalLogs: logs.length,
    logsByLevel: {
      debug: logs.filter(l => l.level === 'debug').length,
      info: logs.filter(l => l.level === 'info').length,
      warn: logs.filter(l => l.level === 'warn').length,
      error: logs.filter(l => l.level === 'error').length,
      fatal: logs.filter(l => l.level === 'fatal').length
    },
    logsByCategory: {
      system: logs.filter(l => l.category === 'system').length,
      security: logs.filter(l => l.category === 'security').length,
      network: logs.filter(l => l.category === 'network').length,
      application: logs.filter(l => l.category === 'application').length,
      user: logs.filter(l => l.category === 'user').length,
      backup: logs.filter(l => l.category === 'backup').length
    },
    topSources: Object.entries(
      logs.reduce((acc, log) => {
        acc[log.source] = (acc[log.source] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    )
      .map(([source, count]) => ({ source, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10),
    errorRate: logs.length > 0 
      ? (logs.filter(l => l.level === 'error' || l.level === 'fatal').length / logs.length) * 100 
      : 0,
    logsPerHour: Array.from({ length: 24 }, (_, hour) => ({
      hour,
      count: logs.filter(log => {
        const logHour = log.timestamp.getHours();
        return logHour === hour;
      }).length
    }))
  };

  const updateFilter = useCallback((newFilter: Partial<LogFilter>) => {
    setFilter(prev => ({ ...prev, ...newFilter }));
    setCurrentPage(1); // Reset para primeira página
  }, []);

  const setPage = useCallback((page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  }, [totalPages]);

  const setPageSizeCallback = useCallback((size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  }, []);

  const exportLogs = useCallback(async (format: 'csv' | 'json'): Promise<void> => {
    setIsLoading(true);
    try {
      let content: string;
      let filename: string;
      let mimeType: string;
      
      if (format === 'csv') {
        const headers = ['Timestamp', 'Level', 'Category', 'Source', 'Message', 'User ID', 'IP Address'];
        const csvRows = [
          headers.join(','),
          ...filteredLogs.map(log => [
            log.timestamp.toISOString(),
            log.level,
            log.category,
            log.source,
            `"${log.message.replace(/"/g, '""')}"`,
            log.userId || '',
            log.ipAddress || ''
          ].join(','))
        ];
        content = csvRows.join('\n');
        filename = `system-logs-${new Date().toISOString().split('T')[0]}.csv`;
        mimeType = 'text/csv';
      } else {
        content = JSON.stringify(filteredLogs, null, 2);
        filename = `system-logs-${new Date().toISOString().split('T')[0]}.json`;
        mimeType = 'application/json';
      }
      
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao exportar logs:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [filteredLogs]);

  const clearLogs = useCallback(() => {
    setLogs([]);
    setCurrentPage(1);
  }, []);

  const addLog = useCallback((logData: Omit<SystemLog, 'id' | 'timestamp'>) => {
    const newLog: SystemLog = {
      ...logData,
      id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };
    
    setLogs(prev => [newLog, ...prev.slice(0, MAX_LOGS - 1)]);
  }, []);

  const getLogDetails = useCallback((logId: string): SystemLog | null => {
    return logs.find(log => log.id === logId) || null;
  }, [logs]);

  const searchLogs = useCallback((term: string) => {
    updateFilter({ searchTerm: term });
  }, [updateFilter]);

  return {
    logs: paginatedLogs,
    filteredLogs,
    stats,
    filter,
    isLoading,
    totalPages,
    currentPage,
    pageSize,
    updateFilter,
    setPage,
    setPageSize: setPageSizeCallback,
    exportLogs,
    clearLogs,
    addLog,
    getLogDetails,
    searchLogs
  };
};

export default useSystemLogs;

# RemoteOps Helm Chart Values
# Default values for remoteops deployment

# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""

# Application configuration
app:
  name: remoteops
  version: "1.0.0"
  environment: production

# Image configuration
image:
  registry: docker.io
  repository: remoteops/remoteops
  tag: "latest"
  pullPolicy: IfNotPresent
  pullSecrets: []

# Backend configuration
backend:
  enabled: true
  name: backend
  replicaCount: 3
  image:
    repository: remoteops/backend
    tag: "latest"
    pullPolicy: IfNotPresent
  
  service:
    type: ClusterIP
    port: 3000
    targetPort: 3000
    annotations: {}
  
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  nodeSelector: {}
  tolerations: []
  affinity: {}
  
  env:
    NODE_ENV: production
    PORT: "3000"
    JWT_SECRET: ""
    DATABASE_URL: ""
    REDIS_URL: ""
  
  envFrom: []
  
  livenessProbe:
    httpGet:
      path: /api/health/live
      port: 3000
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
  
  readinessProbe:
    httpGet:
      path: /api/health/ready
      port: 3000
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3

# Frontend configuration
frontend:
  enabled: true
  name: frontend
  replicaCount: 2
  image:
    repository: remoteops/frontend
    tag: "latest"
    pullPolicy: IfNotPresent
  
  service:
    type: ClusterIP
    port: 80
    targetPort: 3000
    annotations: {}
  
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 5
    targetCPUUtilizationPercentage: 70
  
  nodeSelector: {}
  tolerations: []
  affinity: {}
  
  env:
    REACT_APP_API_URL: ""
    REACT_APP_PYTHON_API_URL: ""
  
  livenessProbe:
    httpGet:
      path: /
      port: 3000
    initialDelaySeconds: 30
    periodSeconds: 10
  
  readinessProbe:
    httpGet:
      path: /
      port: 3000
    initialDelaySeconds: 5
    periodSeconds: 5

# Python microservice configuration
pythonService:
  enabled: true
  name: python-service
  replicaCount: 2
  image:
    repository: remoteops/python-service
    tag: "latest"
    pullPolicy: IfNotPresent
  
  service:
    type: ClusterIP
    port: 8000
    targetPort: 8000
    annotations: {}
  
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 70
  
  nodeSelector: {}
  tolerations: []
  affinity: {}
  
  env:
    REDIS_URL: ""
    DATABASE_URL: ""
  
  livenessProbe:
    httpGet:
      path: /health
      port: 8000
    initialDelaySeconds: 30
    periodSeconds: 10
  
  readinessProbe:
    httpGet:
      path: /health
      port: 8000
    initialDelaySeconds: 5
    periodSeconds: 5

# PostgreSQL configuration
postgresql:
  enabled: true
  auth:
    postgresPassword: "password"
    username: "postgres"
    password: "password"
    database: "remoteops"
  
  primary:
    persistence:
      enabled: true
      size: 20Gi
      storageClass: ""
    
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi
  
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true

# Redis configuration
redis:
  enabled: true
  auth:
    enabled: false
  
  master:
    persistence:
      enabled: true
      size: 8Gi
      storageClass: ""
    
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi
  
  metrics:
    enabled: true
    serviceMonitor:
      enabled: true

# Ingress configuration
ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  
  hosts:
    - host: remoteops.example.com
      paths:
        - path: /
          pathType: Prefix
          service:
            name: frontend
            port: 80
        - path: /api
          pathType: Prefix
          service:
            name: backend
            port: 3000
        - path: /python-api
          pathType: Prefix
          service:
            name: python-service
            port: 8000
  
  tls:
    - secretName: remoteops-tls
      hosts:
        - remoteops.example.com

# Service Monitor for Prometheus
serviceMonitor:
  enabled: true
  namespace: ""
  labels: {}
  interval: 30s
  scrapeTimeout: 10s

# Monitoring configuration
monitoring:
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true
  
  grafana:
    enabled: true
    adminPassword: "admin"
    dashboards:
      enabled: true

# Security configuration
security:
  podSecurityPolicy:
    enabled: false
  
  networkPolicy:
    enabled: true
    ingress:
      enabled: true
    egress:
      enabled: true
  
  rbac:
    create: true
  
  serviceAccount:
    create: true
    name: ""
    annotations: {}

# Persistence configuration
persistence:
  enabled: true
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 10Gi

# ConfigMap and Secret configuration
config:
  create: true
  data: {}

secrets:
  create: true
  data: {}

# Pod Disruption Budget
podDisruptionBudget:
  enabled: true
  minAvailable: 1
  maxUnavailable: ""

# Node affinity and tolerations
nodeSelector: {}
tolerations: []
affinity: {}

# Extra objects to deploy
extraObjects: []

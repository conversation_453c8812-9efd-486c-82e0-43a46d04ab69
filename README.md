<div align="center">
  <img src="assets/remoteops-logo.svg" alt="RemoteOps Logo" width="180" height="180">
  
  # 🚀 RemoteOps
  
  <p><em>Streamline Your Remote Operations</em></p>
  <p>Plataforma SaaS completa para gerenciamento de infraestrutura remota via SSH/RDP</p>

  <p>
    <a href="#-sobre">Sobre</a> •
    <a href="#-funcionalidades">Funcionalidades</a> •
    <a href="#-tecnologias">Tecnologias</a> •
    <a href="#-arquitetura">Arquitetura</a> •
    <a href="#-instalação">Instalação</a> •
    <a href="#-uso">Uso</a> •
    <a href="#-documentação">Documentação</a> •
    <a href="#-saas">SaaS</a> •
    <a href="#-contribuição">Contribuição</a> •
    <a href="#-licença">Licença</a>
  </p>

  <p>
    <img src="https://img.shields.io/badge/Versão-2.0.0-blue?style=for-the-badge&logo=semver" alt="Versão">
    <img src="https://img.shields.io/badge/Licença-MIT-green?style=for-the-badge&logo=opensourceinitiative" alt="Licença">
    <img src="https://img.shields.io/badge/Status-SaaS%20Ready-success?style=for-the-badge&logo=vercel" alt="Status">
    <img src="https://img.shields.io/badge/Docker-Ready-blue?style=for-the-badge&logo=docker" alt="Docker">
  </p>
  
  <p>
    <img src="https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white" alt="TypeScript">
    <img src="https://img.shields.io/badge/React-61DAFB?style=for-the-badge&logo=react&logoColor=black" alt="React">
    <img src="https://img.shields.io/badge/Fastify-000000?style=for-the-badge&logo=fastify&logoColor=white" alt="Fastify">
    <img src="https://img.shields.io/badge/Python-3776AB?style=for-the-badge&logo=python&logoColor=white" alt="Python">
  </p>
  
  <p>
    <img src="https://img.shields.io/badge/Redis-DC382D?style=for-the-badge&logo=redis&logoColor=white" alt="Redis">
    <img src="https://img.shields.io/badge/PostgreSQL-336791?style=for-the-badge&logo=postgresql&logoColor=white" alt="PostgreSQL">
    <img src="https://img.shields.io/badge/NGINX-009639?style=for-the-badge&logo=nginx&logoColor=white" alt="NGINX">
    <img src="https://img.shields.io/badge/Docker_Compose-2496ED?style=for-the-badge&logo=docker&logoColor=white" alt="Docker Compose">
  </p>
</div>

---

## 🌟 Sobre

**RemoteOps** é uma plataforma SaaS completa para gerenciamento de infraestrutura remota que simplifica operações SSH/RDP em escala empresarial. Desenvolvida para DevOps Engineers, System Administrators e equipes de TI que precisam gerenciar centenas ou milhares de servidores de forma eficiente e segura.

<div align="center">
  <img src="assets/dashboard-preview.png" alt="Dashboard Preview" width="90%">
</div>

### 🎯 Objetivos

- **Centralizar** o gerenciamento de toda infraestrutura remota em uma única plataforma
- **Automatizar** tarefas repetitivas com templates e comandos inteligentes
- **Otimizar** performance com cache distribuído e execução paralela
- **Garantir** segurança com auditoria completa e controle de acesso granular
- **Escalar** para suportar milhares de servidores e usuários simultâneos
- **Simplificar** operações complexas com interface intuitiva e moderna

### 🚀 Diferenciais

<div align="center">
  <table>
    <tr>
      <td align="center" width="33%">
        <img src="assets/icons/cache.svg" width="48" height="48"><br>
        <b>Cache Distribuído Inteligente</b><br>
        Reduz latência em até 90%
      </td>
      <td align="center" width="33%">
        <img src="assets/icons/parallel.svg" width="48" height="48"><br>
        <b>Execução Paralela</b><br>
        Comandos em múltiplos servidores
      </td>
      <td align="center" width="33%">
        <img src="assets/icons/template.svg" width="48" height="48"><br>
        <b>Templates Avançados</b><br>
        Biblioteca de comandos reutilizáveis
      </td>
    </tr>
    <tr>
      <td align="center">
        <img src="assets/icons/audit.svg" width="48" height="48"><br>
        <b>Auditoria Completa</b><br>
        Rastreamento de todas as ações
      </td>
      <td align="center">
        <img src="assets/icons/api.svg" width="48" height="48"><br>
        <b>API Robusta</b><br>
        Integração com ferramentas existentes
      </td>
      <td align="center">
        <img src="assets/icons/tenant.svg" width="48" height="48"><br>
        <b>Multi-tenant</b><br>
        Pronto para SaaS desde o primeiro dia
      </td>
    </tr>
  </table>
</div>

## ✨ Funcionalidades

<div align="center">
  <img src="assets/features-overview.png" alt="Principais Funcionalidades" width="90%">
</div>

<div align="center">
  <table>
    <tr>
      <td align="center">
        <img src="assets/icons/auth.svg" width="50" height="50"><br>
        <b>Autenticação Segura</b><br>
        JWT + 2FA + Roles
      </td>
      <td align="center">
        <img src="assets/icons/servers.svg" width="50" height="50"><br>
        <b>Gerenciamento de Servidores</b><br>
        Multi-fabricante e SO
      </td>
      <td align="center">
        <img src="assets/icons/commands.svg" width="50" height="50"><br>
        <b>Execução de Comandos</b><br>
        Tempo real com fallback
      </td>
    </tr>
    <tr>
      <td align="center">
        <img src="assets/icons/templates.svg" width="50" height="50"><br>
        <b>Templates de Comandos</b><br>
        Marketplace e biblioteca
      </td>
      <td align="center">
        <img src="assets/icons/groups.svg" width="50" height="50"><br>
        <b>Grupos de Servidores</b><br>
        Organização hierárquica
      </td>
      <td align="center">
        <img src="assets/icons/monitoring.svg" width="50" height="50"><br>
        <b>Monitoramento</b><br>
        Métricas e alertas
      </td>
    </tr>
    <tr>
      <td align="center">
        <img src="assets/icons/audit-trail.svg" width="50" height="50"><br>
        <b>Auditoria</b><br>
        Log de todas as ações
      </td>
      <td align="center">
        <img src="assets/icons/backup.svg" width="50" height="50"><br>
        <b>Backup e Recuperação</b><br>
        Proteção completa de dados
      </td>
      <td align="center">
        <img src="assets/icons/api-access.svg" width="50" height="50"><br>
        <b>API Pública</b><br>
        Integrações e webhooks
      </td>
    </tr>
  </table>
</div>

### 🛠️ Dispositivos Suportados

<div align="center">
  <table>
    <tr>
      <th>Fabricante</th>
      <th>Sistema</th>
      <th>Suporte</th>
      <th>Observações</th>
    </tr>
    <tr>
      <td><img src="assets/vendors/huawei.png" height="20"> <b>Huawei/HarmonyOS</b></td>
      <td>VRP</td>
      <td>✅ Completo</td>
      <td>Configurações otimizadas, timeouts estendidos</td>
    </tr>
    <tr>
      <td><img src="assets/vendors/nokia.png" height="20"> <b>Nokia</b></td>
      <td>SR OS</td>
      <td>✅ Completo</td>
      <td>Timeouts estendidos</td>
    </tr>
    <tr>
      <td><img src="assets/vendors/mikrotik.png" height="20"> <b>Mikrotik</b></td>
      <td>RouterOS</td>
      <td>✅ Completo</td>
      <td>API nativa e SSH</td>
    </tr>
    <tr>
      <td><img src="assets/vendors/datacom.png" height="20"> <b>DMOS</b></td>
      <td>DataCom</td>
      <td>✅ Completo</td>
      <td>Suporte padrão</td>
    </tr>
    <tr>
      <td><img src="assets/vendors/cisco.png" height="20"> <b>Cisco</b></td>
      <td>IOS</td>
      <td>✅ Completo</td>
      <td>Configurações padrão</td>
    </tr>
    <tr>
      <td><img src="assets/vendors/linux.png" height="20"> <b>Linux</b></td>
      <td>Diversos</td>
      <td>✅ Completo</td>
      <td>Servidores padrão</td>
    </tr>
    <tr>
      <td><img src="assets/vendors/windows.png" height="20"> <b>Windows</b></td>
      <td>Windows Server</td>
      <td>✅ Completo</td>
      <td>Via SSH ou RDP</td>
    </tr>
  </table>
</div>

## 🚀 Tecnologias

<div align="center">
  <table>
    <tr>
      <td align="center"><img src="assets/tech/backend.svg" height="48"><br><b>Backend</b></td>
      <td align="center"><img src="assets/tech/frontend.svg" height="48"><br><b>Frontend</b></td>
      <td align="center"><img src="assets/tech/python.svg" height="48"><br><b>Microserviço Python</b></td>
      <td align="center"><img src="assets/tech/infra.svg" height="48"><br><b>Infraestrutura</b></td>
    </tr>
    <tr>
      <td>
        <ul>
          <li>Node.js 18 com TypeScript</li>
          <li>Fastify (framework web)</li>
          <li>Prisma (ORM)</li>
          <li>PostgreSQL (banco de dados)</li>
          <li>Redis (cache)</li>
          <li>JWT (autenticação)</li>
        </ul>
      </td>
      <td>
        <ul>
          <li>React 18 com TypeScript</li>
          <li>Vite (build tool)</li>
          <li>Tailwind CSS</li>
          <li>React Router</li>
          <li>Axios</li>
        </ul>
      </td>
      <td>
        <ul>
          <li>FastAPI</li>
          <li>Netmiko (SSH)</li>
          <li>Uvicorn (servidor ASGI)</li>
          <li>Pydantic (validação)</li>
        </ul>
      </td>
      <td>
        <ul>
          <li>Docker e Docker Compose</li>
          <li>Nginx (reverse proxy)</li>
          <li>Let's Encrypt (SSL)</li>
        </ul>
      </td>
    </tr>
  </table>
</div>

## 🏗️ Arquitetura

O sistema utiliza uma arquitetura híbrida inovadora que combina:

<div align="center">
  <img src="assets/architecture-diagram.svg" alt="Arquitetura RemoteOps" width="80%">
</div>

### 🔄 Fluxo de Trabalho

1. O frontend React envia requisições para a API Node.js
2. A API Node.js processa a requisição e decide se:
   - Executa o comando diretamente via SSH (dispositivos compatíveis)
   - Encaminha para o microserviço Python (dispositivos problemáticos)
3. O resultado é retornado ao frontend e armazenado no histórico

## 📦 Instalação

### Pré-requisitos

- Docker e Docker Compose
- Node.js 18+ (para desenvolvimento)
- Python 3.10+ (para desenvolvimento)

### Instalação com Docker (Recomendado)

```bash
# 1. Clone o repositório
git clone https://github.com/seu-usuario/remoteops.git
cd remoteops

# 2. Configure o ambiente
cp .env.example .env
# Edite o arquivo .env conforme necessário

# 3. Inicie os containers
docker-compose up -d

# 4. Acesse o sistema
# Frontend: http://localhost:3000
# API: http://localhost:3001
# Python Service: http://localhost:8000
```

### Instalação Manual

Consulte o [Guia de Deploy](docs/guia_deploy_producao.md) para instruções detalhadas.

## 🖥️ Uso

<div align="center">
  <img src="assets/usage-example.gif" alt="Exemplo de Uso" width="90%">
</div>

### Interface Web

Acesse a interface web em `http://localhost:3000` (ou o endereço configurado).

1. Faça login com suas credenciais
2. Navegue até "Servidores" para gerenciar seus dispositivos
3. Selecione um servidor para conectar
4. Execute comandos diretamente na interface

### API REST

A API está disponível em `http://localhost:3001` (ou o endereço configurado).

Documentação completa da API: [API Reference](docs/api_reference.md)

#### Exemplo de Uso da API

```bash
# Autenticação
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"senha123"}'

# Executar comando SSH (com token JWT)
curl -X POST http://localhost:3001/ssh/execute \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer SEU_TOKEN_JWT" \
  -d '{
    "serverId": "123e4567-e89b-12d3-a456-426614174000",
    "command": "display version"
  }'
```

## 📚 Documentação

<div align="center">
  <img src="assets/documentation-preview.png" alt="Documentação" width="90%">
</div>

### Documentação Técnica

- [📋 Checklist Completo](checklist.md)
- [🚀 Guia de Deploy](docs/guia_deploy_producao.md)
- [📊 Sistema de Monitoramento](docs/sistema_monitoramento.md)
- [⚡ Cache e Performance](docs/sistema_cache_performance.md)
- [🔒 Backup e Testes](docs/sistema_backup_testes.md)
- [🐍 Microserviço Python](docs/implementacao_microservico_python.md)
- [📡 API Reference](docs/api_reference.md)

### Documentação do Banco de Dados

A estrutura completa do banco de dados está documentada em [db_structure.md](db_structure.md).

## 🤝 Contribuição

Contribuições são bem-vindas! Consulte o [Guia de Contribuição](CONTRIBUTING.md) para mais informações sobre como participar do desenvolvimento.

### Como Contribuir

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/nova-funcionalidade`)
3. Faça commit das suas alterações (`git commit -m 'Adiciona nova funcionalidade'`)
4. Faça push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

## 🌐 SaaS - Software as a Service

<div align="center">
  <img src="assets/saas-overview.png" alt="SaaS Overview" width="90%">
</div>

**RemoteOps** está sendo transformado em uma solução SaaS completa, oferecendo todos os benefícios de uma plataforma gerenciada na nuvem.

### 💰 Planos de Preços

<div align="center">
  <table>
    <tr>
      <th>Plano</th>
      <th>Preço</th>
      <th>Servidores</th>
      <th>Usuários</th>
      <th>Recursos</th>
    </tr>
    <tr>
      <td>🆓 <b>Starter</b></td>
      <td>Gratuito</td>
      <td>5</td>
      <td>1</td>
      <td>Comandos básicos, Suporte community</td>
    </tr>
    <tr>
      <td>🔵 <b>Professional</b></td>
      <td>$29/mês</td>
      <td>50</td>
      <td>5</td>
      <td>Templates avançados, Cache otimizado, Suporte email</td>
    </tr>
    <tr>
      <td>🟢 <b>Business</b></td>
      <td>$99/mês</td>
      <td>200</td>
      <td>20</td>
      <td>Auditoria completa, API access, Suporte prioritário</td>
    </tr>
    <tr>
      <td>⭐ <b>Enterprise</b></td>
      <td>Custom</td>
      <td>Ilimitado</td>
      <td>Ilimitado</td>
      <td>SSO/SAML, Suporte dedicado, Features customizadas</td>
    </tr>
  </table>
</div>

### 🎯 Benefícios SaaS

<div align="center">
  <table>
    <tr>
      <td align="center" width="33%">
        <img src="assets/icons/maintenance.svg" width="40" height="40"><br>
        <b>Zero Manutenção</b><br>
        Sem gerenciar infraestrutura
      </td>
      <td align="center" width="33%">
        <img src="assets/icons/scale.svg" width="40" height="40"><br>
        <b>Escalabilidade Automática</b><br>
        Cresce com sua necessidade
      </td>
      <td align="center" width="33%">
        <img src="assets/icons/updates.svg" width="40" height="40"><br>
        <b>Atualizações Automáticas</b><br>
        Sempre na versão mais recente
      </td>
    </tr>
    <tr>
      <td align="center">
        <img src="assets/icons/backup-cloud.svg" width="40" height="40"><br>
        <b>Backup Automático</b><br>
        Seus dados sempre seguros
      </td>
      <td align="center">
        <img src="assets/icons/support.svg" width="40" height="40"><br>
        <b>Suporte 24/7</b><br>
        Equipe especializada disponível
      </td>
      <td align="center">
        <img src="assets/icons/sla.svg" width="40" height="40"><br>
        <b>SLA 99.9%</b><br>
        Garantia de disponibilidade
      </td>
    </tr>
  </table>
</div>

### 🚀 Roadmap SaaS

<div align="center">
  <img src="assets/roadmap.png" alt="Roadmap SaaS" width="90%">
</div>

#### v2.1.0 - Multi-tenancy (Em Desenvolvimento)
- [x] Sistema de cache distribuído
- [x] Isolamento de dados por tenant
- [ ] Billing e subscription management
- [ ] Onboarding automatizado
- [ ] Admin panel para SaaS

#### v2.2.0 - Marketplace (Implementado)
- [x] Template marketplace
- [x] Plugin system
- [x] Integrações com terceiros
- [x] Webhooks avançados

#### v2.3.0 - Enterprise Features (Planejado)
- [ ] SSO/SAML integration
- [ ] Advanced RBAC
- [ ] Compliance reports
- [ ] White-label options
- [ ] Dedicated instances

## 📄 Licença

Este projeto está licenciado sob a [Licença MIT](LICENSE).

## 👥 Equipe

Desenvolvido com ❤️ pela equipe **RemoteOps** para DevOps e SysAdmins.

### 🤝 Comunidade

<div align="center">
  <a href="https://remoteops.io" target="_blank">
    <img src="assets/icons/website.svg" width="40" height="40"><br>
    <b>Website</b>
  </a>&nbsp;&nbsp;&nbsp;&nbsp;
  <a href="mailto:<EMAIL>">
    <img src="assets/icons/email.svg" width="40" height="40"><br>
    <b>Email</b>
  </a>&nbsp;&nbsp;&nbsp;&nbsp;
  <a href="https://discord.gg/remoteops" target="_blank">
    <img src="assets/icons/discord.svg" width="40" height="40"><br>
    <b>Discord</b>
  </a>&nbsp;&nbsp;&nbsp;&nbsp;
  <a href="https://twitter.com/remoteops" target="_blank">
    <img src="assets/icons/twitter.svg" width="40" height="40"><br>
    <b>Twitter</b>
  </a>&nbsp;&nbsp;&nbsp;&nbsp;
  <a href="https://linkedin.com/company/remoteops" target="_blank">
    <img src="assets/icons/linkedin.svg" width="40" height="40"><br>
    <b>LinkedIn</b>
  </a>
</div>

---

<div align="center">
  <p>
    <a href="https://github.com/remoteops/remoteops/issues">Reportar Bug</a> •
    <a href="https://github.com/remoteops/remoteops/issues">Solicitar Funcionalidade</a> •
    <a href="https://docs.remoteops.io">Documentação</a> •
    <a href="https://status.remoteops.io">Status</a>
  </p>
  <p>
    <strong>RemoteOps</strong> - Streamline Your Remote Operations! 🚀
  </p>
  <p>
    <em>Transformando a forma como você gerencia infraestrutura remota</em>
  </p>
  <p>
    <small>Ícones e diagramas criados com SVG para melhor visualização, performance e acessibilidade.</small>
  </p>
</div>
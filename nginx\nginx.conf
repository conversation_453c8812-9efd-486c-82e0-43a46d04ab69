# RemoteOps Load Balancer Configuration
# High-performance NGINX configuration for production SaaS

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Optimize worker connections
events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    
    # Upstream backend servers
    upstream remoteops_backend {
        # Load balancing method
        least_conn;
        
        # Backend servers with health checks
        server remoteops-backend-1:3000 max_fails=3 fail_timeout=30s;
        server remoteops-backend-2:3000 max_fails=3 fail_timeout=30s;
        server remoteops-backend-3:3000 max_fails=3 fail_timeout=30s backup;
        
        # Keep alive connections
        keepalive 32;
    }
    
    # Upstream Python microservice
    upstream remoteops_python {
        least_conn;
        
        server remoteops-python-1:8000 max_fails=3 fail_timeout=30s;
        server remoteops-python-2:8000 max_fails=3 fail_timeout=30s;
        
        keepalive 16;
    }
    
    # Upstream frontend servers
    upstream remoteops_frontend {
        least_conn;
        
        server remoteops-frontend-1:80 max_fails=3 fail_timeout=30s;
        server remoteops-frontend-2:80 max_fails=3 fail_timeout=30s;
        
        keepalive 16;
    }
    
    # Health check endpoint
    server {
        listen 8080;
        server_name _;
        
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow **********/12;
            allow ***********/16;
            deny all;
        }
    }
    
    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name _;
        return 301 https://$host$request_uri;
    }
    
    # Main HTTPS server
    server {
        listen 443 ssl http2;
        server_name remoteops.com www.remoteops.com;
        
        # SSL configuration
        ssl_certificate /etc/ssl/certs/remoteops.crt;
        ssl_certificate_key /etc/ssl/private/remoteops.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin";
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss:";
        
        # Frontend static files
        location / {
            proxy_pass http://remoteops_frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Caching for static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                proxy_pass http://remoteops_frontend;
            }
        }
        
        # API endpoints
        location /api/ {
            # Rate limiting
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://remoteops_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 5s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
            
            # Buffering
            proxy_buffering on;
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            
            # Keep alive
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        # Python microservice
        location /python-api/ {
            proxy_pass http://remoteops_python/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Longer timeouts for SSH operations
            proxy_connect_timeout 10s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
            
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        # WebSocket support for real-time features
        location /ws/ {
            proxy_pass http://remoteops_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket timeouts
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
        }
        
        # Auth endpoints with stricter rate limiting
        location /api/auth/ {
            limit_req zone=login burst=5 nodelay;
            
            proxy_pass http://remoteops_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            proxy_pass http://remoteops_backend/api/health;
            proxy_set_header Host $host;
        }
    }
    
    # Staging environment
    server {
        listen 443 ssl http2;
        server_name staging.remoteops.com;
        
        # Same SSL config as production
        ssl_certificate /etc/ssl/certs/remoteops.crt;
        ssl_certificate_key /etc/ssl/private/remoteops.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        
        # Staging upstream
        location / {
            proxy_pass http://remoteops_frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        location /api/ {
            proxy_pass http://remoteops_backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}

# Stream module for TCP/UDP load balancing
stream {
    # SSH proxy for direct SSH access (optional)
    upstream ssh_backend {
        least_conn;
        server remoteops-backend-1:2222;
        server remoteops-backend-2:2222;
    }
    
    server {
        listen 2222;
        proxy_pass ssh_backend;
        proxy_timeout 1s;
        proxy_responses 1;
    }
}

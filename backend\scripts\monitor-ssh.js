/**
 * Script para monitorar e encerrar sessões SSH pendentes
 * 
 * Este script verifica se existem sessões SSH pendentes e as encerra,
 * evitando o acúmulo de conexões que podem bloquear o acesso aos equipamentos.
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configurações
const LOG_FILE = path.join(__dirname, '../logs/ssh-monitor.log');
const MAX_INACTIVE_TIME = 5 * 60 * 1000; // 5 minutos em milissegundos

// Garantir que o diretório de logs exista
const logDir = path.dirname(LOG_FILE);
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Função para registrar logs
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  console.log(message);
  fs.appendFileSync(LOG_FILE, logMessage);
}

// Função para verificar processos SSH
function checkSSHProcesses() {
  log('Verificando processos SSH pendentes...');
  
  // Comando para listar processos SSH
  // No Windows, usamos o comando 'netstat' para listar conexões
  // No Linux, podemos usar 'ps aux | grep ssh'
  const isWindows = process.platform === 'win32';
  const command = isWindows 
    ? 'netstat -ano | findstr "ESTABLISHED" | findstr ":22 "' 
    : 'ps aux | grep ssh | grep -v grep';
  
  exec(command, (error, stdout, stderr) => {
    if (error && error.code !== 1) {
      log(`Erro ao verificar processos SSH: ${error.message}`);
      return;
    }
    
    if (stderr) {
      log(`Erro na saída do comando: ${stderr}`);
      return;
    }
    
    const lines = stdout.trim().split('\n');
    
    if (lines.length === 0 || (lines.length === 1 && lines[0] === '')) {
      log('Nenhum processo SSH pendente encontrado.');
      return;
    }
    
    log(`Encontrados ${lines.length} processos SSH potencialmente pendentes.`);
    
    // Processar cada linha para identificar processos SSH
    lines.forEach(line => {
      if (line.trim() === '') return;
      
      log(`Processo encontrado: ${line}`);
      
      // Extrair PID do processo
      let pid;
      if (isWindows) {
        // No Windows, o PID é o último campo
        const parts = line.trim().split(/\s+/);
        pid = parts[parts.length - 1];
      } else {
        // No Linux, o PID é o segundo campo
        const parts = line.trim().split(/\s+/);
        pid = parts[1];
      }
      
      if (pid && !isNaN(parseInt(pid))) {
        // Verificar se o processo está inativo por muito tempo
        checkProcessInactivity(pid, isWindows);
      }
    });
  });
}

// Função para verificar inatividade de um processo
function checkProcessInactivity(pid, isWindows) {
  const command = isWindows
    ? `wmic process where ProcessId=${pid} get CreationDate`
    : `ps -p ${pid} -o etimes=`;
  
  exec(command, (error, stdout, stderr) => {
    if (error) {
      log(`Erro ao verificar inatividade do processo ${pid}: ${error.message}`);
      return;
    }
    
    let inactiveTime = 0;
    
    if (isWindows) {
      // No Windows, precisamos calcular o tempo com base na data de criação
      const lines = stdout.trim().split('\n');
      if (lines.length >= 2) {
        const creationDateStr = lines[1].trim();
        if (creationDateStr) {
          // Formato: YYYYMMDDHHMMSS.mmmmmm+UUU
          const year = creationDateStr.substring(0, 4);
          const month = creationDateStr.substring(4, 6);
          const day = creationDateStr.substring(6, 8);
          const hour = creationDateStr.substring(8, 10);
          const minute = creationDateStr.substring(10, 12);
          const second = creationDateStr.substring(12, 14);
          
          const creationDate = new Date(`${year}-${month}-${day}T${hour}:${minute}:${second}`);
          inactiveTime = Date.now() - creationDate.getTime();
        }
      }
    } else {
      // No Linux, o comando retorna diretamente o tempo em segundos
      inactiveTime = parseInt(stdout.trim()) * 1000;
    }
    
    if (inactiveTime >= MAX_INACTIVE_TIME) {
      log(`Processo ${pid} está inativo por ${Math.round(inactiveTime / 1000)} segundos. Encerrando...`);
      killProcess(pid, isWindows);
    } else {
      log(`Processo ${pid} está ativo (${Math.round(inactiveTime / 1000)} segundos). Mantendo.`);
    }
  });
}

// Função para encerrar um processo
function killProcess(pid, isWindows) {
  const command = isWindows ? `taskkill /F /PID ${pid}` : `kill -9 ${pid}`;
  
  exec(command, (error, stdout, stderr) => {
    if (error) {
      log(`Erro ao encerrar processo ${pid}: ${error.message}`);
      return;
    }
    
    log(`Processo ${pid} encerrado com sucesso.`);
  });
}

// Executar a verificação
log('Iniciando monitoramento de sessões SSH...');
checkSSHProcesses();

// Se quiser executar periodicamente, descomente o código abaixo
// const INTERVAL = 5 * 60 * 1000; // 5 minutos
// setInterval(checkSSHProcesses, INTERVAL);

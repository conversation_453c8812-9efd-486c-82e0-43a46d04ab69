#!/bin/bash
# Script para fazer backup do banco de dados e copiar para fora do container
# Este script deve ser executado no host, não dentro do container

# Detectar ambiente
if [ -d "/var/www/sem-fronteiras-ssh" ]; then
  echo "Executando em ambiente de PRODUÇÃO"
  AMBIENTE="produção"
  # Configurações de produção
  PROJETO_DIR="/var/www/sem-fronteiras-ssh"
  POSTGRES_CONTAINER="sem-fronteiras-ssh-postgres-1"
  BACKUP_CONTAINER_DIR="/backups"
  BACKUP_HOST_DIR="/var/www/backups"
  BACKUP_EXTERNO_DIR="/var/backups/sem-fronteiras"
else
  echo "Executando em ambiente de DESENVOLVIMENTO"
  AMBIENTE="desenvolvimento"
  # Configurações de desenvolvimento
  PROJETO_DIR="."
  POSTGRES_CONTAINER="sem-fronteiras-postgres-1"
  BACKUP_CONTAINER_DIR="/backups"
  BACKUP_HOST_DIR="./backups"
  BACKUP_EXTERNO_DIR="./backups-externos"
fi

# Criar diretórios de backup se não existirem
mkdir -p "$BACKUP_HOST_DIR"
mkdir -p "$BACKUP_EXTERNO_DIR"

# Definir nome do arquivo de backup com timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILENAME="backup_$TIMESTAMP.sql"
BACKUP_HOST_FILE="$BACKUP_HOST_DIR/$BACKUP_FILENAME"
BACKUP_EXTERNO_FILE="$BACKUP_EXTERNO_DIR/$BACKUP_FILENAME"

echo "[$(date)] Iniciando backup do banco de dados..."

# 1. Fazer backup do banco de dados para o diretório do host
if docker exec $POSTGRES_CONTAINER pg_dump -U postgres -d sem_fronteiras > "$BACKUP_HOST_FILE" 2>/dev/null; then
  echo "[$(date)] Backup concluído com sucesso: $BACKUP_HOST_FILE"
  
  # 2. Copiar o backup para o diretório externo
  echo "[$(date)] Copiando backup para diretório externo: $BACKUP_EXTERNO_DIR"
  cp "$BACKUP_HOST_FILE" "$BACKUP_EXTERNO_FILE"
  
  if [ $? -eq 0 ]; then
    echo "[$(date)] Backup copiado com sucesso para: $BACKUP_EXTERNO_FILE"
    
    # 3. Definir permissões corretas para o arquivo de backup externo
    chmod 644 "$BACKUP_EXTERNO_FILE"
    
    # 4. Limpar backups antigos (manter últimos 30 dias)
    echo "[$(date)] Limpando backups antigos no diretório externo..."
    find "$BACKUP_EXTERNO_DIR" -type f -name "backup_*.sql" -mtime +30 -delete
    
    echo "[$(date)] Processo de backup externo concluído com sucesso."
  else
    echo "[$(date)] ERRO: Falha ao copiar backup para diretório externo."
  fi
else
  echo "[$(date)] ERRO: Falha ao criar backup do banco de dados."
  exit 1
fi

# Registrar informações sobre o espaço em disco
echo "[$(date)] Informações de espaço em disco:"
df -h | grep -E "(Filesystem|/var|/$)"

# Registrar contagem de backups
BACKUP_COUNT=$(find "$BACKUP_EXTERNO_DIR" -type f -name "backup_*.sql" | wc -l)
echo "[$(date)] Total de backups externos: $BACKUP_COUNT"

exit 0

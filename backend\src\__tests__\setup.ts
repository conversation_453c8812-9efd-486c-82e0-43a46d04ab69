import { PrismaClient } from '@prisma/client';

// Mock do Prisma para testes
const mockPrisma = {
  user: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  server: {
    findUnique: jest.fn(),
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  commandHistory: {
    findMany: jest.fn(),
    create: jest.fn(),
  },
  commandTemplate: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  serverGroup: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  serverUserAccess: {
    findMany: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
  },
  $disconnect: jest.fn(),
};

// Mock global do Prisma
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn(() => mockPrisma),
}));

// Mock do Redis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    get: jest.fn(),
    set: jest.fn(),
    setex: jest.fn(),
    del: jest.fn(),
    keys: jest.fn(),
    flushdb: jest.fn(),
    info: jest.fn(),
    config: jest.fn(),
    quit: jest.fn(),
    on: jest.fn(),
  }));
});

// Mock do node-ssh
jest.mock('node-ssh', () => ({
  NodeSSH: jest.fn().mockImplementation(() => ({
    connect: jest.fn(),
    execCommand: jest.fn(),
    dispose: jest.fn(),
  })),
}));

// Mock do node-routeros-v2
jest.mock('node-routeros-v2', () => ({
  RouterOSAPI: jest.fn().mockImplementation(() => ({
    connect: jest.fn(),
    write: jest.fn(),
    close: jest.fn(),
  })),
}));

// Mock do axios
jest.mock('axios', () => ({
  default: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  },
  isAxiosError: jest.fn(),
}));

// Mock do node-fetch
jest.mock('node-fetch', () => ({
  default: jest.fn(),
}));

// Configurações globais para testes
beforeEach(() => {
  // Limpar todos os mocks antes de cada teste
  jest.clearAllMocks();
  
  // Configurar variáveis de ambiente para testes
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-secret';
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';
  process.env.REDIS_URL = 'redis://localhost:6379';
});

afterEach(() => {
  // Limpeza após cada teste
  jest.clearAllMocks();
});

// Timeout global para testes
jest.setTimeout(30000);

// Exportar mock do Prisma para uso nos testes
export { mockPrisma };

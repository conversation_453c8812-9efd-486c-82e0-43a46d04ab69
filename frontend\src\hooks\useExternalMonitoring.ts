import { useState, useEffect, useCallback } from 'react';

export interface ExternalMonitoringProvider {
  id: string;
  name: string;
  type: 'prometheus' | 'grafana' | 'datadog' | 'newrelic' | 'elastic' | 'zabbix' | 'nagios' | 'custom';
  url: string;
  apiKey?: string;
  username?: string;
  password?: string;
  isActive: boolean;
  config: Record<string, any>;
  lastSync?: Date;
  status: 'connected' | 'disconnected' | 'error' | 'syncing';
  metrics: string[];
  dashboards: ExternalDashboard[];
  alerts: ExternalAlert[];
}

export interface ExternalDashboard {
  id: string;
  name: string;
  url: string;
  description?: string;
  tags: string[];
  isEmbeddable: boolean;
  embedUrl?: string;
}

export interface ExternalAlert {
  id: string;
  name: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'active' | 'resolved' | 'silenced';
  message: string;
  timestamp: Date;
  source: string;
  metadata?: Record<string, any>;
}

export interface MonitoringIntegration {
  id: string;
  providerId: string;
  serverIds: string[];
  metrics: string[];
  syncInterval: number; // em minutos
  isActive: boolean;
  lastSync?: Date;
  errorCount: number;
  config: {
    autoCreateAlerts: boolean;
    alertThresholds: Record<string, number>;
    customLabels: Record<string, string>;
  };
}

export interface SyncResult {
  success: boolean;
  metricsCount: number;
  alertsCount: number;
  dashboardsCount: number;
  errors: string[];
  duration: number;
}

interface UseExternalMonitoringReturn {
  providers: ExternalMonitoringProvider[];
  integrations: MonitoringIntegration[];
  isLoading: boolean;
  addProvider: (provider: Omit<ExternalMonitoringProvider, 'id' | 'status' | 'lastSync'>) => Promise<ExternalMonitoringProvider>;
  updateProvider: (providerId: string, updates: Partial<ExternalMonitoringProvider>) => Promise<boolean>;
  deleteProvider: (providerId: string) => Promise<boolean>;
  testConnection: (providerId: string) => Promise<boolean>;
  syncProvider: (providerId: string) => Promise<SyncResult>;
  createIntegration: (integration: Omit<MonitoringIntegration, 'id' | 'lastSync' | 'errorCount'>) => Promise<MonitoringIntegration>;
  updateIntegration: (integrationId: string, updates: Partial<MonitoringIntegration>) => Promise<boolean>;
  deleteIntegration: (integrationId: string) => Promise<boolean>;
  getProviderMetrics: (providerId: string, timeRange?: string) => Promise<any[]>;
  getProviderAlerts: (providerId: string) => Promise<ExternalAlert[]>;
  exportConfiguration: () => Promise<void>;
  importConfiguration: (file: File) => Promise<boolean>;
}

const STORAGE_KEY = 'sem-fronteiras-external-monitoring';
const INTEGRATIONS_KEY = 'sem-fronteiras-monitoring-integrations';

const PROVIDER_TEMPLATES = {
  prometheus: {
    name: 'Prometheus',
    type: 'prometheus' as const,
    config: {
      queryPath: '/api/v1/query',
      queryRangePath: '/api/v1/query_range',
      defaultStep: '15s'
    },
    metrics: ['cpu_usage', 'memory_usage', 'disk_usage', 'network_io', 'up']
  },
  grafana: {
    name: 'Grafana',
    type: 'grafana' as const,
    config: {
      apiPath: '/api',
      orgId: 1
    },
    metrics: ['dashboard_views', 'alert_states', 'datasource_status']
  },
  datadog: {
    name: 'Datadog',
    type: 'datadog' as const,
    config: {
      apiVersion: 'v1',
      site: 'datadoghq.com'
    },
    metrics: ['system.cpu.user', 'system.mem.used', 'system.disk.used', 'system.net.bytes_sent']
  },
  elastic: {
    name: 'Elasticsearch',
    type: 'elastic' as const,
    config: {
      index: 'metricbeat-*',
      version: '7.x'
    },
    metrics: ['system.cpu.total.pct', 'system.memory.used.pct', 'system.filesystem.used.pct']
  }
};

export const useExternalMonitoring = (): UseExternalMonitoringReturn => {
  const [providers, setProviders] = useState<ExternalMonitoringProvider[]>([]);
  const [integrations, setIntegrations] = useState<MonitoringIntegration[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Carregar dados do localStorage
  useEffect(() => {
    try {
      const storedProviders = localStorage.getItem(STORAGE_KEY);
      const storedIntegrations = localStorage.getItem(INTEGRATIONS_KEY);
      
      if (storedProviders) {
        const parsed = JSON.parse(storedProviders);
        const providersWithDates = parsed.map((provider: any) => ({
          ...provider,
          lastSync: provider.lastSync ? new Date(provider.lastSync) : undefined,
          alerts: provider.alerts?.map((alert: any) => ({
            ...alert,
            timestamp: new Date(alert.timestamp)
          })) || []
        }));
        setProviders(providersWithDates);
      } else {
        // Gerar dados mock para demonstração
        generateMockProviders();
      }
      
      if (storedIntegrations) {
        const parsed = JSON.parse(storedIntegrations);
        const integrationsWithDates = parsed.map((integration: any) => ({
          ...integration,
          lastSync: integration.lastSync ? new Date(integration.lastSync) : undefined
        }));
        setIntegrations(integrationsWithDates);
      }
    } catch (error) {
      console.error('Erro ao carregar monitoramento externo:', error);
    }
  }, []);

  // Salvar no localStorage
  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(providers));
    } catch (error) {
      console.error('Erro ao salvar providers:', error);
    }
  }, [providers]);

  useEffect(() => {
    try {
      localStorage.setItem(INTEGRATIONS_KEY, JSON.stringify(integrations));
    } catch (error) {
      console.error('Erro ao salvar integrações:', error);
    }
  }, [integrations]);

  const generateMockProviders = useCallback(() => {
    const mockProviders: ExternalMonitoringProvider[] = [
      {
        id: '1',
        name: 'Prometheus Principal',
        type: 'prometheus',
        url: 'http://prometheus.empresa.com:9090',
        isActive: true,
        config: PROVIDER_TEMPLATES.prometheus.config,
        lastSync: new Date(Date.now() - 5 * 60 * 1000),
        status: 'connected',
        metrics: PROVIDER_TEMPLATES.prometheus.metrics,
        dashboards: [
          {
            id: 'dash-1',
            name: 'Sistema Overview',
            url: 'http://grafana.empresa.com/d/system-overview',
            description: 'Visão geral do sistema',
            tags: ['system', 'overview'],
            isEmbeddable: true,
            embedUrl: 'http://grafana.empresa.com/d-solo/system-overview'
          }
        ],
        alerts: [
          {
            id: 'alert-1',
            name: 'CPU Alto',
            severity: 'high',
            status: 'active',
            message: 'CPU usage above 80% for 5 minutes',
            timestamp: new Date(Date.now() - 10 * 60 * 1000),
            source: 'prometheus'
          }
        ]
      },
      {
        id: '2',
        name: 'Grafana Dashboard',
        type: 'grafana',
        url: 'http://grafana.empresa.com',
        apiKey: 'eyJrIjoiVGVzdEtleSIsIm4iOiJ0ZXN0IiwiaWQiOjF9',
        isActive: true,
        config: PROVIDER_TEMPLATES.grafana.config,
        lastSync: new Date(Date.now() - 15 * 60 * 1000),
        status: 'connected',
        metrics: PROVIDER_TEMPLATES.grafana.metrics,
        dashboards: [
          {
            id: 'dash-2',
            name: 'Network Monitoring',
            url: 'http://grafana.empresa.com/d/network-monitoring',
            description: 'Monitoramento de rede',
            tags: ['network', 'monitoring'],
            isEmbeddable: true
          }
        ],
        alerts: []
      }
    ];

    const mockIntegrations: MonitoringIntegration[] = [
      {
        id: '1',
        providerId: '1',
        serverIds: ['server-1', 'server-2'],
        metrics: ['cpu_usage', 'memory_usage'],
        syncInterval: 5,
        isActive: true,
        lastSync: new Date(Date.now() - 5 * 60 * 1000),
        errorCount: 0,
        config: {
          autoCreateAlerts: true,
          alertThresholds: {
            cpu_usage: 80,
            memory_usage: 90
          },
          customLabels: {
            environment: 'production',
            team: 'infrastructure'
          }
        }
      }
    ];

    setProviders(mockProviders);
    setIntegrations(mockIntegrations);
  }, []);

  const addProvider = useCallback(async (providerData: Omit<ExternalMonitoringProvider, 'id' | 'status' | 'lastSync'>): Promise<ExternalMonitoringProvider> => {
    setIsLoading(true);
    try {
      // Simular API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newProvider: ExternalMonitoringProvider = {
        ...providerData,
        id: Date.now().toString(),
        status: 'disconnected',
        dashboards: [],
        alerts: []
      };
      
      setProviders(prev => [newProvider, ...prev]);
      return newProvider;
    } catch (error) {
      console.error('Erro ao adicionar provider:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateProvider = useCallback(async (providerId: string, updates: Partial<ExternalMonitoringProvider>): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setProviders(prev => prev.map(provider => 
        provider.id === providerId ? { ...provider, ...updates } : provider
      ));
      
      return true;
    } catch (error) {
      console.error('Erro ao atualizar provider:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteProvider = useCallback(async (providerId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setProviders(prev => prev.filter(provider => provider.id !== providerId));
      setIntegrations(prev => prev.filter(integration => integration.providerId !== providerId));
      
      return true;
    } catch (error) {
      console.error('Erro ao deletar provider:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const testConnection = useCallback(async (providerId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Simular teste de conexão
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const success = Math.random() > 0.2; // 80% de sucesso
      
      setProviders(prev => prev.map(provider => 
        provider.id === providerId 
          ? { ...provider, status: success ? 'connected' : 'error' }
          : provider
      ));
      
      return success;
    } catch (error) {
      console.error('Erro ao testar conexão:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const syncProvider = useCallback(async (providerId: string): Promise<SyncResult> => {
    setIsLoading(true);
    try {
      const startTime = Date.now();
      
      // Simular sincronização
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const result: SyncResult = {
        success: Math.random() > 0.1, // 90% de sucesso
        metricsCount: Math.floor(Math.random() * 50) + 10,
        alertsCount: Math.floor(Math.random() * 10),
        dashboardsCount: Math.floor(Math.random() * 5) + 1,
        errors: [],
        duration: Date.now() - startTime
      };
      
      if (!result.success) {
        result.errors = ['Connection timeout', 'Invalid API key'];
      }
      
      setProviders(prev => prev.map(provider => 
        provider.id === providerId 
          ? { 
              ...provider, 
              lastSync: new Date(),
              status: result.success ? 'connected' : 'error'
            }
          : provider
      ));
      
      return result;
    } catch (error) {
      console.error('Erro ao sincronizar provider:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const createIntegration = useCallback(async (integrationData: Omit<MonitoringIntegration, 'id' | 'lastSync' | 'errorCount'>): Promise<MonitoringIntegration> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newIntegration: MonitoringIntegration = {
        ...integrationData,
        id: Date.now().toString(),
        errorCount: 0
      };
      
      setIntegrations(prev => [newIntegration, ...prev]);
      return newIntegration;
    } catch (error) {
      console.error('Erro ao criar integração:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateIntegration = useCallback(async (integrationId: string, updates: Partial<MonitoringIntegration>): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setIntegrations(prev => prev.map(integration => 
        integration.id === integrationId ? { ...integration, ...updates } : integration
      ));
      
      return true;
    } catch (error) {
      console.error('Erro ao atualizar integração:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const deleteIntegration = useCallback(async (integrationId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setIntegrations(prev => prev.filter(integration => integration.id !== integrationId));
      return true;
    } catch (error) {
      console.error('Erro ao deletar integração:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getProviderMetrics = useCallback(async (providerId: string, timeRange: string = '1h'): Promise<any[]> => {
    try {
      // Simular busca de métricas
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockMetrics = Array.from({ length: 20 }, (_, i) => ({
        timestamp: new Date(Date.now() - (20 - i) * 60 * 1000),
        cpu_usage: Math.random() * 100,
        memory_usage: Math.random() * 100,
        disk_usage: Math.random() * 100
      }));
      
      return mockMetrics;
    } catch (error) {
      console.error('Erro ao buscar métricas:', error);
      return [];
    }
  }, []);

  const getProviderAlerts = useCallback(async (providerId: string): Promise<ExternalAlert[]> => {
    try {
      const provider = providers.find(p => p.id === providerId);
      return provider?.alerts || [];
    } catch (error) {
      console.error('Erro ao buscar alertas:', error);
      return [];
    }
  }, [providers]);

  const exportConfiguration = useCallback(async (): Promise<void> => {
    try {
      const exportData = {
        exportedAt: new Date().toISOString(),
        providers: providers.map(p => ({ ...p, apiKey: '***HIDDEN***', password: '***HIDDEN***' })),
        integrations
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `monitoring-config-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao exportar configuração:', error);
      throw error;
    }
  }, [providers, integrations]);

  const importConfiguration = useCallback(async (file: File): Promise<boolean> => {
    setIsLoading(true);
    try {
      const text = await file.text();
      const data = JSON.parse(text);
      
      if (data.providers && Array.isArray(data.providers)) {
        setProviders(data.providers);
      }
      
      if (data.integrations && Array.isArray(data.integrations)) {
        setIntegrations(data.integrations);
      }
      
      return true;
    } catch (error) {
      console.error('Erro ao importar configuração:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    providers,
    integrations,
    isLoading,
    addProvider,
    updateProvider,
    deleteProvider,
    testConnection,
    syncProvider,
    createIntegration,
    updateIntegration,
    deleteIntegration,
    getProviderMetrics,
    getProviderAlerts,
    exportConfiguration,
    importConfiguration
  };
};

export { PROVIDER_TEMPLATES };
export default useExternalMonitoring;

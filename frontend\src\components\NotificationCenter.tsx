import React, { useState } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Typography,
  Box,
  Chip,
  Button,
  Divider,
  <PERSON>ert,
  <PERSON>ge,
  Tooltip,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  Close as CloseIcon,
  MarkEmailRead as MarkReadIcon,
  Delete as DeleteIcon,
  Clear as ClearIcon,
  FilterList as FilterIcon,
  Info as InfoIcon,
  CheckCircle as SuccessIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { useNotifications, Notification } from '../hooks/useNotifications';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface NotificationCenterProps {
  open: boolean;
  onClose: () => void;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({
  open,
  onClose
}) => {
  const {
    notifications,
    unreadCount,
    removeNotification,
    mark<PERSON><PERSON><PERSON>,
    markAllAsRead,
    clearAll,
    clearRead
  } = useNotifications();

  const [filter, setFilter] = useState<'all' | 'unread' | 'info' | 'success' | 'warning' | 'error'>('all');
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'all') return true;
    if (filter === 'unread') return !notification.read;
    return notification.type === filter;
  });

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <SuccessIcon color="success" />;
      case 'warning':
        return <WarningIcon color="warning" />;
      case 'error':
        return <ErrorIcon color="error" />;
      default:
        return <InfoIcon color="info" />;
    }
  };

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return 'success';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'info';
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMenuAnchor(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
  };

  const filterOptions = [
    { value: 'all', label: 'Todas', count: notifications.length },
    { value: 'unread', label: 'Não lidas', count: unreadCount },
    { value: 'info', label: 'Informações', count: notifications.filter(n => n.type === 'info').length },
    { value: 'success', label: 'Sucessos', count: notifications.filter(n => n.type === 'success').length },
    { value: 'warning', label: 'Avisos', count: notifications.filter(n => n.type === 'warning').length },
    { value: 'error', label: 'Erros', count: notifications.filter(n => n.type === 'error').length }
  ];

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: { width: { xs: '100%', sm: 400 } }
      }}
    >
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <NotificationsIcon color="primary" />
            <Typography variant="h6">Notificações</Typography>
            {unreadCount > 0 && (
              <Badge badgeContent={unreadCount} color="error" />
            )}
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Filtros">
              <IconButton size="small" onClick={handleMenuOpen}>
                <FilterIcon />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Opções">
              <IconButton size="small" onClick={handleMenuOpen}>
                <MoreVertIcon />
              </IconButton>
            </Tooltip>
            
            <IconButton size="small" onClick={onClose}>
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Filtros */}
        <Box sx={{ display: 'flex', gap: 1, mt: 2, flexWrap: 'wrap' }}>
          {filterOptions.map(option => (
            <Chip
              key={option.value}
              label={`${option.label} (${option.count})`}
              size="small"
              color={filter === option.value ? 'primary' : 'default'}
              onClick={() => setFilter(option.value as any)}
              variant={filter === option.value ? 'filled' : 'outlined'}
            />
          ))}
        </Box>
      </Box>

      {/* Menu de opções */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => { markAllAsRead(); handleMenuClose(); }}>
          <ListItemIcon><MarkReadIcon fontSize="small" /></ListItemIcon>
          Marcar todas como lidas
        </MenuItem>
        <MenuItem onClick={() => { clearRead(); handleMenuClose(); }}>
          <ListItemIcon><DeleteIcon fontSize="small" /></ListItemIcon>
          Remover lidas
        </MenuItem>
        <Divider />
        <MenuItem 
          onClick={() => { 
            if (window.confirm('Tem certeza que deseja limpar todas as notificações?')) {
              clearAll(); 
              handleMenuClose();
            }
          }}
          sx={{ color: 'error.main' }}
        >
          <ListItemIcon><ClearIcon fontSize="small" color="error" /></ListItemIcon>
          Limpar todas
        </MenuItem>
      </Menu>

      <Box sx={{ flex: 1, overflow: 'auto' }}>
        {filteredNotifications.length === 0 ? (
          <Box sx={{ p: 4, textAlign: 'center' }}>
            <NotificationsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Nenhuma notificação
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {filter === 'all' 
                ? 'Você não tem notificações no momento'
                : `Nenhuma notificação do tipo "${filterOptions.find(f => f.value === filter)?.label}"`
              }
            </Typography>
          </Box>
        ) : (
          <List>
            {filteredNotifications.map((notification, index) => (
              <React.Fragment key={notification.id}>
                <ListItem
                  button
                  onClick={() => handleNotificationClick(notification)}
                  sx={{
                    backgroundColor: notification.read ? 'transparent' : 'action.hover',
                    borderLeft: `4px solid ${
                      notification.type === 'success' ? 'success.main' :
                      notification.type === 'warning' ? 'warning.main' :
                      notification.type === 'error' ? 'error.main' : 'info.main'
                    }`
                  }}
                >
                  <ListItemIcon>
                    {getNotificationIcon(notification.type)}
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography 
                          variant="subtitle2" 
                          sx={{ fontWeight: notification.read ? 400 : 600 }}
                        >
                          {notification.title}
                        </Typography>
                        {!notification.read && (
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              backgroundColor: 'primary.main'
                            }}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {notification.message}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {format(notification.timestamp, 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                        </Typography>
                      </Box>
                    }
                  />
                  
                  <ListItemSecondaryAction>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeNotification(notification.id);
                      }}
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
                
                {/* Ações da notificação */}
                {notification.actions && notification.actions.length > 0 && (
                  <Box sx={{ px: 2, pb: 1 }}>
                    <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                      {notification.actions.map((action, actionIndex) => (
                        <Button
                          key={actionIndex}
                          size="small"
                          color={action.color || 'primary'}
                          onClick={action.action}
                        >
                          {action.label}
                        </Button>
                      ))}
                    </Box>
                  </Box>
                )}
                
                {index < filteredNotifications.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>

      {/* Ações rápidas */}
      {notifications.length > 0 && (
        <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {unreadCount > 0 && (
              <Button
                size="small"
                startIcon={<MarkReadIcon />}
                onClick={markAllAsRead}
              >
                Marcar todas como lidas
              </Button>
            )}
            
            <Button
              size="small"
              startIcon={<ClearIcon />}
              onClick={clearRead}
              disabled={notifications.filter(n => n.read).length === 0}
            >
              Limpar lidas
            </Button>
          </Box>
        </Box>
      )}
    </Drawer>
  );
};

export default NotificationCenter;

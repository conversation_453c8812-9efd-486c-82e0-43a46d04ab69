import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Grid,
  LinearProgress,
  Tooltip,
  IconButton,
  Alert
} from '@mui/material';
import {
  Wifi as ConnectedIcon,
  WifiOff as DisconnectedIcon,
  Refresh as RefreshIcon,
  Computer as ServerIcon,
  Warning as AlertIcon,
  CheckCircle as HealthyIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useRealTimeStatus, useSystemWebSocket } from '../hooks/useWebSocket';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface RealTimeStatusProps {
  compact?: boolean;
  showDetails?: boolean;
}

export const RealTimeStatus: React.FC<RealTimeStatusProps> = ({
  compact = false,
  showDetails = true
}) => {
  const { systemStatus, connectionState, isConnected } = useRealTimeStatus();
  const { reconnect } = useSystemWebSocket();

  const getConnectionIcon = () => {
    switch (connectionState) {
      case 'connected':
        return <ConnectedIcon color="success" />;
      case 'connecting':
        return <ConnectedIcon color="warning" />;
      default:
        return <DisconnectedIcon color="error" />;
    }
  };

  const getConnectionColor = () => {
    switch (connectionState) {
      case 'connected':
        return 'success';
      case 'connecting':
        return 'warning';
      default:
        return 'error';
    }
  };

  const getConnectionText = () => {
    switch (connectionState) {
      case 'connected':
        return 'Conectado';
      case 'connecting':
        return 'Conectando...';
      case 'disconnected':
        return 'Desconectado';
      default:
        return 'Erro de conexão';
    }
  };

  const serverHealthPercentage = systemStatus.totalServers > 0 
    ? (systemStatus.serversOnline / systemStatus.totalServers) * 100 
    : 0;

  const getHealthColor = () => {
    if (serverHealthPercentage >= 80) return 'success';
    if (serverHealthPercentage >= 60) return 'warning';
    return 'error';
  };

  const getHealthIcon = () => {
    if (serverHealthPercentage >= 80) return <HealthyIcon color="success" />;
    if (serverHealthPercentage >= 60) return <AlertIcon color="warning" />;
    return <ErrorIcon color="error" />;
  };

  if (compact) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <Tooltip title={`Status da conexão: ${getConnectionText()}`}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            {getConnectionIcon()}
            <Chip 
              label={getConnectionText()} 
              size="small" 
              color={getConnectionColor() as any}
              variant="outlined"
            />
          </Box>
        </Tooltip>

        {isConnected && (
          <>
            <Tooltip title={`${systemStatus.serversOnline}/${systemStatus.totalServers} servidores online`}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <ServerIcon color="action" fontSize="small" />
                <Typography variant="caption">
                  {systemStatus.serversOnline}/{systemStatus.totalServers}
                </Typography>
              </Box>
            </Tooltip>

            {systemStatus.activeAlerts > 0 && (
              <Tooltip title={`${systemStatus.activeAlerts} alertas ativos`}>
                <Chip 
                  icon={<AlertIcon />}
                  label={systemStatus.activeAlerts}
                  size="small"
                  color="warning"
                />
              </Tooltip>
            )}
          </>
        )}

        {!isConnected && (
          <Tooltip title="Tentar reconectar">
            <IconButton size="small" onClick={reconnect}>
              <RefreshIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" component="div">
            Status do Sistema
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip 
              icon={getConnectionIcon()}
              label={getConnectionText()}
              color={getConnectionColor() as any}
              size="small"
            />
            
            {!isConnected && (
              <Tooltip title="Tentar reconectar">
                <IconButton size="small" onClick={reconnect}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </Box>

        {!isConnected && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            Conexão perdida com o servidor. Algumas informações podem estar desatualizadas.
          </Alert>
        )}

        {showDetails && (
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  {getHealthIcon()}
                  <Typography variant="subtitle2">
                    Saúde dos Servidores
                  </Typography>
                </Box>
                
                <LinearProgress
                  variant="determinate"
                  value={serverHealthPercentage}
                  color={getHealthColor() as any}
                  sx={{ mb: 1 }}
                />
                
                <Typography variant="body2" color="text.secondary">
                  {systemStatus.serversOnline} de {systemStatus.totalServers} servidores online
                  {serverHealthPercentage > 0 && ` (${Math.round(serverHealthPercentage)}%)`}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <AlertIcon color={systemStatus.activeAlerts > 0 ? 'warning' : 'action'} />
                  <Typography variant="subtitle2">
                    Alertas Ativos
                  </Typography>
                </Box>
                
                <Typography variant="h4" color={systemStatus.activeAlerts > 0 ? 'warning.main' : 'text.secondary'}>
                  {systemStatus.activeAlerts}
                </Typography>
                
                <Typography variant="body2" color="text.secondary">
                  {systemStatus.activeAlerts === 0 ? 'Nenhum alerta ativo' : 'Alertas requerem atenção'}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="caption" color="text.secondary">
                Última atualização: {format(systemStatus.lastUpdate, 'dd/MM/yyyy HH:mm:ss', { locale: ptBR })}
              </Typography>
            </Grid>
          </Grid>
        )}

        {connectionState === 'connecting' && (
          <Box sx={{ mt: 2 }}>
            <LinearProgress />
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              Conectando ao servidor...
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

// Componente para exibir na barra de status
export const StatusBar: React.FC = () => {
  return (
    <Box 
      sx={{ 
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'background.paper',
        borderTop: 1,
        borderColor: 'divider',
        px: 2,
        py: 1,
        zIndex: 1000
      }}
    >
      <RealTimeStatus compact />
    </Box>
  );
};

// Hook para usar o status em outros componentes
export const useSystemHealth = () => {
  const { systemStatus, isConnected } = useRealTimeStatus();
  
  const healthPercentage = systemStatus.totalServers > 0 
    ? (systemStatus.serversOnline / systemStatus.totalServers) * 100 
    : 0;

  const healthStatus = healthPercentage >= 80 ? 'healthy' : 
                      healthPercentage >= 60 ? 'warning' : 'critical';

  return {
    ...systemStatus,
    isConnected,
    healthPercentage,
    healthStatus,
    hasAlerts: systemStatus.activeAlerts > 0,
    allServersOnline: systemStatus.serversOnline === systemStatus.totalServers && systemStatus.totalServers > 0
  };
};

export default RealTimeStatus;

# Prometheus configuration for RemoteOps monitoring

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # RemoteOps Backend instances
  - job_name: 'remoteops-backend'
    scrape_interval: 10s
    metrics_path: '/api/health/metrics'
    static_configs:
      - targets: 
        - 'remoteops-backend-1:3000'
        - 'remoteops-backend-2:3000'
        - 'remoteops-backend-3:3000'
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
      - source_labels: [__address__]
        regex: '([^:]+):.*'
        target_label: host
        replacement: '${1}'

  # RemoteOps Python microservice
  - job_name: 'remoteops-python'
    scrape_interval: 10s
    metrics_path: '/metrics'
    static_configs:
      - targets:
        - 'remoteops-python-1:8000'
        - 'remoteops-python-2:8000'

  # NGINX Load Balancer
  - job_name: 'nginx'
    scrape_interval: 30s
    static_configs:
      - targets: ['nginx:8080']
    metrics_path: '/nginx_status'

  # PostgreSQL
  - job_name: 'postgres'
    scrape_interval: 30s
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Redis
  - job_name: 'redis'
    scrape_interval: 30s
    static_configs:
      - targets: ['redis-exporter:9121']

  # Node Exporter (system metrics)
  - job_name: 'node-exporter'
    scrape_interval: 30s
    static_configs:
      - targets: 
        - 'node-exporter:9100'

  # Docker containers
  - job_name: 'docker'
    scrape_interval: 30s
    static_configs:
      - targets: ['cadvisor:8080']

  # Health checks for load balancer decisions
  - job_name: 'remoteops-health'
    scrape_interval: 5s
    metrics_path: '/api/health'
    static_configs:
      - targets:
        - 'remoteops-backend-1:3000'
        - 'remoteops-backend-2:3000'
        - 'remoteops-backend-3:3000'
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
      - source_labels: [__address__]
        regex: '([^:]+):.*'
        target_label: host
        replacement: '${1}'

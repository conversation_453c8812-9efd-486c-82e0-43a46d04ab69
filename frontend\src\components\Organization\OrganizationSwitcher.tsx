import React, { useState } from 'react'
import { ChevronDown, Building, Plus, Check, Crown, Users } from 'lucide-react'
import { useOrganization } from '../../hooks/useOrganization'
import { OrganizationService } from '../../services/organizationService'

const OrganizationSwitcher: React.FC = () => {
  const {
    currentOrganization,
    userOrganizations,
    switchOrganization,
    isLoading
  } = useOrganization()

  const [isOpen, setIsOpen] = useState(false)

  const handleSwitch = async (organizationId: string) => {
    if (organizationId !== currentOrganization?.id) {
      await switchOrganization(organizationId)
    }
    setIsOpen(false)
  }

  const getPlanBadgeColor = (planType: string) => {
    const colors = {
      STARTER: 'bg-gray-100 text-gray-800',
      PROFESSIONAL: 'bg-blue-100 text-blue-800',
      BUSINESS: 'bg-purple-100 text-purple-800',
      ENTERPRISE: 'bg-gold-100 text-gold-800'
    }
    return colors[planType as keyof typeof colors] || colors.STARTER
  }

  const getPlanIcon = (planType: string) => {
    if (planType === 'ENTERPRISE') {
      return <Crown className="h-3 w-3" />
    }
    return <Building className="h-3 w-3" />
  }

  if (!currentOrganization && userOrganizations.length === 0) {
    return (
      <div className="flex items-center space-x-2 text-sm text-gray-600">
        <Building className="h-4 w-4" />
        <span>Sem organização</span>
      </div>
    )
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isLoading}
        className="flex items-center space-x-2 px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
      >
        <Building className="h-4 w-4 text-gray-500" />
        <div className="flex flex-col items-start min-w-0">
          <span className="font-medium text-gray-900 truncate max-w-32">
            {currentOrganization?.name || 'Selecionar organização'}
          </span>
          {currentOrganization && (
            <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${getPlanBadgeColor(currentOrganization.planType)}`}>
              {getPlanIcon(currentOrganization.planType)}
              <span className="ml-1">{OrganizationService.getPlanInfo(currentOrganization.planType).name}</span>
            </span>
          )}
        </div>
        <ChevronDown className="h-4 w-4 text-gray-400" />
      </button>

      {isOpen && (
        <>
          {/* Overlay */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-20">
            <div className="p-3 border-b border-gray-200">
              <h3 className="text-sm font-medium text-gray-900">Organizações</h3>
            </div>
            
            <div className="max-h-64 overflow-y-auto">
              {userOrganizations.map((org) => (
                <button
                  key={org.id}
                  onClick={() => handleSwitch(org.id)}
                  className="w-full px-3 py-3 text-left hover:bg-gray-50 focus:outline-none focus:bg-gray-50 border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 min-w-0 flex-1">
                      <div className="flex-shrink-0">
                        {org.logo ? (
                          <img
                            src={org.logo}
                            alt={org.name}
                            className="h-8 w-8 rounded-lg object-cover"
                          />
                        ) : (
                          <div className="h-8 w-8 bg-gray-200 rounded-lg flex items-center justify-center">
                            <Building className="h-4 w-4 text-gray-500" />
                          </div>
                        )}
                      </div>
                      
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {org.name}
                          </p>
                          {currentOrganization?.id === org.id && (
                            <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                          )}
                        </div>
                        
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getPlanBadgeColor(org.planType)}`}>
                            {getPlanIcon(org.planType)}
                            <span className="ml-1">{OrganizationService.getPlanInfo(org.planType).name}</span>
                          </span>
                          
                          {OrganizationService.isInTrial(org) && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                              Trial: {OrganizationService.getTrialDaysRemaining(org.trialEndsAt)} dias
                            </span>
                          )}
                        </div>
                        
                        {org.description && (
                          <p className="text-xs text-gray-500 truncate mt-1">
                            {org.description}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
            
            <div className="p-3 border-t border-gray-200">
              <button
                onClick={() => {
                  setIsOpen(false)
                  // TODO: Abrir modal de criação de organização
                }}
                className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
              >
                <Plus className="h-4 w-4" />
                <span>Criar nova organização</span>
              </button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default OrganizationSwitcher

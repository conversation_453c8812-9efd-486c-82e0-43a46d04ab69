{{- if .Values.config.create }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "remoteops.fullname" . }}-config
  labels:
    {{- include "remoteops.labels" . | nindent 4 }}
data:
  # Application configuration
  app.json: |
    {
      "name": "{{ .Values.app.name }}",
      "version": "{{ .Values.app.version }}",
      "environment": "{{ .Values.app.environment }}",
      "features": {
        "multiTenant": true,
        "billing": false,
        "marketplace": true,
        "mobileApp": false,
        "aiAssistant": false
      },
      "monitoring": {
        "enabled": true,
        "prometheus": {
          "enabled": {{ .Values.monitoring.prometheus.enabled }},
          "endpoint": "/metrics"
        },
        "grafana": {
          "enabled": {{ .Values.monitoring.grafana.enabled }}
        }
      },
      "security": {
        "cors": {
          "enabled": true,
          "origins": ["*"]
        },
        "rateLimit": {
          "enabled": true,
          "windowMs": 900000,
          "max": 100
        }
      }
    }
  
  # NGINX configuration for sidecar proxy (if needed)
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        upstream backend {
            server localhost:3000;
        }
        
        upstream python-service {
            server localhost:8000;
        }
        
        server {
            listen 80;
            
            location /api/ {
                proxy_pass http://backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            location /python-api/ {
                proxy_pass http://python-service/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            location / {
                proxy_pass http://localhost:3001;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
        }
    }
  
  # Prometheus configuration
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    scrape_configs:
      - job_name: 'remoteops-backend'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - {{ .Release.Namespace }}
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: {{ include "remoteops.fullname" . }}-backend
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
        metrics_path: /api/health/metrics
      
      - job_name: 'remoteops-python'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - {{ .Release.Namespace }}
        relabel_configs:
          - source_labels: [__meta_kubernetes_service_name]
            action: keep
            regex: {{ include "remoteops.fullname" . }}-python
          - source_labels: [__meta_kubernetes_endpoint_port_name]
            action: keep
            regex: http
        metrics_path: /metrics
  
  # Grafana dashboard configuration
  dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "RemoteOps Dashboard",
        "tags": ["remoteops", "kubernetes"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Request Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(remoteops_requests_total[5m])",
                "legendFormat": "{{instance}}"
              }
            ]
          },
          {
            "id": 2,
            "title": "Response Time",
            "type": "graph",
            "targets": [
              {
                "expr": "remoteops_response_time_avg",
                "legendFormat": "{{instance}}"
              }
            ]
          },
          {
            "id": 3,
            "title": "Error Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "remoteops_error_rate",
                "legendFormat": "{{instance}}"
              }
            ]
          },
          {
            "id": 4,
            "title": "Memory Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "remoteops_memory_usage_bytes{type=\"used\"} / remoteops_memory_usage_bytes{type=\"total\"} * 100",
                "legendFormat": "{{instance}}"
              }
            ]
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "5s"
      }
    }
  
  {{- with .Values.config.data }}
  {{- toYaml . | nindent 2 }}
  {{- end }}
{{- end }}

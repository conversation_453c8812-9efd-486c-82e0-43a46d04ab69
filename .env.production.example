# ===========================================
# CONFIGURAÇÕES DE PRODUÇÃO - REMOTEOPS
# ===========================================

# ============================================
# CONFIGURAÇÕES GERAIS
# ============================================
NODE_ENV=production
APP_VERSION=1.0.0
LOG_LEVEL=info

# ============================================
# BANCO DE DADOS POSTGRESQL
# ============================================
POSTGRES_DB=sem_fronteiras_prod
POSTGRES_USER=sem_fronteiras_user
POSTGRES_PASSWORD=SUA_SENHA_SUPER_SEGURA_AQUI
DATABASE_URL=**************************************************************************/sem_fronteiras_prod

# ============================================
# REDIS CACHE
# ============================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=
CACHE_DEFAULT_TTL=300
CACHE_MAX_MEMORY=256mb

# ============================================
# AUTENTICAÇÃO JWT
# ============================================
JWT_SECRET=SUA_CHAVE_JWT_SUPER_SECRETA_DE_PELO_MENOS_32_CARACTERES
JWT_EXPIRES_IN=24h

# ============================================
# SERVIÇOS
# ============================================
# Backend
PORT=3001
BACKEND_URL=http://backend:3001

# Python Service
PYTHON_SERVICE_URL=http://python-service:8000

# Frontend
VITE_API_URL=http://localhost:3001

# ============================================
# SISTEMA DE BACKUP
# ============================================
BACKUP_DIR=/app/backups
AUTO_BACKUP_INTERVAL=86400000
MAX_BACKUPS=30

# ============================================
# MONITORAMENTO E ALERTAS
# ============================================
# Email para alertas (opcional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=sua-senha-de-app
ALERT_EMAIL=<EMAIL>

# Slack para alertas (opcional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/SEU/WEBHOOK/URL

# ============================================
# CONFIGURAÇÕES DE PERFORMANCE
# ============================================
# Otimização automática
PERFORMANCE_OPTIMIZATION_INTERVAL=3600000
PERFORMANCE_CLEANUP_INTERVAL=86400000

# Timeouts
DEFAULT_SSH_TIMEOUT=60
DEFAULT_KEEPALIVE_INTERVAL=30
MAX_CONCURRENT_CONNECTIONS=50

# ============================================
# CONFIGURAÇÕES DE SEGURANÇA
# ============================================
# CORS
CORS_ORIGIN=http://localhost,https://seu-dominio.com
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Session
SESSION_SECRET=SUA_CHAVE_DE_SESSAO_SUPER_SECRETA

# ============================================
# CONFIGURAÇÕES DE REDE
# ============================================
# Proxy (se aplicável)
HTTP_PROXY=
HTTPS_PROXY=
NO_PROXY=localhost,127.0.0.1

# DNS
DNS_SERVERS=*******,*******

# ============================================
# CONFIGURAÇÕES DE LOGS
# ============================================
LOG_DIR=/app/logs
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5
LOG_DATE_PATTERN=YYYY-MM-DD

# ============================================
# CONFIGURAÇÕES DE MONITORAMENTO EXTERNO
# ============================================
# Prometheus (opcional)
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090

# Grafana (opcional)
GRAFANA_PASSWORD=admin_password_seguro

# Health Check
HEALTH_CHECK_INTERVAL=30000

# ============================================
# CONFIGURAÇÕES ESPECÍFICAS DE DISPOSITIVOS
# ============================================
# Huawei/HarmonyOS
HUAWEI_DEFAULT_TIMEOUT=90
HUAWEI_DEFAULT_KEEPALIVE=10

# Mikrotik
MIKROTIK_DEFAULT_TIMEOUT=60
MIKROTIK_DEFAULT_KEEPALIVE=15
MIKROTIK_API_ENABLED=true

# Nokia
NOKIA_DEFAULT_TIMEOUT=75
NOKIA_DEFAULT_KEEPALIVE=20

# DMOS
DMOS_DEFAULT_TIMEOUT=60
DMOS_DEFAULT_KEEPALIVE=25

# ============================================
# CONFIGURAÇÕES DE DESENVOLVIMENTO (remover em produção)
# ============================================
# DEBUG=false
# ENABLE_SWAGGER=false
# ENABLE_PLAYGROUND=false

# ============================================
# CONFIGURAÇÕES DE DEPLOY
# ============================================
# Docker
COMPOSE_PROJECT_NAME=sem-fronteiras
COMPOSE_FILE=docker-compose.prod.yml

# Volumes
POSTGRES_DATA_PATH=./data/postgres
REDIS_DATA_PATH=./data/redis
BACKUP_PATH=./backups
LOGS_PATH=./logs

# ============================================
# CONFIGURAÇÕES DE SSL/TLS (se aplicável)
# ============================================
# SSL_ENABLED=true
# SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
# SSL_KEY_PATH=/etc/nginx/ssl/key.pem
# SSL_CA_PATH=/etc/nginx/ssl/ca.pem

# ============================================
# CONFIGURAÇÕES DE BACKUP EXTERNO (opcional)
# ============================================
# AWS S3
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=
# AWS_REGION=us-east-1
# S3_BUCKET_NAME=sem-fronteiras-backups

# Google Cloud Storage
# GCS_PROJECT_ID=
# GCS_BUCKET_NAME=
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json

# ============================================
# NOTAS IMPORTANTES
# ============================================
# 1. Substitua TODAS as senhas e chaves por valores seguros
# 2. Use geradores de senha para JWT_SECRET e outras chaves
# 3. Configure SSL/TLS para produção
# 4. Configure backup externo para dados críticos
# 5. Configure monitoramento e alertas
# 6. Teste todas as configurações antes do deploy
# 7. Mantenha este arquivo seguro e fora do controle de versão

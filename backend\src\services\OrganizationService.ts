import { PrismaClient, Organization, OrganizationRole, PlanType, SubscriptionStatus } from '@prisma/client'
import { Logger } from '../utils/Logger'
import crypto from 'crypto'

const prisma = new PrismaClient()

export interface CreateOrganizationDTO {
  name: string
  slug: string
  description?: string
  website?: string
  planType?: PlanType
}

export interface InviteUserDTO {
  email: string
  role: OrganizationRole
}

export interface OrganizationWithStats extends Organization {
  _count: {
    users: number
    servers: number
    serverGroups: number
    commandTemplates: number
  }
  currentUsage: {
    serversCount: number
    usersCount: number
    commandsExecuted: number
    apiCalls: number
    storageUsed: bigint
  }
}

/**
 * Serviço para gerenciamento de organizações multi-tenant
 */
export class OrganizationService {
  
  /**
   * Cria uma nova organização
   */
  static async createOrganization(ownerId: string, data: CreateOrganizationDTO): Promise<Organization> {
    try {
      // Verificar se o slug já existe
      const existingOrg = await prisma.organization.findUnique({
        where: { slug: data.slug }
      })

      if (existingOrg) {
        throw new Error('Slug da organização já está em uso')
      }

      // Definir limites baseados no plano
      const planLimits = this.getPlanLimits(data.planType || PlanType.STARTER)

      const organization = await prisma.organization.create({
        data: {
          name: data.name,
          slug: data.slug,
          description: data.description,
          website: data.website,
          ownerId,
          planType: data.planType || PlanType.STARTER,
          maxServers: planLimits.maxServers,
          maxUsers: planLimits.maxUsers,
          trialEndsAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 dias
          subscriptionStatus: SubscriptionStatus.TRIAL
        }
      })

      // Adicionar o owner como membro da organização
      await prisma.user.update({
        where: { id: ownerId },
        data: {
          organizationId: organization.id,
          organizationRole: OrganizationRole.OWNER
        }
      })

      Logger.log(`Organização ${organization.name} criada por ${ownerId}`)
      return organization
    } catch (error) {
      Logger.error('Erro ao criar organização:', error)
      throw error
    }
  }

  /**
   * Obtém organização com estatísticas
   */
  static async getOrganizationWithStats(organizationId: string): Promise<OrganizationWithStats | null> {
    try {
      const organization = await prisma.organization.findUnique({
        where: { id: organizationId },
        include: {
          _count: {
            select: {
              users: true,
              servers: true,
              serverGroups: true,
              commandTemplates: true
            }
          },
          usageMetrics: {
            orderBy: { date: 'desc' },
            take: 1
          }
        }
      })

      if (!organization) {
        return null
      }

      const currentUsage = organization.usageMetrics[0] || {
        serversCount: 0,
        usersCount: 0,
        commandsExecuted: 0,
        apiCalls: 0,
        storageUsed: BigInt(0)
      }

      return {
        ...organization,
        currentUsage
      } as OrganizationWithStats
    } catch (error) {
      Logger.error('Erro ao obter organização:', error)
      throw error
    }
  }

  /**
   * Lista organizações do usuário
   */
  static async getUserOrganizations(userId: string): Promise<Organization[]> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          organization: true,
          ownedOrganizations: true
        }
      })

      if (!user) {
        return []
      }

      const organizations = []
      
      // Organização atual do usuário
      if (user.organization) {
        organizations.push(user.organization)
      }

      // Organizações que o usuário possui
      organizations.push(...user.ownedOrganizations)

      // Remover duplicatas
      const uniqueOrgs = organizations.filter((org, index, self) => 
        index === self.findIndex(o => o.id === org.id)
      )

      return uniqueOrgs
    } catch (error) {
      Logger.error('Erro ao listar organizações do usuário:', error)
      throw error
    }
  }

  /**
   * Convida usuário para organização
   */
  static async inviteUser(organizationId: string, invitedById: string, data: InviteUserDTO): Promise<void> {
    try {
      // Verificar se o usuário tem permissão para convidar
      const inviter = await prisma.user.findFirst({
        where: {
          id: invitedById,
          organizationId,
          organizationRole: { in: [OrganizationRole.OWNER, OrganizationRole.ADMIN] }
        }
      })

      if (!inviter) {
        throw new Error('Sem permissão para convidar usuários')
      }

      // Verificar se já existe convite pendente
      const existingInvite = await prisma.organizationInvite.findUnique({
        where: {
          organizationId_email: {
            organizationId,
            email: data.email
          }
        }
      })

      if (existingInvite && !existingInvite.acceptedAt) {
        throw new Error('Convite já enviado para este email')
      }

      // Verificar se usuário já é membro
      const existingUser = await prisma.user.findFirst({
        where: {
          email: data.email,
          organizationId
        }
      })

      if (existingUser) {
        throw new Error('Usuário já é membro da organização')
      }

      // Gerar token único
      const token = crypto.randomBytes(32).toString('hex')
      const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 dias

      await prisma.organizationInvite.create({
        data: {
          email: data.email,
          role: data.role,
          token,
          expiresAt,
          organizationId,
          invitedById
        }
      })

      // TODO: Enviar email de convite
      Logger.log(`Convite enviado para ${data.email} na organização ${organizationId}`)
    } catch (error) {
      Logger.error('Erro ao convidar usuário:', error)
      throw error
    }
  }

  /**
   * Aceita convite de organização
   */
  static async acceptInvite(token: string, userId: string): Promise<Organization> {
    try {
      const invite = await prisma.organizationInvite.findUnique({
        where: { token },
        include: { organization: true }
      })

      if (!invite) {
        throw new Error('Convite não encontrado')
      }

      if (invite.acceptedAt) {
        throw new Error('Convite já foi aceito')
      }

      if (invite.expiresAt < new Date()) {
        throw new Error('Convite expirado')
      }

      // Verificar se o email do usuário corresponde
      const user = await prisma.user.findUnique({
        where: { id: userId }
      })

      if (!user || user.email !== invite.email) {
        throw new Error('Email não corresponde ao convite')
      }

      // Aceitar convite
      await prisma.$transaction([
        prisma.organizationInvite.update({
          where: { id: invite.id },
          data: { acceptedAt: new Date() }
        }),
        prisma.user.update({
          where: { id: userId },
          data: {
            organizationId: invite.organizationId,
            organizationRole: invite.role
          }
        })
      ])

      Logger.log(`Usuário ${userId} aceitou convite para organização ${invite.organizationId}`)
      return invite.organization
    } catch (error) {
      Logger.error('Erro ao aceitar convite:', error)
      throw error
    }
  }

  /**
   * Remove usuário da organização
   */
  static async removeUser(organizationId: string, userId: string, removedById: string): Promise<void> {
    try {
      // Verificar permissões
      const remover = await prisma.user.findFirst({
        where: {
          id: removedById,
          organizationId,
          organizationRole: { in: [OrganizationRole.OWNER, OrganizationRole.ADMIN] }
        }
      })

      if (!remover) {
        throw new Error('Sem permissão para remover usuários')
      }

      // Não permitir remover o owner
      const userToRemove = await prisma.user.findFirst({
        where: { id: userId, organizationId }
      })

      if (!userToRemove) {
        throw new Error('Usuário não encontrado na organização')
      }

      if (userToRemove.organizationRole === OrganizationRole.OWNER) {
        throw new Error('Não é possível remover o proprietário da organização')
      }

      // Remover usuário
      await prisma.user.update({
        where: { id: userId },
        data: {
          organizationId: null,
          organizationRole: OrganizationRole.MEMBER
        }
      })

      Logger.log(`Usuário ${userId} removido da organização ${organizationId}`)
    } catch (error) {
      Logger.error('Erro ao remover usuário:', error)
      throw error
    }
  }

  /**
   * Atualiza uso da organização
   */
  static async updateUsage(organizationId: string): Promise<void> {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      const [serversCount, usersCount] = await Promise.all([
        prisma.server.count({ where: { organizationId } }),
        prisma.user.count({ where: { organizationId } })
      ])

      await prisma.organizationUsage.upsert({
        where: {
          organizationId_date: {
            organizationId,
            date: today
          }
        },
        update: {
          serversCount,
          usersCount
        },
        create: {
          organizationId,
          date: today,
          serversCount,
          usersCount
        }
      })
    } catch (error) {
      Logger.error('Erro ao atualizar uso da organização:', error)
    }
  }

  /**
   * Verifica limites da organização
   */
  static async checkLimits(organizationId: string): Promise<{
    canAddServer: boolean
    canAddUser: boolean
    currentLimits: { servers: number; users: number; maxServers: number; maxUsers: number }
  }> {
    try {
      const org = await this.getOrganizationWithStats(organizationId)
      
      if (!org) {
        throw new Error('Organização não encontrada')
      }

      const canAddServer = org._count.servers < org.maxServers
      const canAddUser = org._count.users < org.maxUsers

      return {
        canAddServer,
        canAddUser,
        currentLimits: {
          servers: org._count.servers,
          users: org._count.users,
          maxServers: org.maxServers,
          maxUsers: org.maxUsers
        }
      }
    } catch (error) {
      Logger.error('Erro ao verificar limites:', error)
      throw error
    }
  }

  /**
   * Obtém limites do plano
   */
  private static getPlanLimits(planType: PlanType) {
    const limits = {
      [PlanType.STARTER]: { maxServers: 5, maxUsers: 1 },
      [PlanType.PROFESSIONAL]: { maxServers: 50, maxUsers: 5 },
      [PlanType.BUSINESS]: { maxServers: 200, maxUsers: 20 },
      [PlanType.ENTERPRISE]: { maxServers: -1, maxUsers: -1 } // Ilimitado
    }

    return limits[planType]
  }
}

export default OrganizationService

<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" viewBox="0 0 800 400">
  <style>
    .box { fill: white; stroke: #2563eb; stroke-width: 2; rx: 10; ry: 10; }
    .arrow { stroke: #10b981; stroke-width: 3; marker-end: url(#arrowhead); }
    .label { fill: #333; font-family: Arial; font-size: 16px; text-anchor: middle; dominant-baseline: middle; }
    .box-title { fill: #2563eb; font-family: Arial; font-size: 18px; font-weight: bold; text-anchor: middle; dominant-baseline: middle; }
  </style>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
    </marker>
  </defs>
  
  <!-- Frontend Box -->
  <rect x="50" y="150" width="150" height="100" class="box" />
  <text x="125" y="180" class="box-title">Frontend</text>
  <text x="125" y="210" class="label">React</text>
  
  <!-- Node.js API Box -->
  <rect x="325" y="150" width="150" height="100" class="box" />
  <text x="400" y="180" class="box-title">Node.js API</text>
  <text x="400" y="210" class="label">Fastify</text>
  
  <!-- PostgreSQL Box -->
  <rect x="600" y="150" width="150" height="100" class="box" />
  <text x="675" y="180" class="box-title">Banco de Dados</text>
  <text x="675" y="210" class="label">PostgreSQL</text>
  
  <!-- Python Service Box -->
  <rect x="325" y="325" width="150" height="100" class="box" />
  <text x="400" y="355" class="box-title">Microserviço</text>
  <text x="400" y="385" class="label">Python (SSH)</text>
  
  <!-- Redis Box -->
  <rect x="600" y="325" width="150" height="100" class="box" />
  <text x="675" y="355" class="box-title">Cache</text>
  <text x="675" y="385" class="label">Redis</text>
  
  <!-- Network Box -->
  <rect x="325" y="500" width="150" height="100" class="box" transform="translate(0,-100)" />
  <text x="400" y="530" class="box-title" transform="translate(0,-100)">Dispositivos</text>
  <text x="400" y="560" class="label" transform="translate(0,-100)">de Rede</text>
  
  <!-- Arrows -->
  <line x1="200" y1="200" x2="325" y2="200" class="arrow" />
  <line x1="475" y1="200" x2="600" y2="200" class="arrow" />
  <line x1="400" y1="250" x2="400" y2="325" class="arrow" />
  <line x1="475" y1="375" x2="600" y2="375" class="arrow" />
  <line x1="400" y1="425" x2="400" y2="500" class="arrow" transform="translate(0,-100)" />
</svg> 
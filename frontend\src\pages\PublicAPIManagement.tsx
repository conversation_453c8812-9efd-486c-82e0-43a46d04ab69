import React, { useState } from 'react';
import {
  Box,
  Container,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  FormControlLabel,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Switch,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
  VisibilityOff as HideIcon,
  Code as CodeIcon,
  Send as TestIcon,
  Download as ExportIcon,
  ContentCopy as CopyIcon,
  Api as ApiIcon,
  Webhook as WebhookIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import { usePublicAPI, APIKey, APIPermission, WebhookConfig } from '../hooks/usePublicAPI';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
};

const PublicAPIManagement: React.FC = () => {
  const {
    apiKeys,
    webhooks,
    stats,
    isLoading,
    createAPIKey,
    updateAPIKey,
    deleteAPIKey,
    regenerateAPIKey,
    createWebhook,
    updateWebhook,
    deleteWebhook,
    testWebhook,
    getAPIDocumentation,
    exportAPIKeys
  } = usePublicAPI();

  const [activeTab, setActiveTab] = useState(0);
  const [showCreateKeyDialog, setShowCreateKeyDialog] = useState(false);
  const [showCreateWebhookDialog, setShowCreateWebhookDialog] = useState(false);
  const [showKeyDetails, setShowKeyDetails] = useState(false);
  const [selectedKey, setSelectedKey] = useState<APIKey | null>(null);
  const [selectedWebhook, setSelectedWebhook] = useState<WebhookConfig | null>(null);
  const [showApiKeys, setShowApiKeys] = useState<Record<string, boolean>>({});

  // Formulário de nova chave API
  const [newKeyData, setNewKeyData] = useState({
    name: '',
    description: '',
    permissions: [] as APIPermission[],
    rateLimit: { requests: 1000, window: 3600 },
    ipWhitelist: [] as string[],
    expiresAt: undefined as Date | undefined
  });

  // Formulário de novo webhook
  const [newWebhookData, setNewWebhookData] = useState({
    name: '',
    url: '',
    events: [] as string[],
    secret: '',
    isActive: true,
    retryPolicy: { maxRetries: 3, backoffMultiplier: 2 }
  });

  const availableResources = ['servers', 'users', 'commands', 'templates', 'groups', 'monitoring', 'backups', 'logs'];
  const availableActions = ['read', 'write', 'delete', 'execute'];
  const availableEvents = [
    'server.created', 'server.updated', 'server.deleted', 'server.down', 'server.up',
    'user.created', 'user.updated', 'user.deleted', 'user.login', 'user.logout',
    'command.executed', 'command.failed', 'template.created', 'template.updated',
    'alert.created', 'alert.resolved', 'backup.started', 'backup.completed', 'backup.failed'
  ];

  const handleCreateKey = async () => {
    try {
      await createAPIKey({
        ...newKeyData,
        isActive: true
      });
      setShowCreateKeyDialog(false);
      setNewKeyData({
        name: '',
        description: '',
        permissions: [],
        rateLimit: { requests: 1000, window: 3600 },
        ipWhitelist: [],
        expiresAt: undefined
      });
    } catch (error) {
      console.error('Erro ao criar chave API:', error);
    }
  };

  const handleCreateWebhook = async () => {
    try {
      await createWebhook(newWebhookData);
      setShowCreateWebhookDialog(false);
      setNewWebhookData({
        name: '',
        url: '',
        events: [],
        secret: '',
        isActive: true,
        retryPolicy: { maxRetries: 3, backoffMultiplier: 2 }
      });
    } catch (error) {
      console.error('Erro ao criar webhook:', error);
    }
  };

  const handleToggleKeyVisibility = (keyId: string) => {
    setShowApiKeys(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }));
  };

  const handleCopyKey = (key: string) => {
    navigator.clipboard.writeText(key);
  };

  const handleRegenerateKey = async (keyId: string) => {
    if (window.confirm('Tem certeza que deseja regenerar esta chave? A chave atual será invalidada.')) {
      try {
        await regenerateAPIKey(keyId);
      } catch (error) {
        console.error('Erro ao regenerar chave:', error);
      }
    }
  };

  const handleTestWebhook = async (webhookId: string) => {
    try {
      const success = await testWebhook(webhookId);
      alert(success ? 'Webhook testado com sucesso!' : 'Falha no teste do webhook');
    } catch (error) {
      console.error('Erro ao testar webhook:', error);
    }
  };

  const handleViewDocumentation = async () => {
    try {
      const docs = await getAPIDocumentation();
      // Em produção, abriria uma nova aba com a documentação
      console.log('Documentação da API:', docs);
      window.open('/api/docs', '_blank');
    } catch (error) {
      console.error('Erro ao carregar documentação:', error);
    }
  };

  const formatRateLimit = (rateLimit: APIKey['rateLimit']) => {
    const hours = rateLimit.window / 3600;
    return `${rateLimit.requests} req/${hours}h`;
  };

  return (
    <Container maxWidth="xl">
      {/* Cabeçalho */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          API Pública
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Gerencie chaves de API, webhooks e integrações externas
        </Typography>
      </Box>

      {/* Estatísticas */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <ApiIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4" color="primary">
                {stats.totalKeys}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Chaves API
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <AnalyticsIcon sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h4" color="secondary">
                {stats.requestsToday.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Requests Hoje
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <SecurityIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" color="success.main">
                {stats.averageResponseTime.toFixed(0)}ms
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tempo Médio
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <WebhookIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" color="warning.main">
                {webhooks.filter(w => w.isActive).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Webhooks Ativos
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Loading */}
      {isLoading && <LinearProgress sx={{ mb: 3 }} />}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
          <Tab label="Chaves API" />
          <Tab label="Webhooks" />
          <Tab label="Documentação" />
          <Tab label="Analytics" />
        </Tabs>
      </Box>

      {/* Aba Chaves API */}
      <TabPanel value={activeTab} index={0}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">
            Chaves de API ({apiKeys.length})
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              startIcon={<ExportIcon />}
              onClick={exportAPIKeys}
              variant="outlined"
            >
              Exportar
            </Button>
            <Button
              startIcon={<CodeIcon />}
              onClick={handleViewDocumentation}
              variant="outlined"
            >
              Documentação
            </Button>
            <Button
              startIcon={<AddIcon />}
              onClick={() => setShowCreateKeyDialog(true)}
              variant="contained"
            >
              Nova Chave
            </Button>
          </Box>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Nome</TableCell>
                <TableCell>Chave</TableCell>
                <TableCell>Permissões</TableCell>
                <TableCell>Rate Limit</TableCell>
                <TableCell>Último Uso</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Ações</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {apiKeys.map((key) => (
                <TableRow key={key.id}>
                  <TableCell>
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {key.name}
                      </Typography>
                      {key.description && (
                        <Typography variant="caption" color="text.secondary">
                          {key.description}
                        </Typography>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" fontFamily="monospace">
                        {showApiKeys[key.id] ? key.key : '••••••••••••••••'}
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={() => handleToggleKeyVisibility(key.id)}
                      >
                        {showApiKeys[key.id] ? <VisibilityOff /> : <ViewIcon />}
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleCopyKey(key.key)}
                      >
                        <CopyIcon />
                      </IconButton>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                      {key.permissions.slice(0, 2).map((perm, index) => (
                        <Chip
                          key={index}
                          label={`${perm.resource}:${perm.actions.join(',')}`}
                          size="small"
                          variant="outlined"
                        />
                      ))}
                      {key.permissions.length > 2 && (
                        <Chip
                          label={`+${key.permissions.length - 2}`}
                          size="small"
                          variant="outlined"
                        />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {formatRateLimit(key.rateLimit)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    {key.lastUsed ? (
                      <Typography variant="body2">
                        {format(key.lastUsed, 'dd/MM HH:mm')}
                      </Typography>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        Nunca
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={key.isActive ? 'Ativa' : 'Inativa'}
                      color={key.isActive ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <Tooltip title="Editar">
                        <IconButton
                          size="small"
                          onClick={() => {
                            setSelectedKey(key);
                            setShowKeyDetails(true);
                          }}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Regenerar">
                        <IconButton
                          size="small"
                          onClick={() => handleRegenerateKey(key.id)}
                        >
                          <RefreshIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Excluir">
                        <IconButton
                          size="small"
                          onClick={() => {
                            if (window.confirm('Tem certeza que deseja excluir esta chave?')) {
                              deleteAPIKey(key.id);
                            }
                          }}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Aba Webhooks */}
      <TabPanel value={activeTab} index={1}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h6">
            Webhooks ({webhooks.length})
          </Typography>
          <Button
            startIcon={<AddIcon />}
            onClick={() => setShowCreateWebhookDialog(true)}
            variant="contained"
          >
            Novo Webhook
          </Button>
        </Box>

        <Grid container spacing={3}>
          {webhooks.map((webhook) => (
            <Grid item xs={12} md={6} key={webhook.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Typography variant="h6">
                      {webhook.name}
                    </Typography>
                    <Switch
                      checked={webhook.isActive}
                      onChange={(e) => updateWebhook(webhook.id, { isActive: e.target.checked })}
                    />
                  </Box>

                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {webhook.url}
                  </Typography>

                  <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mb: 2 }}>
                    {webhook.events.slice(0, 3).map((event) => (
                      <Chip key={event} label={event} size="small" variant="outlined" />
                    ))}
                    {webhook.events.length > 3 && (
                      <Chip label={`+${webhook.events.length - 3}`} size="small" variant="outlined" />
                    )}
                  </Box>

                  {webhook.lastTriggered && (
                    <Typography variant="caption" color="text.secondary">
                      Último disparo: {format(webhook.lastTriggered, 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                    </Typography>
                  )}
                </CardContent>

                <CardActions>
                  <Button
                    size="small"
                    startIcon={<TestIcon />}
                    onClick={() => handleTestWebhook(webhook.id)}
                  >
                    Testar
                  </Button>
                  <Button
                    size="small"
                    startIcon={<EditIcon />}
                    onClick={() => {
                      setSelectedWebhook(webhook);
                      // Abrir dialog de edição
                    }}
                  >
                    Editar
                  </Button>
                  <Button
                    size="small"
                    startIcon={<DeleteIcon />}
                    onClick={() => {
                      if (window.confirm('Tem certeza que deseja excluir este webhook?')) {
                        deleteWebhook(webhook.id);
                      }
                    }}
                    color="error"
                  >
                    Excluir
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      </TabPanel>

      {/* Aba Documentação */}
      <TabPanel value={activeTab} index={2}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Documentação da API
            </Typography>
            <Typography variant="body1" paragraph>
              A API do REMOTEOPS permite integração completa com sistemas externos.
            </Typography>
            
            <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
              Endpoints Principais
            </Typography>
            <List>
              <ListItem>
                <ListItemText
                  primary="GET /api/v1/servers"
                  secondary="Lista todos os servidores"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="POST /api/v1/commands/execute"
                  secondary="Executa comando em servidor específico"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="GET /api/v1/monitoring/metrics"
                  secondary="Obtém métricas de monitoramento"
                />
              </ListItem>
            </List>

            <Button
              variant="contained"
              startIcon={<CodeIcon />}
              onClick={handleViewDocumentation}
              sx={{ mt: 2 }}
            >
              Ver Documentação Completa
            </Button>
          </CardContent>
        </Card>
      </TabPanel>

      {/* Aba Analytics */}
      <TabPanel value={activeTab} index={3}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Endpoints Mais Utilizados
                </Typography>
                <List>
                  {stats.topEndpoints.map((endpoint, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={endpoint.endpoint}
                        secondary={`${endpoint.count} requests`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Uso por Chave API
                </Typography>
                <List>
                  {stats.usageByKey.map((usage, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={usage.name}
                        secondary={`${usage.requests} requests`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Dialog para criar chave API */}
      <Dialog open={showCreateKeyDialog} onClose={() => setShowCreateKeyDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Criar Nova Chave API</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Nome da Chave"
                  value={newKeyData.name}
                  onChange={(e) => setNewKeyData(prev => ({ ...prev, name: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={3}
                  label="Descrição"
                  value={newKeyData.description}
                  onChange={(e) => setNewKeyData(prev => ({ ...prev, description: e.target.value }))}
                />
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Permissões
                </Typography>
                {availableResources.map((resource) => (
                  <FormControlLabel
                    key={resource}
                    control={
                      <Checkbox
                        checked={newKeyData.permissions.some(p => p.resource === resource)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setNewKeyData(prev => ({
                              ...prev,
                              permissions: [...prev.permissions, { resource: resource as any, actions: ['read'] }]
                            }));
                          } else {
                            setNewKeyData(prev => ({
                              ...prev,
                              permissions: prev.permissions.filter(p => p.resource !== resource)
                            }));
                          }
                        }}
                      />
                    }
                    label={resource}
                  />
                ))}
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCreateKeyDialog(false)}>Cancelar</Button>
          <Button onClick={handleCreateKey} variant="contained">Criar Chave</Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para criar webhook */}
      <Dialog open={showCreateWebhookDialog} onClose={() => setShowCreateWebhookDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Criar Novo Webhook</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Nome do Webhook"
                  value={newWebhookData.name}
                  onChange={(e) => setNewWebhookData(prev => ({ ...prev, name: e.target.value }))}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="URL do Webhook"
                  value={newWebhookData.url}
                  onChange={(e) => setNewWebhookData(prev => ({ ...prev, url: e.target.value }))}
                  placeholder="https://example.com/webhook"
                />
              </Grid>

              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel>Eventos</InputLabel>
                  <Select
                    multiple
                    value={newWebhookData.events}
                    onChange={(e) => setNewWebhookData(prev => ({ ...prev, events: e.target.value as string[] }))}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => (
                          <Chip key={value} label={value} size="small" />
                        ))}
                      </Box>
                    )}
                  >
                    {availableEvents.map((event) => (
                      <MenuItem key={event} value={event}>
                        <Checkbox checked={newWebhookData.events.includes(event)} />
                        {event}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Secret (opcional)"
                  value={newWebhookData.secret}
                  onChange={(e) => setNewWebhookData(prev => ({ ...prev, secret: e.target.value }))}
                  helperText="Usado para validar a autenticidade dos webhooks"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCreateWebhookDialog(false)}>Cancelar</Button>
          <Button onClick={handleCreateWebhook} variant="contained">Criar Webhook</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default PublicAPIManagement;

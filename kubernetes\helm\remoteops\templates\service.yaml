{{- if .Values.backend.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "remoteops.fullname" . }}-backend
  labels:
    {{- include "remoteops.labels" . | nindent 4 }}
    app.kubernetes.io/component: backend
  {{- with .Values.backend.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.backend.service.type }}
  ports:
    - port: {{ .Values.backend.service.port }}
      targetPort: {{ .Values.backend.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "remoteops.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: backend
---
{{- end }}
{{- if .Values.frontend.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "remoteops.fullname" . }}-frontend
  labels:
    {{- include "remoteops.labels" . | nindent 4 }}
    app.kubernetes.io/component: frontend
  {{- with .Values.frontend.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.frontend.service.type }}
  ports:
    - port: {{ .Values.frontend.service.port }}
      targetPort: {{ .Values.frontend.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "remoteops.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: frontend
---
{{- end }}
{{- if .Values.pythonService.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "remoteops.fullname" . }}-python
  labels:
    {{- include "remoteops.labels" . | nindent 4 }}
    app.kubernetes.io/component: python-service
  {{- with .Values.pythonService.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.pythonService.service.type }}
  ports:
    - port: {{ .Values.pythonService.service.port }}
      targetPort: {{ .Values.pythonService.service.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "remoteops.selectorLabels" . | nindent 4 }}
    app.kubernetes.io/component: python-service
{{- end }}

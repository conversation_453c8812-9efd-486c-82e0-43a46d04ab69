import React from 'react'
import { 
  Building, 
  Users, 
  Server, 
  FileText, 
  Calendar,
  TrendingUp,
  AlertTriangle,
  Crown,
  Clock
} from 'lucide-react'
import { useOrganization } from '../../hooks/useOrganization'
import { OrganizationService } from '../../services/organizationService'

const OrganizationDashboard: React.FC = () => {
  const {
    currentOrganization,
    usagePercentages,
    isInTrial,
    trialDaysRemaining,
    canAddServer,
    canAddUser
  } = useOrganization()

  if (!currentOrganization) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center">
          <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhuma organização selecionada
          </h3>
          <p className="text-gray-600">
            Crie ou selecione uma organização para começar
          </p>
        </div>
      </div>
    )
  }

  const planInfo = OrganizationService.getPlanInfo(currentOrganization.planType)
  const subscriptionInfo = OrganizationService.getSubscriptionStatusInfo(currentOrganization.subscriptionStatus)

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500'
    if (percentage >= 75) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  const getUsageTextColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600'
    if (percentage >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  return (
    <div className="space-y-6">
      {/* Header da Organização */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-4">
            {currentOrganization.logo ? (
              <img
                src={currentOrganization.logo}
                alt={currentOrganization.name}
                className="h-16 w-16 rounded-lg object-cover"
              />
            ) : (
              <div className="h-16 w-16 bg-gray-200 rounded-lg flex items-center justify-center">
                <Building className="h-8 w-8 text-gray-500" />
              </div>
            )}
            
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {currentOrganization.name}
              </h1>
              {currentOrganization.description && (
                <p className="text-gray-600 mt-1">
                  {currentOrganization.description}
                </p>
              )}
              <div className="flex items-center space-x-3 mt-2">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                  currentOrganization.planType === 'ENTERPRISE' 
                    ? 'bg-gold-100 text-gold-800' 
                    : 'bg-blue-100 text-blue-800'
                }`}>
                  {currentOrganization.planType === 'ENTERPRISE' && <Crown className="h-4 w-4 mr-1" />}
                  {planInfo.name}
                </span>
                
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-${subscriptionInfo.color}-100 text-${subscriptionInfo.color}-800`}>
                  {subscriptionInfo.label}
                </span>
              </div>
            </div>
          </div>
          
          {isInTrial && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-yellow-600 mr-2" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">
                    Trial ativo
                  </p>
                  <p className="text-sm text-yellow-600">
                    {trialDaysRemaining} dias restantes
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Estatísticas de Uso */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Servidores */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Servidores</p>
              <p className="text-2xl font-bold text-gray-900">
                {currentOrganization._count.servers}
                {currentOrganization.maxServers > 0 && (
                  <span className="text-lg text-gray-500">
                    /{currentOrganization.maxServers}
                  </span>
                )}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Server className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          
          {currentOrganization.maxServers > 0 && (
            <div className="mt-4">
              <div className="flex items-center justify-between text-sm">
                <span className={getUsageTextColor(usagePercentages.servers)}>
                  {usagePercentages.servers}% usado
                </span>
                {!canAddServer && (
                  <span className="text-red-600 flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    Limite atingido
                  </span>
                )}
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className={`h-2 rounded-full ${getUsageColor(usagePercentages.servers)}`}
                  style={{ width: `${Math.min(usagePercentages.servers, 100)}%` }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Usuários */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Usuários</p>
              <p className="text-2xl font-bold text-gray-900">
                {currentOrganization._count.users}
                {currentOrganization.maxUsers > 0 && (
                  <span className="text-lg text-gray-500">
                    /{currentOrganization.maxUsers}
                  </span>
                )}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <Users className="h-6 w-6 text-green-600" />
            </div>
          </div>
          
          {currentOrganization.maxUsers > 0 && (
            <div className="mt-4">
              <div className="flex items-center justify-between text-sm">
                <span className={getUsageTextColor(usagePercentages.users)}>
                  {usagePercentages.users}% usado
                </span>
                {!canAddUser && (
                  <span className="text-red-600 flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-1" />
                    Limite atingido
                  </span>
                )}
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div
                  className={`h-2 rounded-full ${getUsageColor(usagePercentages.users)}`}
                  style={{ width: `${Math.min(usagePercentages.users, 100)}%` }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Templates */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Templates</p>
              <p className="text-2xl font-bold text-gray-900">
                {currentOrganization._count.commandTemplates}
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <FileText className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Templates criados
          </p>
        </div>

        {/* Comandos Executados */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Comandos</p>
              <p className="text-2xl font-bold text-gray-900">
                {currentOrganization.currentUsage.commandsExecuted.toLocaleString()}
              </p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <TrendingUp className="h-6 w-6 text-orange-600" />
            </div>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Este mês
          </p>
        </div>
      </div>

      {/* Informações do Plano */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Plano {planInfo.name}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Recursos inclusos:</h4>
            <ul className="space-y-1">
              {planInfo.features.map((feature, index) => (
                <li key={index} className="text-sm text-gray-600 flex items-center">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2" />
                  {feature}
                </li>
              ))}
            </ul>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Informações da conta:</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Criada em:</span>
                <span className="text-gray-900">
                  {new Date(currentOrganization.createdAt).toLocaleDateString('pt-BR')}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className={`text-${subscriptionInfo.color}-600 font-medium`}>
                  {subscriptionInfo.description}
                </span>
              </div>
              {currentOrganization.website && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Website:</span>
                  <a 
                    href={currentOrganization.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    {currentOrganization.website}
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Alertas e Avisos */}
      {(usagePercentages.servers >= 80 || usagePercentages.users >= 80 || trialDaysRemaining <= 7) && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800 mb-2">
                Atenção necessária
              </h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                {usagePercentages.servers >= 80 && (
                  <li>• Você está próximo do limite de servidores ({usagePercentages.servers}% usado)</li>
                )}
                {usagePercentages.users >= 80 && (
                  <li>• Você está próximo do limite de usuários ({usagePercentages.users}% usado)</li>
                )}
                {trialDaysRemaining <= 7 && trialDaysRemaining > 0 && (
                  <li>• Seu trial expira em {trialDaysRemaining} dias</li>
                )}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default OrganizationDashboard

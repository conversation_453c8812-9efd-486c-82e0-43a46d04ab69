import React, { useState } from 'react'
import { 
  Search, 
  Filter, 
  Star, 
  Download, 
  Heart, 
  TrendingUp,
  Clock,
  Grid,
  List,
  Plus
} from 'lucide-react'
import { useMarketplace } from '../hooks/useMarketplace'
import MarketplaceGrid from '../components/Marketplace/MarketplaceGrid'
import MarketplaceList from '../components/Marketplace/MarketplaceList'
import MarketplaceFilters from '../components/Marketplace/MarketplaceFilters'
import FeaturedTemplates from '../components/Marketplace/FeaturedTemplates'
import PublishTemplateModal from '../components/Marketplace/PublishTemplateModal'

const Marketplace: React.FC = () => {
  const {
    templates,
    featuredTemplates,
    categories,
    searchResult,
    isLoading,
    isSearching,
    filters,
    setFilters,
    searchTemplates,
    downloadTemplate,
    toggleLike,
    toggleFavorite,
    publishTemplate
  } = useMarketplace()

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [showPublishModal, setShowPublishModal] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  const handleSearch = (term: string) => {
    setSearchTerm(term)
    setFilters({ ...filters, search: term, page: 1 })
  }

  const handleFilterChange = (newFilters: any) => {
    setFilters({ ...filters, ...newFilters, page: 1 })
  }

  const handleSortChange = (sortBy: string) => {
    setFilters({ ...filters, sortBy: sortBy as any, page: 1 })
  }

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page })
  }

  const handlePublishTemplate = async (templateId: string, data: any) => {
    await publishTemplate(templateId, data)
    setShowPublishModal(false)
  }

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Marketplace de Templates</h1>
            <p className="text-gray-600 mt-1">
              Descubra, compartilhe e baixe templates de comandos criados pela comunidade
            </p>
          </div>
          
          <button
            onClick={() => setShowPublishModal(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Publicar Template
          </button>
        </div>

        {/* Search Bar */}
        <div className="flex items-center space-x-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar templates..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center px-4 py-3 border rounded-lg transition-colors ${
              showFilters 
                ? 'bg-blue-50 border-blue-300 text-blue-700' 
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filtros
          </button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Grid className="h-5 w-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Templates</p>
                <p className="text-lg font-semibold text-gray-900">
                  {searchResult?.total || templates.length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Download className="h-5 w-5 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Downloads</p>
                <p className="text-lg font-semibold text-gray-900">
                  {templates.reduce((sum, t) => sum + t.downloads, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <Heart className="h-5 w-5 text-red-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Likes</p>
                <p className="text-lg font-semibold text-gray-900">
                  {templates.reduce((sum, t) => sum + t.likes, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-4">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Star className="h-5 w-5 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Categorias</p>
                <p className="text-lg font-semibold text-gray-900">{categories.length}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Templates */}
      {featuredTemplates.length > 0 && !searchTerm && (
        <div className="mb-8">
          <FeaturedTemplates
            templates={featuredTemplates}
            onDownload={downloadTemplate}
            onLike={toggleLike}
            onFavorite={toggleFavorite}
          />
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <div className="mb-6">
          <MarketplaceFilters
            categories={categories}
            filters={filters}
            onFilterChange={handleFilterChange}
            onReset={() => setFilters({ page: 1, limit: 20, sortBy: 'popular' })}
          />
        </div>
      )}

      {/* Controls */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <select
            value={filters.sortBy}
            onChange={(e) => handleSortChange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="popular">Mais Populares</option>
            <option value="recent">Mais Recentes</option>
            <option value="downloads">Mais Baixados</option>
            <option value="rating">Melhor Avaliados</option>
          </select>
          
          {searchResult && (
            <span className="text-sm text-gray-500">
              {searchResult.total} templates encontrados
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg ${
              viewMode === 'grid' 
                ? 'bg-blue-100 text-blue-600' 
                : 'text-gray-400 hover:text-gray-600'
            }`}
          >
            <Grid className="h-4 w-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg ${
              viewMode === 'list' 
                ? 'bg-blue-100 text-blue-600' 
                : 'text-gray-400 hover:text-gray-600'
            }`}
          >
            <List className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Templates Grid/List */}
      {isSearching ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Buscando templates...</span>
        </div>
      ) : templates.length > 0 ? (
        <>
          {viewMode === 'grid' ? (
            <MarketplaceGrid
              templates={templates}
              onDownload={downloadTemplate}
              onLike={toggleLike}
              onFavorite={toggleFavorite}
              isLoading={isLoading}
            />
          ) : (
            <MarketplaceList
              templates={templates}
              onDownload={downloadTemplate}
              onLike={toggleLike}
              onFavorite={toggleFavorite}
              isLoading={isLoading}
            />
          )}
          
          {/* Pagination */}
          {searchResult && searchResult.totalPages > 1 && (
            <div className="flex items-center justify-center mt-8">
              <div className="flex items-center space-x-2">
                {[...Array(Math.min(5, searchResult.totalPages))].map((_, i) => {
                  const page = Math.max(1, searchResult.page - 2) + i
                  if (page > searchResult.totalPages) return null
                  
                  return (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-3 py-2 rounded-lg ${
                        page === searchResult.page
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                      }`}
                    >
                      {page}
                    </button>
                  )
                })}
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhum template encontrado
          </h3>
          <p className="text-gray-600">
            Tente ajustar os filtros ou termos de busca
          </p>
        </div>
      )}

      {/* Publish Template Modal */}
      {showPublishModal && (
        <PublishTemplateModal
          categories={categories}
          onPublish={handlePublishTemplate}
          onClose={() => setShowPublishModal(false)}
        />
      )}
    </div>
  )
}

export default Marketplace
